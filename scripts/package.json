{"name": "tfchain-js-scripts", "version": "2.9.3", "description": "scripts to fetch data / write data to tfchain", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "dependencies": {"@polkadot/api": "^10.7.2", "axios": "^0.25.0", "bip39": "^3.0.3", "blake": "^1.0.1", "bn.js": "^5.1.3", "ip-regex": "^4.3.0", "moment": "^2.29.1"}, "devDependencies": {"standard": "^16.0.3"}}