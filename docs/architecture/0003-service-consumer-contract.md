# 3. Service consumer contract

Date: 2022-10-17

## Status

Accepted

## Context

See [here](https://github.com/threefoldtech/tfchain/issues/445) for more details.

## Decision

It is now possible to create generic contract between two `TFChain` users (without restriction of account type) for some "away from the grid" service and bill for it.

The service consumer contract specs are described [here](../../substrate-node/pallets/pallet-smart-contract/service_consumer_contract_specs.md).
See [here](../../substrate-node/pallets/pallet-smart-contract/service_consumer_contract_flow.md) how to use this feature.