# Tfchain terminology

## Validator

A person or legal entity that supports a tfchain by running a chain validator node and has a seat in the chain DAO council to vote.

### Council member

<PERSON> is a member of the DAO council that is the governance of the tfchain.

### Validator node account

A validator runs a validator node that participates in the tfchain consensus. It has a seperate tfchain account to do so since the private key of this account needs to placed on the running validator node and for security concerns it is not advised that this is the same account as the one used for managing the validator and to express votes in the DAO.

## Validator stash account

In order to apply and be a validator a tfchain can require a certain amount of TFT's to be present in a stash account.

## Binding a stash account

In order to prove that the stash account is under control of or supports the validator the stash account needs to bind itself to a validator.
