package substrate

import (
	"math/big"
	"testing"

	"github.com/stretchr/testify/require"
)

var (
	StellarAccountAddress = "GCPVVC4MWKV7ZGQCMHHMALNWVLN2II43RC3FDLCRFCZJBAJCZHNE4VKK"
)

func TestSwapToStellar(t *testing.T) {
	cl := startLocalConnection(t)
	defer cl.Close()

	identity, err := NewIdentityFromSr25519Phrase(BobMnemonics)
	require.NoError(t, err)

	err = cl.SwapTo<PERSON>tellar(identity, StellarAccountAddress, *big.NewInt(********))
	require.NoError(t, err)
}
