{"Farm": {"version": "u32", "id": "u32", "name": "Vec<u8>", "twin_id": "u32", "pricing_policy_id": "u32", "certification": "FarmCertification", "public_ips": "Vec<PublicIP>", "dedicated_farm": "bool", "farming_policy_limits": "Option<FarmingPolicyLimit>"}, "FarmingPolicyLimit": {"farming_policy_id": "u32", "cu": "Option<u64>", "su": "Option<u64>", "end": "Option<u64>", "node_count": "Option<u32>", "node_certification": "bool"}, "PublicIP": {"ip": "Vec<u8>", "gateway": "Vec<u8>", "contract_id": "u64"}, "Entity": {"version": "u32", "id": "u32", "name": "Vec<u8>", "account_id": "AccountId", "country": "Vec<u8>", "city": "Vec<u8>"}, "Twin": {"version": "u32", "id": "u32", "account_id": "AccountId", "ip": "Vec<u8>", "entities": "Vec<EntityProof>"}, "EntityProof": {"entity_id": "u32", "signature": "Vec<u8>"}, "Node": {"version": "u32", "id": "u32", "farm_id": "u32", "twin_id": "u32", "resources": "Resources", "location": "Location", "country": "Vec<u8>", "city": "Vec<u8>", "public_config": "Option<PublicConfig>", "created": "u64", "farming_policy_id": "u32", "interfaces": "Vec<Interface>", "certification": "NodeCertification", "secure_boot": "bool", "virtualized": "bool", "serial_number": "Vec<u8>", "connection_price": "u32"}, "PublicConfig": {"ipv4": "Vec<u8>", "ipv6": "Vec<u8>", "gw4": "Vec<u8>", "gw6": "Vec<u8>", "domain": "Vec<u8>"}, "Location": {"longitude": "Vec<u8>", "latitude": "Vec<u8>"}, "Resources": {"hru": "u64", "sru": "u64", "cru": "u64", "mru": "u64"}, "Interface": {"name": "Vec<u8>", "mac": "Vec<u8>", "ips": "Vec<Vec<u8>>"}, "NodeCertification": {"_enum": ["<PERSON><PERSON>", "Certified"]}, "FarmCertification": {"_enum": ["NotCertified", "Gold"]}, "PricingPolicy": {"version": "u32", "id": "u32", "name": "Vec<u8>", "su": "Policy", "cu": "Policy", "nu": "Policy", "ipu": "Policy", "unique_name": "Policy", "domain_name": "Policy", "foundation_account": "AccountId", "certified_sales_account": "AccountId", "discount_for_dedicated_nodes": "u8"}, "Policy": {"value": "u32", "unit": "Unit"}, "Unit": {"_enum": ["Bytes", "Kilobytes", "Megabytes", "Gigabytes", "Terrabytes"]}, "Contract": {"version": " u32", "state": "ContractState", "contract_id": "u64", "twin_id": "u32", "contract_type": "ContractData"}, "ContractData": {"_enum": {"NodeContract": "NodeContract", "NameContract": "NameContract", "RentContract": "RentContract"}}, "NodeContract": {"node_id": "u32", "deployment_data": "Vec<u8>", "deployment_hash": "Vec<u8>", "public_ips": "u32", "public_ips_list": "Vec<PublicIP>"}, "NameContract": {"name": "Vec<u8>"}, "RentContract": {"node_id": "u32"}, "ContractBillingInformation": {"previous_nu_reported": "u64", "last_updated": "u64", "amount_unbilled": "u64"}, "ContractState": {"_enum": {"Created": null, "Deleted": "Cause", "GracePeriod": "u64"}}, "ContractResources": {"contract_id": "u64", "used": "Resources"}, "Cause": {"_enum": ["CanceledByUser", "OutOfFunds", "CanceledByCollective"]}, "NruConsumption": {"contract_id": "u64", "timestamp": "u64", "window": "u64", "nru": "u64"}, "Consumption": {"contract_id": "u64", "timestamp": "u64", "cru": "u64", "sru": "u64", "hru": "u64", "mru": "u64", "nru": "u64"}, "DiscountLevel": {"_enum": ["None", "<PERSON><PERSON><PERSON>", "Bronze", "Silver", "Gold"]}, "NameRegistration": {"name_registration_id": "u64", "twin_id": "u32", "name": "Vec<u8>"}, "FarmingPolicy": {"version": "u32", "id": "u32", "name": "Vec<u8>", "cu": "u32", "su": "u32", "nu": "u32", "ipv4": "u32", "minimal_uptime": "u16", "policy_created": "BlockNumber", "policy_end": "BlockNumber", "immutable": "bool", "default": "bool", "node_certification": "NodeCertification", "farm_certification": "FarmCertification"}, "ContractBill": {"contract_id": "u64", "timestamp": "u64", "discount_level": "DiscountLevel", "amount_billed": "u128"}, "PalletStorageVersion": {"_enum": ["V1", "V2", "V3"]}, "StorageVersion": {"_enum": ["V1Struct", "V2Struct", "V3Struct"]}, "Address": "MultiAddress", "LookupSource": "MultiAddress", "BalanceOf": "Balance", "Public": "[u8;32]", "U16F16": "[u8; 4]", "BufferIndex": "u32", "VestingInfo": {"locked": "Balance", "perBlock": "Balance", "startingBlock": "BlockNumber", "tft_price": "U16F16", "lastReleasedBlock": "BlockNumber"}, "StellarTransaction": {"amount": "Balance", "target": "MultiAddress"}, "MintTransaction": {"amount": "u64", "target": "AccountId", "block": "BlockNumber", "votes": "u32"}, "BurnTransaction": {"block": "BlockNumber", "amount": "u64", "target": "Vec<u8>", "signatures": "Vec<StellarSignature>", "sequence_number": "u64"}, "RefundTransaction": {"block": "BlockNumber", "amount": "u64", "target": "Vec<u8>", "tx_hash": "Vec<u8>", "signatures": "Vec<StellarSignature>", "sequence_number": "u64"}, "StellarSignature": {"signature": "Vec<u8>", "stellar_pubkey": "Vec<u8>"}, "Burn": {"target": "AccountId", "amount": "BalanceOf", "block": "BlockNumber", "message": "Vec<u8>"}, "ValueStruct": {"value": "U16F16"}, "AccountInfo": "AccountInfoWithProviders", "Keys": "SessionKeys2", "SessionKeys1": "(AccountId)", "SessionKeys2": "(AccountId, AccountId)", "TermsAndConditions": {"account_id": "AccountId", "timestamp": "u64", "document_link": "Vec<u8>", "document_hash": "Vec<u8>"}, "Validator": {"validator_node_account": "AccountId", "stash_account": "AccountId", "description": "Vec<u8>", "tf_connect_id": "Vec<u8>", "info": "Vec<u8>", "state": "ValidatorRequestState"}, "ValidatorRequestState": {"_enum": ["Created", "Approved", "Validating"]}, "ContractLock": {"amount_locked": "Balance", "lock_updated": "u64", "cycles": "u16"}, "DaoProposal": {"index": "u32", "description": "Vec<u8>", "link": "Vec<u8>"}, "DaoVotes": {"index": "u32", "threshold": "u32", "ayes": "Vec<VoteWeight>", "nayes": "Vec<VoteWeight>", "end": "BlockNumber", "vetos": "Vec<AccountId>"}, "VoteWeight": {"farm_id": "u32", "weight": "u64"}}