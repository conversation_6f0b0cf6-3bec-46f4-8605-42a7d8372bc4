name: Generate benchmark weights

on:
  workflow_dispatch:

jobs:
  generate-benchmark-weights:
    runs-on: [self-hosted, tfchainrunner01]
    container:
      image: threefolddev/tfchain:4
      env:
        DEBIAN_FRONTEND: noninteractive
        PATH: /root/.cargo/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/local/go/bin
        RUSTUP_HOME: /root/.rustup
        CARGO_HOME: /root/.cargo

    steps:
      - name: Fail if branch is main
        if: github.event_name == 'workflow_dispatch' && github.ref == 'refs/heads/development'
        run: |
          echo "This workflow should not be triggered with workflow_dispatch on main branch"
          exit 1

      - name: Checkout the repo
        uses: actions/checkout@v3

      - name: Chown user
        run: |
          chown -R $USER:$USER $GITHUB_WORKSPACE

      - name: Cache build
        uses: actions/cache@v3
        timeout-minutes: 6
        continue-on-error: true
        with:
          path: |
            /root/.cargo/bin/
            /root/.cargo/registry/index/
            /root/.cargo/registry/cache/
            /root/.cargo/git/db/
            substrate-node/target/
          key: ${{ runner.os }}-tfchain-build-cache-${{ hashFiles('**/Cargo.lock') }}
          restore-keys: ${{ runner.os }}-tfchain-build-cache

      - name: Build
        run: |
          cd substrate-node
          cargo build --profile=production --features runtime-benchmarks

      - name: Run benchmarking
        shell: bash
        run: |
          cd substrate-node
          for weights_rs_file in ./pallets/*/src/weights.rs
          do
            rm $weights_rs_file
            pal_name=$(awk -F'pallets/|/src' '{print $2}' <<< $weights_rs_file)
            ./target/production/tfchain benchmark pallet \
            --chain=dev \
            --wasm-execution=compiled \
            --pallet="$pal_name" \
            --extrinsic="*" \
            --steps=50 \
            --repeat=20 \
            --heap-pages=409 \
            --output ./pallets/"$pal_name"/src/weights.rs \
            --template ./.maintain/frame-weight-template.hbs
          done
          
      - name: Git config
        run: |
          git config --global --add safe.directory /__w/tfchain/tfchain
          git status

      - name: Commit & Push changes
        uses: actions-js/push@master
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          message: 'chore: update benchmark `weights.rs` file for all pallets'
          branch: ${{ github.ref_name }}
