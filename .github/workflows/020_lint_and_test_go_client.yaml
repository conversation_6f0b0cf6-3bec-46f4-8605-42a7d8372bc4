name: Lint Go client

on:
  push:
    paths:
      - clients/tfchain-client-go/**
  workflow_dispatch:

jobs:
  lint:
    name: lint
    runs-on: ubuntu-latest
    timeout-minutes: 5
    steps:    
      - name: Check out code into the Go module directory
        uses: actions/checkout@v4
        with:
          submodules: "true"
          sparse-checkout: clients/tfchain-client-go

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: "1.21"
          cache: false
          # cache-dependency-path: clients/tfchain-client-go/go.sum
        id: go

      - name: golangci-lint
        uses: golangci/golangci-lint-action@v6
        with:
          args: --timeout 3m --verbose
          working-directory: clients/tfchain-client-go

      - name: staticcheck
        uses: dominikh/staticcheck-action@v1
        with:
          version: "latest"
          install-go: false
          working-directory: clients/tfchain-client-go
        env:
          GO111MODULE: on

      - name: gofmt
        uses: Jerome1337/gofmt-action@v1.0.5
        with:
          gofmt-path: './clients/tfchain-client-go'
          gofmt-flags: "-l -d"