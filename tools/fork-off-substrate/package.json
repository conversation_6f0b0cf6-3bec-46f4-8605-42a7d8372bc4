{"name": "fork-off-substrate", "version": "2.9.3", "description": "This script allows bootstrapping a new substrate chain with the current state of a live chain", "main": "index.js", "scripts": {"start": "node index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/maxsam4/fork-off-substrate.git"}, "keywords": ["substrate", "polkadot", "kusama", "fork"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/maxsam4/fork-off-substrate/issues"}, "homepage": "https://github.com/maxsam4/fork-off-substrate#readme", "dependencies": {"@polkadot/api": "^6.10.1", "chalk": "^4.1.2", "cli-progress": "^3.9.1", "dotenv": "^10.0.0"}}