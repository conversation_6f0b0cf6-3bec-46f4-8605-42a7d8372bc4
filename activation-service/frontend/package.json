{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@material-ui/core": "^4.12.3", "@polkadot/api": "^6.4.2", "@polkadot/keyring": "^7.5.1", "@testing-library/jest-dom": "^5.11.4", "@testing-library/react": "^11.1.0", "@testing-library/user-event": "^12.1.10", "axios": "^0.23.0", "notistack": "^1.0.10", "react": "^17.0.2", "react-dom": "^17.0.2", "react-scripts": "4.0.3", "web-vitals": "^1.0.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build && rm -rf ../build && mv build ../", "serve": "serve build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}