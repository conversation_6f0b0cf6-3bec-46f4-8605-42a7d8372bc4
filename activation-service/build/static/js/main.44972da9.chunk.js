(this.webpackJsonpfrontend=this.webpackJsonpfrontend||[]).push([[0],{201:function(t,n,e){},202:function(t,n,e){},206:function(t,n){},207:function(t,n){},225:function(t,n){},226:function(t,n){},233:function(t,n){},235:function(t,n){},245:function(t,n){},247:function(t,n){},277:function(t,n){},279:function(t,n){},286:function(t,n){},287:function(t,n){},305:function(t,n){},327:function(t,n,e){"use strict";e.r(n);var c=e(0),a=e.n(c),i=e(16),r=e.n(i),o=(e(201),e.p+"static/media/3fold_logo.b121932e.png"),s=(e(202),e(32)),u=e.n(s),d=e(69),l=e(2),f=e(365),j=e(367),b=e(364),h=e(360),p=e(363),v=e(361),x=e(11);function O(t){var n=t.activate,e=Object(c.useState)(""),a=Object(l.a)(e,2),i=a[0],r=a[1],o=Object(c.useState)(""),s=Object(l.a)(o,2),O=s[0],m=s[1],g=function(){var t=Object(d.a)(u.a.mark((function t(){return u.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(""!==i){t.next=3;break}return m("Address not valid"),t.abrupt("return");case 3:t.prev=3,(new v.a).addFromAddress(i),t.next=12;break;case 8:return t.prev=8,t.t0=t.catch(3),m("Address not found"),t.abrupt("return");case 12:m(""),n(i);case 14:case"end":return t.stop()}}),t,null,[[3,8]])})));return function(){return t.apply(this,arguments)}}();return Object(x.jsx)("div",{children:Object(x.jsxs)("div",{style:{padding:"50px",display:"flex",flexDirection:"column",width:"90%",margin:"auto"},children:[Object(x.jsx)("span",{style:{fontSize:22,marginBottom:"1em"},children:"Enter your tfchain address to activate"}),Object(x.jsxs)(f.a,{children:[Object(x.jsx)(j.a,{htmlFor:"tfchainAddress",children:"Tfchain Address"}),Object(x.jsx)(b.a,{value:i,onChange:function(t){m(""),r(t.target.value)},id:"tfchainAddress","aria-describedby":"my-helper-text"}),Object(x.jsx)(h.a,{id:"my-helper-text",children:"Enter a tfchain address"}),O&&Object(x.jsx)("div",{children:O})]}),Object(x.jsx)(p.a,{color:"primary",variant:"contained",style:{marginTop:25},type:"submit",onClick:function(){return g()},children:"Activate"})]})})}var m=e(182),g=e.n(m),y=e(76);var A=function(){var t=Object(y.b)().enqueueSnackbar;return Object(x.jsx)("div",{className:"App",children:Object(x.jsxs)("header",{className:"App-header",children:[Object(x.jsx)("img",{src:o,className:"App-logo",alt:"logo"}),Object(x.jsx)(O,{activate:function(n){console.log(""),g.a.post("".concat("","/activation/activate"),{substrateAccountID:n}).then((function(n){t("Successfully activated account!"),console.log(n)})).catch((function(n){t("Failed to activate account"),console.log(n)}))}})]})})},k=function(t){t&&t instanceof Function&&e.e(3).then(e.bind(null,368)).then((function(n){var e=n.getCLS,c=n.getFID,a=n.getFCP,i=n.getLCP,r=n.getTTFB;e(t),c(t),a(t),i(t),r(t)}))};r.a.render(Object(x.jsx)(a.a.StrictMode,{children:Object(x.jsx)(y.a,{maxSnack:3,children:Object(x.jsx)(A,{})})}),document.getElementById("root")),k()}},[[327,1,2]]]);
//# sourceMappingURL=main.44972da9.chunk.js.map