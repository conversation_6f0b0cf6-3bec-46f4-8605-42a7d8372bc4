{"version": 3, "sources": ["3fold_logo.png", "components/deposit.js", "App.js", "reportWebVitals.js", "index.js"], "names": ["ActivateAccount", "activate", "useState", "tf<PERSON>in<PERSON><PERSON><PERSON>", "setTfchain<PERSON><PERSON><PERSON>", "TfchainAddressError", "setTfchainAddressError", "submit", "a", "Keyring", "addFromAddress", "style", "padding", "display", "flexDirection", "width", "margin", "fontSize", "marginBottom", "FormControl", "InputLabel", "htmlFor", "Input", "value", "onChange", "e", "target", "id", "aria-<PERSON><PERSON>", "FormHelperText", "<PERSON><PERSON>", "color", "variant", "marginTop", "type", "onClick", "App", "enqueueSnackbar", "useSnackbar", "className", "src", "logo", "alt", "account", "console", "log", "axios", "post", "substrateAccountID", "then", "res", "catch", "err", "reportWebVitals", "onPerfEntry", "Function", "getCLS", "getFID", "getFCP", "getLCP", "getTTFB", "ReactDOM", "render", "StrictMode", "maxSnack", "document", "getElementById"], "mappings": "kcAAe,G,OAAA,IAA0B,wC,uGCIlC,SAASA,EAAT,GAAyC,IAAbC,EAAY,EAAZA,SAAY,EACDC,mBAAS,IADR,mBACtCC,EADsC,KACtBC,EADsB,OAESF,mBAAS,IAFlB,mBAEtCG,EAFsC,KAEjBC,EAFiB,KAIvCC,EAAM,uCAAG,sBAAAC,EAAA,yDACU,KAAnBL,EADS,uBAEXG,EAAuB,qBAFZ,oCAOK,IAAIG,KACZC,eAAeP,GARZ,uDAgBXG,EAAuB,qBAhBZ,2BAoBbA,EAAuB,IAEvBL,EAASE,GAtBI,yDAAH,qDA8BZ,OACE,8BACE,sBAAKQ,MAAO,CAAEC,QAAS,OAAQC,QAAS,OAAQC,cAAe,SAAUC,MAAO,MAAOC,OAAQ,QAA/F,UACE,sBAAML,MAAO,CAAEM,SAAU,GAAIC,aAAc,OAA3C,oDACA,eAACC,EAAA,EAAD,WACE,cAACC,EAAA,EAAD,CAAYC,QAAQ,iBAApB,6BACA,cAACC,EAAA,EAAD,CACEC,MAAOpB,EACPqB,SAbyB,SAACC,GAClCnB,EAAuB,IACvBF,EAAkBqB,EAAEC,OAAOH,QAYnBI,GAAG,iBACHC,mBAAiB,mBAEnB,cAACC,EAAA,EAAD,CAAgBF,GAAG,iBAAnB,qCACCtB,GACC,8BAAMA,OAIV,cAACyB,EAAA,EAAD,CACEC,MAAM,UACNC,QAAQ,YACRrB,MAAO,CAAEsB,UAAW,IACpBC,KAAK,SACLC,QAAS,kBAAM5B,KALjB,2B,8BCtBO6B,MA1Bf,WAAiB,IACPC,EAAoBC,cAApBD,gBAeR,OACE,qBAAKE,UAAU,MAAf,SACE,yBAAQA,UAAU,aAAlB,UACE,qBAAKC,IAAKC,EAAMF,UAAU,WAAWG,IAAI,SACzC,cAAC1C,EAAD,CAAiBC,SAjBA,SAAC0C,GACtBC,QAAQC,IANmE,IAO3EC,IAAMC,KAAN,UAP2E,GAO3E,wBAAyC,CAAEC,mBAAoBL,IAC5DM,MAAK,SAAAC,GACJb,EAAgB,mCAChBO,QAAQC,IAAIK,MAEbC,OAAM,SAAAC,GACLf,EAAgB,8BAChBO,QAAQC,IAAIO,eCRLC,EAZS,SAAAC,GAClBA,GAAeA,aAAuBC,UACxC,8BAAqBN,MAAK,YAAkD,IAA/CO,EAA8C,EAA9CA,OAAQC,EAAsC,EAAtCA,OAAQC,EAA8B,EAA9BA,OAAQC,EAAsB,EAAtBA,OAAQC,EAAc,EAAdA,QAC3DJ,EAAOF,GACPG,EAAOH,GACPI,EAAOJ,GACPK,EAAOL,GACPM,EAAQN,OCAdO,IAASC,OACP,cAAC,IAAMC,WAAP,UACE,cAAC,IAAD,CAAkBC,SAAU,EAA5B,SACE,cAAC,EAAD,QAGJC,SAASC,eAAe,SAM1Bb,M", "file": "static/js/main.44972da9.chunk.js", "sourcesContent": ["export default __webpack_public_path__ + \"static/media/3fold_logo.b121932e.png\";", "import React, { useState } from 'react'\nimport { FormControl, InputLabel, Input, FormHelperText, Button } from '@material-ui/core'\nimport { Keyring } from '@polkadot/keyring'\n\nexport function ActivateAccount ({ activate }) {\n  const [tfchainAddress, setTfchainAddress] = useState('')\n  const [TfchainAddressError, setTfchainAddressError] = useState('')\n\n  const submit = async () => {\n    if (tfchainAddress === '') {\n      setTfchainAddressError('Address not valid')\n      return\n    }\n\n    try {\n      const keyRing = new Keyring()\n      keyRing.addFromAddress(tfchainAddress)\n      // TODO CHECK IF ADDRESS IS VALID\n\n      // if (!includes) {\n      // setTfchainAddressError('Address does not have a valid trustline to TFT')\n      // return\n      // }\n    } catch (error) {\n      setTfchainAddressError('Address not found')\n      return\n    }\n\n    setTfchainAddressError('')\n\n    activate(tfchainAddress)\n  }\n\n  const handleTfchainAddressChange = (e) => {\n    setTfchainAddressError('')\n    setTfchainAddress(e.target.value)\n  }\n\n  return (\n    <div>\n      <div style={{ padding: '50px', display: 'flex', flexDirection: 'column', width: '90%', margin: 'auto' }}>\n        <span style={{ fontSize: 22, marginBottom: '1em' }}>Enter your tfchain address to activate</span>\n        <FormControl>\n          <InputLabel htmlFor='tfchainAddress'>Tfchain Address</InputLabel>\n          <Input\n            value={tfchainAddress}\n            onChange={handleTfchainAddressChange}\n            id='tfchainAddress'\n            aria-describedby='my-helper-text'\n          />\n          <FormHelperText id='my-helper-text'>Enter a tfchain address</FormHelperText>\n          {TfchainAddressError && (\n            <div>{TfchainAddressError}</div>\n          )}\n        </FormControl>\n\n        <Button\n          color='primary'\n          variant='contained'\n          style={{ marginTop: 25 }}\n          type='submit'\n          onClick={() => submit()}\n        >\n          Activate\n        </Button>\n      </div>\n    </div>\n  )\n}\n", "import logo from './3fold_logo.png'\nimport './App.css'\nimport { ActivateAccount } from './components/deposit'\nimport axios from 'axios'\nimport { useSnackbar } from 'notistack'\n\nconst URL = process.env.NODE_ENV === 'development' ? 'http://localhost:3000' : ''\n\nfunction App () {\n  const { enqueueSnackbar } = useSnackbar()\n\n  const handleActivate = (account) => {\n    console.log(URL)\n    axios.post(`${URL}/activation/activate`, { substrateAccountID: account })\n      .then(res => {\n        enqueueSnackbar('Successfully activated account!')\n        console.log(res)\n      })\n      .catch(err => {\n        enqueueSnackbar('Failed to activate account')\n        console.log(err)\n      })\n  }\n\n  return (\n    <div className='App'>\n      <header className='App-header'>\n        <img src={logo} className='App-logo' alt='logo' />\n        <ActivateAccount activate={handleActivate} />\n      </header>\n    </div>\n  )\n}\n\nexport default App\n", "const reportWebVitals = onPerfEntry => {\n  if (onPerfEntry && onPerfEntry instanceof Function) {\n    import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {\n      getCLS(onPerfEntry);\n      getFID(onPerfEntry);\n      getFCP(onPerfEntry);\n      getLCP(onPerfEntry);\n      getTTFB(onPerfEntry);\n    });\n  }\n};\n\nexport default reportWebVitals;\n", "import React from 'react';\nimport ReactDOM from 'react-dom';\nimport './index.css';\nimport App from './App';\nimport reportWebVitals from './reportWebVitals';\nimport { SnackbarProvider } from 'notistack'\n\nReactDOM.render(\n  <React.StrictMode>\n    <SnackbarProvider maxSnack={3}>\n      <App />\n    </SnackbarProvider>\n  </React.StrictMode>,\n  document.getElementById('root')\n);\n\n// If you want to start measuring performance in your app, pass a function\n// to log results (for example: reportWebVitals(console.log))\n// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals\nreportWebVitals();\n"], "sourceRoot": ""}