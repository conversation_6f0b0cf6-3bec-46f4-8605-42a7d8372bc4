# Creating an Account on TFChain Devnet

## Step 1: Go to the TFChain UI

- Using the testnet public node: https://polkadot.js.org/apps/?rpc=wss%3A%2F%2Ftfchain.test.threefold.io#/accounts

Should look something like this:
![image](https://user-images.githubusercontent.com/********/*********-c34193eb-0864-4f6a-aa49-7ce66b6d72fb.png)

## Step 2: Click "Add Account" and walk to through the Q&A process

Once pressed you will see this:

![image](account_create_1.png)

- Save you mnemonic somewhere save (analogue).  When done click ```"I have saved my mnemonics safely"``` and press next.
- click on ```Advanced creation options``` and select ```"Edwards (ed25519, alternative)"```
- Enter ```name``` and ```password``` 
- Press ```Save```


## Step 3: Find your account address

On the accounts page, scroll down to your account and click on your name that you gave it. On the right, copy the address under your name.

## Step 4: Activate it

Go to https://tfchain.test.threefold.io/activation/ , paste the address in the input field and click `Activate`

Your account should now be activated on Tfchain testnet.