const SUBSTRATE_ERRORS = [
  'NoneValue',
  'StorageOverflow',
  'CannotCreateNode',
  'NodeNotExists',
  'NodeWithPubkeyExists',
  'CannotDeleteNode',
  'NodeDeleteNotAuthorized',
  'FarmExists',
  'FarmNotExists',
  'CannotCreateFarmWrongTwin',
  'CannotUpdateFarmWrongTwin',
  'CannotDeleteFarm',
  'CannotDeleteFarmWrongTwin',
  'IpExists',
  'IpNotExists',
  'EntityWithNameExists',
  'EntityWithPubkeyExists',
  'EntityNotExists',
  'EntitySignatureDoesNotMatch',
  'EntityWithSignatureAlreadyExists',
  'CannotUpdateEntity',
  'CannotDeleteEntity',
  'SignatureLenghtIsIncorrect',
  'TwinExists',
  'TwinNotExists',
  'TwinWithPubkeyExists',
  'CannotCreateTwin',
  'UnauthorizedToUpdateTwin',
  'PricingPolicyExists',
  'CertificationCodeExists',
  'OffchainSignedTxError',
  'NoLocalAcctForSigning'
]

module.exports = SUBSTRATE_ERRORS
