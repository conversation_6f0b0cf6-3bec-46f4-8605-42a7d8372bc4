apiVersion: v1
kind: Service
metadata:
  name: {{ include "tfchainactivationservice.fullname" . }}
  labels:
    {{- include "tfchainactivationservice.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: 3000
      protocol: TCP
      name: http
  selector:
    {{- include "tfchainactivationservice.selectorLabels" . | nindent 4 }}
