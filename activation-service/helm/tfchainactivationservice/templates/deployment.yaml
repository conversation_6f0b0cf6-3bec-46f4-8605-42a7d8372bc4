apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "tfchainactivationservice.fullname" . }}
  labels:
    {{- include "tfchainactivationservice.labels" . | nindent 4 }}
spec:
  selector:
    matchLabels:
      {{- include "tfchainactivationservice.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "tfchainactivationservice.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          env:
          - name: URL
            value: {{ .Values.url }}
          - name: MNEMONIC
            value: {{ .Values.mnemonic }}
          - name: KYC_PUBLIC_KEY
            value: {{ .Values.kyc_public_key }}
          - name: ACTIVATION_AMOUNT
            value: {{ .Values.activation_amount | quote }}
          ports:
            - name: http
              containerPort: 3000
              protocol: TCP
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
