[package]
authors.workspace = true
description = "Shared traits and structs for tfchain runtimes"
documentation.workspace = true
edition.workspace = true
homepage.workspace = true
license-file.workspace = true
name = "tfchain-support"
readme.workspace = true
repository.workspace = true
version.workspace = true

[package.metadata.docs.rs]
targets = ["x86_64-unknown-linux-gnu"]

[dependencies]
parity-scale-codec = {workspace = true, features = ["derive"]}
scale-info = { workspace = true, features = ["derive"] }
frame-support.workspace = true
frame-system.workspace = true
sp-runtime.workspace = true
sp-std.workspace = true
valip = "0.4.0"

[features]
default = ["std"]
std = [
  "parity-scale-codec/std",
  "frame-support/std",
  "frame-system/std",
  "sp-runtime/std",
  "sp-std/std",
  "scale-info/std"
]