
//! Autogenerated weights for pallet_smart_contract
//!
//! THIS FILE WAS AUTO-GENERATED USING THE SUBSTRATE BENCHMARK CLI VERSION 4.0.0-dev
//! DATE: 2024-10-01, STEPS: `50`, REPEAT: `20`, LOW RANGE: `[]`, HIGH RANGE: `[]`
//! WORST CASE MAP SIZE: `1000000`
//! HOSTNAME: `585a9f003813`, CPU: `AMD Ryzen 7 5800X 8-Core Processor`
//! EXECUTION: , WASM-EXECUTION: Compiled, CHAIN: Some("dev"), DB CACHE: 1024

// Executed Command:
// ./target/production/tfchain
// benchmark
// pallet
// --chain=dev
// --wasm-execution=compiled
// --pallet=pallet-smart-contract
// --extrinsic=*
// --steps=50
// --repeat=20
// --heap-pages=409
// --output
// ./pallets/pallet-smart-contract/src/weights.rs
// --template
// ./.maintain/frame-weight-template.hbs

#![cfg_attr(rustfmt, rustfmt_skip)]
#![allow(unused_parens)]
#![allow(unused_imports)]
#![allow(missing_docs)]

use frame_support::{traits::Get, weights::{Weight, constants::RocksDbWeight}};
use core::marker::PhantomData;

/// Weight functions needed for pallet_smart_contract.
pub trait WeightInfo {
	fn create_node_contract() -> Weight;
	fn update_node_contract() -> Weight;
	fn cancel_contract() -> Weight;
	fn create_name_contract() -> Weight;
	fn cancel_name_contract() -> Weight;
	fn add_nru_reports() -> Weight;
	fn report_contract_resources() -> Weight;
	fn create_rent_contract() -> Weight;
	fn cancel_rent_contract() -> Weight;
	fn create_solution_provider() -> Weight;
	fn approve_solution_provider() -> Weight;
	fn bill_contract_for_block() -> Weight;
	fn service_contract_create() -> Weight;
	fn service_contract_set_metadata() -> Weight;
	fn service_contract_set_fees() -> Weight;
	fn service_contract_approve() -> Weight;
	fn service_contract_reject() -> Weight;
	fn service_contract_cancel() -> Weight;
	fn service_contract_bill() -> Weight;
	fn change_billing_frequency() -> Weight;
	fn attach_solution_provider_id() -> Weight;
	fn set_dedicated_node_extra_fee() -> Weight;
	fn cancel_contract_collective() -> Weight;
}

/// Weights for pallet_smart_contract using the Substrate node and recommended hardware.
pub struct SubstrateWeight<T>(PhantomData<T>);
impl<T: frame_system::Config> WeightInfo for SubstrateWeight<T> {
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Nodes` (r:1 w:0)
	/// Proof: `TfgridModule::Nodes` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::NodePower` (r:1 w:0)
	/// Proof: `TfgridModule::NodePower` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Farms` (r:1 w:1)
	/// Proof: `TfgridModule::Farms` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::DedicatedNodesExtraFee` (r:1 w:0)
	/// Proof: `SmartContractModule::DedicatedNodesExtraFee` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ActiveRentContractForNode` (r:1 w:0)
	/// Proof: `SmartContractModule::ActiveRentContractForNode` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractIDByNodeIDAndHash` (r:1 w:1)
	/// Proof: `SmartContractModule::ContractIDByNodeIDAndHash` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractID` (r:1 w:1)
	/// Proof: `SmartContractModule::ContractID` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::BillingFrequency` (r:1 w:0)
	/// Proof: `SmartContractModule::BillingFrequency` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractsToBillAt` (r:1 w:1)
	/// Proof: `SmartContractModule::ContractsToBillAt` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `Timestamp::Now` (r:1 w:0)
	/// Proof: `Timestamp::Now` (`max_values`: Some(1), `max_size`: Some(8), added: 503, mode: `MaxEncodedLen`)
	/// Storage: `SmartContractModule::ActiveNodeContracts` (r:1 w:1)
	/// Proof: `SmartContractModule::ActiveNodeContracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::Contracts` (r:0 w:1)
	/// Proof: `SmartContractModule::Contracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractBillingInformationByID` (r:0 w:1)
	/// Proof: `SmartContractModule::ContractBillingInformationByID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractPaymentState` (r:0 w:1)
	/// Proof: `SmartContractModule::ContractPaymentState` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn create_node_contract() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `868`
		//  Estimated: `4333`
		// Minimum execution time: 48_471_000 picoseconds.
		Weight::from_parts(49_253_000, 4333)
			.saturating_add(T::DbWeight::get().reads(12_u64))
			.saturating_add(T::DbWeight::get().writes(8_u64))
	}
	/// Storage: `SmartContractModule::Contracts` (r:1 w:1)
	/// Proof: `SmartContractModule::Contracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Twins` (r:1 w:0)
	/// Proof: `TfgridModule::Twins` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractIDByNodeIDAndHash` (r:0 w:2)
	/// Proof: `SmartContractModule::ContractIDByNodeIDAndHash` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn update_node_contract() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `869`
		//  Estimated: `4334`
		// Minimum execution time: 23_585_000 picoseconds.
		Weight::from_parts(24_016_000, 4334)
			.saturating_add(T::DbWeight::get().reads(2_u64))
			.saturating_add(T::DbWeight::get().writes(3_u64))
	}
	/// Storage: `SmartContractModule::Contracts` (r:1 w:1)
	/// Proof: `SmartContractModule::Contracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Twins` (r:2 w:0)
	/// Proof: `TfgridModule::Twins` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ActiveNodeContracts` (r:1 w:1)
	/// Proof: `SmartContractModule::ActiveNodeContracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::PricingPolicies` (r:1 w:0)
	/// Proof: `TfgridModule::PricingPolicies` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Nodes` (r:1 w:0)
	/// Proof: `TfgridModule::Nodes` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Farms` (r:1 w:0)
	/// Proof: `TfgridModule::Farms` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractPaymentState` (r:1 w:1)
	/// Proof: `SmartContractModule::ContractPaymentState` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::TwinBoundedAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinBoundedAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `Timestamp::Now` (r:1 w:0)
	/// Proof: `Timestamp::Now` (`max_values`: Some(1), `max_size`: Some(8), added: 503, mode: `MaxEncodedLen`)
	/// Storage: `SmartContractModule::ActiveRentContractForNode` (r:1 w:0)
	/// Proof: `SmartContractModule::ActiveRentContractForNode` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::BillingFrequency` (r:1 w:0)
	/// Proof: `SmartContractModule::BillingFrequency` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractsToBillAt` (r:1 w:1)
	/// Proof: `SmartContractModule::ContractsToBillAt` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractBillingInformationByID` (r:0 w:1)
	/// Proof: `SmartContractModule::ContractBillingInformationByID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::NodeContractResources` (r:0 w:1)
	/// Proof: `SmartContractModule::NodeContractResources` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractIDByNodeIDAndHash` (r:0 w:1)
	/// Proof: `SmartContractModule::ContractIDByNodeIDAndHash` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn cancel_contract() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `1650`
		//  Estimated: `7590`
		// Minimum execution time: 77_096_000 picoseconds.
		Weight::from_parts(78_488_000, 7590)
			.saturating_add(T::DbWeight::get().reads(13_u64))
			.saturating_add(T::DbWeight::get().writes(7_u64))
	}
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractIDByNameRegistration` (r:1 w:1)
	/// Proof: `SmartContractModule::ContractIDByNameRegistration` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractID` (r:1 w:1)
	/// Proof: `SmartContractModule::ContractID` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::BillingFrequency` (r:1 w:0)
	/// Proof: `SmartContractModule::BillingFrequency` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractsToBillAt` (r:1 w:1)
	/// Proof: `SmartContractModule::ContractsToBillAt` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `Timestamp::Now` (r:1 w:0)
	/// Proof: `Timestamp::Now` (`max_values`: Some(1), `max_size`: Some(8), added: 503, mode: `MaxEncodedLen`)
	/// Storage: `SmartContractModule::Contracts` (r:0 w:1)
	/// Proof: `SmartContractModule::Contracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractPaymentState` (r:0 w:1)
	/// Proof: `SmartContractModule::ContractPaymentState` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn create_name_contract() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `340`
		//  Estimated: `3805`
		// Minimum execution time: 24_797_000 picoseconds.
		Weight::from_parts(25_157_000, 3805)
			.saturating_add(T::DbWeight::get().reads(6_u64))
			.saturating_add(T::DbWeight::get().writes(5_u64))
	}
	/// Storage: `SmartContractModule::Contracts` (r:1 w:1)
	/// Proof: `SmartContractModule::Contracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Twins` (r:1 w:0)
	/// Proof: `TfgridModule::Twins` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::PricingPolicies` (r:1 w:0)
	/// Proof: `TfgridModule::PricingPolicies` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractPaymentState` (r:1 w:1)
	/// Proof: `SmartContractModule::ContractPaymentState` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::TwinBoundedAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinBoundedAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `Timestamp::Now` (r:1 w:0)
	/// Proof: `Timestamp::Now` (`max_values`: Some(1), `max_size`: Some(8), added: 503, mode: `MaxEncodedLen`)
	/// Storage: `SmartContractModule::BillingFrequency` (r:1 w:0)
	/// Proof: `SmartContractModule::BillingFrequency` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractsToBillAt` (r:1 w:1)
	/// Proof: `SmartContractModule::ContractsToBillAt` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractIDByNameRegistration` (r:0 w:1)
	/// Proof: `SmartContractModule::ContractIDByNameRegistration` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn cancel_name_contract() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `949`
		//  Estimated: `4414`
		// Minimum execution time: 51_929_000 picoseconds.
		Weight::from_parts(53_511_000, 4414)
			.saturating_add(T::DbWeight::get().reads(8_u64))
			.saturating_add(T::DbWeight::get().writes(4_u64))
	}
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::NodeIdByTwinID` (r:1 w:0)
	/// Proof: `TfgridModule::NodeIdByTwinID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Nodes` (r:1 w:0)
	/// Proof: `TfgridModule::Nodes` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Farms` (r:1 w:0)
	/// Proof: `TfgridModule::Farms` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::PricingPolicies` (r:1 w:0)
	/// Proof: `TfgridModule::PricingPolicies` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::Contracts` (r:1 w:0)
	/// Proof: `SmartContractModule::Contracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractBillingInformationByID` (r:1 w:1)
	/// Proof: `SmartContractModule::ContractBillingInformationByID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn add_nru_reports() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `1286`
		//  Estimated: `4751`
		// Minimum execution time: 38_173_000 picoseconds.
		Weight::from_parts(38_663_000, 4751)
			.saturating_add(T::DbWeight::get().reads(7_u64))
			.saturating_add(T::DbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::NodeIdByTwinID` (r:1 w:0)
	/// Proof: `TfgridModule::NodeIdByTwinID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::Contracts` (r:1 w:0)
	/// Proof: `SmartContractModule::Contracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::NodeContractResources` (r:0 w:1)
	/// Proof: `SmartContractModule::NodeContractResources` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn report_contract_resources() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `765`
		//  Estimated: `4230`
		// Minimum execution time: 23_385_000 picoseconds.
		Weight::from_parts(23_876_000, 4230)
			.saturating_add(T::DbWeight::get().reads(3_u64))
			.saturating_add(T::DbWeight::get().writes(1_u64))
	}
	/// Storage: `SmartContractModule::ActiveRentContractForNode` (r:1 w:1)
	/// Proof: `SmartContractModule::ActiveRentContractForNode` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Nodes` (r:1 w:0)
	/// Proof: `TfgridModule::Nodes` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Farms` (r:1 w:0)
	/// Proof: `TfgridModule::Farms` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ActiveNodeContracts` (r:1 w:0)
	/// Proof: `SmartContractModule::ActiveNodeContracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractID` (r:1 w:1)
	/// Proof: `SmartContractModule::ContractID` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::BillingFrequency` (r:1 w:0)
	/// Proof: `SmartContractModule::BillingFrequency` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractsToBillAt` (r:1 w:1)
	/// Proof: `SmartContractModule::ContractsToBillAt` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `Timestamp::Now` (r:1 w:0)
	/// Proof: `Timestamp::Now` (`max_values`: Some(1), `max_size`: Some(8), added: 503, mode: `MaxEncodedLen`)
	/// Storage: `SmartContractModule::Contracts` (r:0 w:1)
	/// Proof: `SmartContractModule::Contracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractPaymentState` (r:0 w:1)
	/// Proof: `SmartContractModule::ContractPaymentState` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn create_rent_contract() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `776`
		//  Estimated: `4241`
		// Minimum execution time: 34_906_000 picoseconds.
		Weight::from_parts(35_367_000, 4241)
			.saturating_add(T::DbWeight::get().reads(9_u64))
			.saturating_add(T::DbWeight::get().writes(5_u64))
	}
	/// Storage: `SmartContractModule::Contracts` (r:1 w:1)
	/// Proof: `SmartContractModule::Contracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Twins` (r:2 w:0)
	/// Proof: `TfgridModule::Twins` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ActiveNodeContracts` (r:1 w:0)
	/// Proof: `SmartContractModule::ActiveNodeContracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::PricingPolicies` (r:1 w:0)
	/// Proof: `TfgridModule::PricingPolicies` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Nodes` (r:1 w:0)
	/// Proof: `TfgridModule::Nodes` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Farms` (r:1 w:0)
	/// Proof: `TfgridModule::Farms` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractPaymentState` (r:1 w:1)
	/// Proof: `SmartContractModule::ContractPaymentState` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::TwinBoundedAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinBoundedAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `Timestamp::Now` (r:1 w:0)
	/// Proof: `Timestamp::Now` (`max_values`: Some(1), `max_size`: Some(8), added: 503, mode: `MaxEncodedLen`)
	/// Storage: `TfgridModule::NodePower` (r:1 w:0)
	/// Proof: `TfgridModule::NodePower` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::DedicatedNodesExtraFee` (r:1 w:0)
	/// Proof: `SmartContractModule::DedicatedNodesExtraFee` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::BillingFrequency` (r:1 w:0)
	/// Proof: `SmartContractModule::BillingFrequency` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractsToBillAt` (r:1 w:1)
	/// Proof: `SmartContractModule::ContractsToBillAt` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ActiveRentContractForNode` (r:0 w:1)
	/// Proof: `SmartContractModule::ActiveRentContractForNode` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn cancel_rent_contract() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `1607`
		//  Estimated: `7547`
		// Minimum execution time: 72_007_000 picoseconds.
		Weight::from_parts(73_199_000, 7547)
			.saturating_add(T::DbWeight::get().reads(14_u64))
			.saturating_add(T::DbWeight::get().writes(4_u64))
	}
	/// Storage: `SmartContractModule::SolutionProviderID` (r:1 w:1)
	/// Proof: `SmartContractModule::SolutionProviderID` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::SolutionProviders` (r:0 w:1)
	/// Proof: `SmartContractModule::SolutionProviders` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn create_solution_provider() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `37`
		//  Estimated: `1522`
		// Minimum execution time: 9_798_000 picoseconds.
		Weight::from_parts(10_129_000, 1522)
			.saturating_add(T::DbWeight::get().reads(1_u64))
			.saturating_add(T::DbWeight::get().writes(2_u64))
	}
	/// Storage: `SmartContractModule::SolutionProviders` (r:1 w:1)
	/// Proof: `SmartContractModule::SolutionProviders` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn approve_solution_provider() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `215`
		//  Estimated: `3680`
		// Minimum execution time: 12_794_000 picoseconds.
		Weight::from_parts(12_955_000, 3680)
			.saturating_add(T::DbWeight::get().reads(1_u64))
			.saturating_add(T::DbWeight::get().writes(1_u64))
	}
	/// Storage: `SmartContractModule::Contracts` (r:1 w:0)
	/// Proof: `SmartContractModule::Contracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Twins` (r:2 w:0)
	/// Proof: `TfgridModule::Twins` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::PricingPolicies` (r:1 w:0)
	/// Proof: `TfgridModule::PricingPolicies` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Nodes` (r:1 w:0)
	/// Proof: `TfgridModule::Nodes` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Farms` (r:1 w:0)
	/// Proof: `TfgridModule::Farms` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractPaymentState` (r:1 w:1)
	/// Proof: `SmartContractModule::ContractPaymentState` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `System::Account` (r:1 w:1)
	/// Proof: `System::Account` (`max_values`: None, `max_size`: Some(128), added: 2603, mode: `MaxEncodedLen`)
	/// Storage: `TfgridModule::TwinBoundedAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinBoundedAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `Timestamp::Now` (r:1 w:0)
	/// Proof: `Timestamp::Now` (`max_values`: Some(1), `max_size`: Some(8), added: 503, mode: `MaxEncodedLen`)
	/// Storage: `SmartContractModule::ContractBillingInformationByID` (r:1 w:1)
	/// Proof: `SmartContractModule::ContractBillingInformationByID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::NodeContractResources` (r:1 w:0)
	/// Proof: `SmartContractModule::NodeContractResources` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ActiveRentContractForNode` (r:1 w:0)
	/// Proof: `SmartContractModule::ActiveRentContractForNode` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TFTPriceModule::AverageTftPrice` (r:1 w:0)
	/// Proof: `TFTPriceModule::AverageTftPrice` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `TFTPriceModule::MinTftPrice` (r:1 w:0)
	/// Proof: `TFTPriceModule::MinTftPrice` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `TFTPriceModule::MaxTftPrice` (r:1 w:0)
	/// Proof: `TFTPriceModule::MaxTftPrice` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `Session::Validators` (r:1 w:0)
	/// Proof: `Session::Validators` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	fn bill_contract_for_block() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `2077`
		//  Estimated: `8017`
		// Minimum execution time: 87_206_000 picoseconds.
		Weight::from_parts(88_408_000, 8017)
			.saturating_add(T::DbWeight::get().reads(17_u64))
			.saturating_add(T::DbWeight::get().writes(3_u64))
	}
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:2 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ServiceContractID` (r:1 w:1)
	/// Proof: `SmartContractModule::ServiceContractID` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ServiceContracts` (r:0 w:1)
	/// Proof: `SmartContractModule::ServiceContracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn service_contract_create() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `395`
		//  Estimated: `6335`
		// Minimum execution time: 18_244_000 picoseconds.
		Weight::from_parts(18_726_000, 6335)
			.saturating_add(T::DbWeight::get().reads(3_u64))
			.saturating_add(T::DbWeight::get().writes(2_u64))
	}
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ServiceContracts` (r:1 w:1)
	/// Proof: `SmartContractModule::ServiceContracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn service_contract_set_metadata() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `489`
		//  Estimated: `3954`
		// Minimum execution time: 16_522_000 picoseconds.
		Weight::from_parts(16_892_000, 3954)
			.saturating_add(T::DbWeight::get().reads(2_u64))
			.saturating_add(T::DbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ServiceContracts` (r:1 w:1)
	/// Proof: `SmartContractModule::ServiceContracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn service_contract_set_fees() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `489`
		//  Estimated: `3954`
		// Minimum execution time: 16_201_000 picoseconds.
		Weight::from_parts(16_482_000, 3954)
			.saturating_add(T::DbWeight::get().reads(2_u64))
			.saturating_add(T::DbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ServiceContracts` (r:1 w:1)
	/// Proof: `SmartContractModule::ServiceContracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn service_contract_approve() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `502`
		//  Estimated: `3967`
		// Minimum execution time: 16_461_000 picoseconds.
		Weight::from_parts(16_982_000, 3967)
			.saturating_add(T::DbWeight::get().reads(2_u64))
			.saturating_add(T::DbWeight::get().writes(1_u64))
	}
	/// Storage: `SmartContractModule::ServiceContracts` (r:1 w:1)
	/// Proof: `SmartContractModule::ServiceContracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn service_contract_reject() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `502`
		//  Estimated: `3967`
		// Minimum execution time: 17_202_000 picoseconds.
		Weight::from_parts(17_463_000, 3967)
			.saturating_add(T::DbWeight::get().reads(2_u64))
			.saturating_add(T::DbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ServiceContracts` (r:1 w:1)
	/// Proof: `SmartContractModule::ServiceContracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn service_contract_cancel() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `502`
		//  Estimated: `3967`
		// Minimum execution time: 15_760_000 picoseconds.
		Weight::from_parts(16_351_000, 3967)
			.saturating_add(T::DbWeight::get().reads(2_u64))
			.saturating_add(T::DbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ServiceContracts` (r:1 w:1)
	/// Proof: `SmartContractModule::ServiceContracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `Timestamp::Now` (r:1 w:0)
	/// Proof: `Timestamp::Now` (`max_values`: Some(1), `max_size`: Some(8), added: 503, mode: `MaxEncodedLen`)
	/// Storage: `TfgridModule::Twins` (r:2 w:0)
	/// Proof: `TfgridModule::Twins` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `System::Account` (r:1 w:0)
	/// Proof: `System::Account` (`max_values`: None, `max_size`: Some(128), added: 2603, mode: `MaxEncodedLen`)
	fn service_contract_bill() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `841`
		//  Estimated: `6781`
		// Minimum execution time: 28_654_000 picoseconds.
		Weight::from_parts(29_275_000, 6781)
			.saturating_add(T::DbWeight::get().reads(6_u64))
			.saturating_add(T::DbWeight::get().writes(1_u64))
	}
	/// Storage: `SmartContractModule::BillingFrequency` (r:1 w:1)
	/// Proof: `SmartContractModule::BillingFrequency` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	fn change_billing_frequency() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `37`
		//  Estimated: `1522`
		// Minimum execution time: 7_474_000 picoseconds.
		Weight::from_parts(7_865_000, 1522)
			.saturating_add(T::DbWeight::get().reads(1_u64))
			.saturating_add(T::DbWeight::get().writes(1_u64))
	}
	/// Storage: `SmartContractModule::SolutionProviders` (r:1 w:0)
	/// Proof: `SmartContractModule::SolutionProviders` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::Contracts` (r:1 w:1)
	/// Proof: `SmartContractModule::Contracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn attach_solution_provider_id() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `965`
		//  Estimated: `4430`
		// Minimum execution time: 21_992_000 picoseconds.
		Weight::from_parts(22_443_000, 4430)
			.saturating_add(T::DbWeight::get().reads(3_u64))
			.saturating_add(T::DbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Nodes` (r:1 w:0)
	/// Proof: `TfgridModule::Nodes` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Farms` (r:1 w:0)
	/// Proof: `TfgridModule::Farms` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ActiveNodeContracts` (r:1 w:0)
	/// Proof: `SmartContractModule::ActiveNodeContracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ActiveRentContractForNode` (r:1 w:0)
	/// Proof: `SmartContractModule::ActiveRentContractForNode` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::DedicatedNodesExtraFee` (r:0 w:1)
	/// Proof: `SmartContractModule::DedicatedNodesExtraFee` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn set_dedicated_node_extra_fee() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `733`
		//  Estimated: `4198`
		// Minimum execution time: 23_004_000 picoseconds.
		Weight::from_parts(23_434_000, 4198)
			.saturating_add(T::DbWeight::get().reads(5_u64))
			.saturating_add(T::DbWeight::get().writes(1_u64))
	}
	/// Storage: `SmartContractModule::Contracts` (r:1 w:1)
	/// Proof: `SmartContractModule::Contracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ActiveNodeContracts` (r:1 w:1)
	/// Proof: `SmartContractModule::ActiveNodeContracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Twins` (r:2 w:0)
	/// Proof: `TfgridModule::Twins` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::PricingPolicies` (r:1 w:0)
	/// Proof: `TfgridModule::PricingPolicies` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Nodes` (r:1 w:0)
	/// Proof: `TfgridModule::Nodes` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Farms` (r:1 w:0)
	/// Proof: `TfgridModule::Farms` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractPaymentState` (r:1 w:1)
	/// Proof: `SmartContractModule::ContractPaymentState` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::TwinBoundedAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinBoundedAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `Timestamp::Now` (r:1 w:0)
	/// Proof: `Timestamp::Now` (`max_values`: Some(1), `max_size`: Some(8), added: 503, mode: `MaxEncodedLen`)
	/// Storage: `SmartContractModule::ActiveRentContractForNode` (r:1 w:0)
	/// Proof: `SmartContractModule::ActiveRentContractForNode` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::BillingFrequency` (r:1 w:0)
	/// Proof: `SmartContractModule::BillingFrequency` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractsToBillAt` (r:1 w:1)
	/// Proof: `SmartContractModule::ContractsToBillAt` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractBillingInformationByID` (r:0 w:1)
	/// Proof: `SmartContractModule::ContractBillingInformationByID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::NodeContractResources` (r:0 w:1)
	/// Proof: `SmartContractModule::NodeContractResources` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractIDByNodeIDAndHash` (r:0 w:1)
	/// Proof: `SmartContractModule::ContractIDByNodeIDAndHash` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn cancel_contract_collective() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `1650`
		//  Estimated: `7590`
		// Minimum execution time: 74_531_000 picoseconds.
		Weight::from_parts(75_944_000, 7590)
			.saturating_add(T::DbWeight::get().reads(13_u64))
			.saturating_add(T::DbWeight::get().writes(7_u64))
	}
}

// For backwards compatibility and tests
impl WeightInfo for () {
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Nodes` (r:1 w:0)
	/// Proof: `TfgridModule::Nodes` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::NodePower` (r:1 w:0)
	/// Proof: `TfgridModule::NodePower` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Farms` (r:1 w:1)
	/// Proof: `TfgridModule::Farms` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::DedicatedNodesExtraFee` (r:1 w:0)
	/// Proof: `SmartContractModule::DedicatedNodesExtraFee` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ActiveRentContractForNode` (r:1 w:0)
	/// Proof: `SmartContractModule::ActiveRentContractForNode` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractIDByNodeIDAndHash` (r:1 w:1)
	/// Proof: `SmartContractModule::ContractIDByNodeIDAndHash` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractID` (r:1 w:1)
	/// Proof: `SmartContractModule::ContractID` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::BillingFrequency` (r:1 w:0)
	/// Proof: `SmartContractModule::BillingFrequency` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractsToBillAt` (r:1 w:1)
	/// Proof: `SmartContractModule::ContractsToBillAt` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `Timestamp::Now` (r:1 w:0)
	/// Proof: `Timestamp::Now` (`max_values`: Some(1), `max_size`: Some(8), added: 503, mode: `MaxEncodedLen`)
	/// Storage: `SmartContractModule::ActiveNodeContracts` (r:1 w:1)
	/// Proof: `SmartContractModule::ActiveNodeContracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::Contracts` (r:0 w:1)
	/// Proof: `SmartContractModule::Contracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractBillingInformationByID` (r:0 w:1)
	/// Proof: `SmartContractModule::ContractBillingInformationByID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractPaymentState` (r:0 w:1)
	/// Proof: `SmartContractModule::ContractPaymentState` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn create_node_contract() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `868`
		//  Estimated: `4333`
		// Minimum execution time: 48_471_000 picoseconds.
		Weight::from_parts(49_253_000, 4333)
			.saturating_add(RocksDbWeight::get().reads(12_u64))
			.saturating_add(RocksDbWeight::get().writes(8_u64))
	}
	/// Storage: `SmartContractModule::Contracts` (r:1 w:1)
	/// Proof: `SmartContractModule::Contracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Twins` (r:1 w:0)
	/// Proof: `TfgridModule::Twins` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractIDByNodeIDAndHash` (r:0 w:2)
	/// Proof: `SmartContractModule::ContractIDByNodeIDAndHash` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn update_node_contract() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `869`
		//  Estimated: `4334`
		// Minimum execution time: 23_585_000 picoseconds.
		Weight::from_parts(24_016_000, 4334)
			.saturating_add(RocksDbWeight::get().reads(2_u64))
			.saturating_add(RocksDbWeight::get().writes(3_u64))
	}
	/// Storage: `SmartContractModule::Contracts` (r:1 w:1)
	/// Proof: `SmartContractModule::Contracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Twins` (r:2 w:0)
	/// Proof: `TfgridModule::Twins` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ActiveNodeContracts` (r:1 w:1)
	/// Proof: `SmartContractModule::ActiveNodeContracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::PricingPolicies` (r:1 w:0)
	/// Proof: `TfgridModule::PricingPolicies` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Nodes` (r:1 w:0)
	/// Proof: `TfgridModule::Nodes` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Farms` (r:1 w:0)
	/// Proof: `TfgridModule::Farms` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractPaymentState` (r:1 w:1)
	/// Proof: `SmartContractModule::ContractPaymentState` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::TwinBoundedAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinBoundedAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `Timestamp::Now` (r:1 w:0)
	/// Proof: `Timestamp::Now` (`max_values`: Some(1), `max_size`: Some(8), added: 503, mode: `MaxEncodedLen`)
	/// Storage: `SmartContractModule::ActiveRentContractForNode` (r:1 w:0)
	/// Proof: `SmartContractModule::ActiveRentContractForNode` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::BillingFrequency` (r:1 w:0)
	/// Proof: `SmartContractModule::BillingFrequency` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractsToBillAt` (r:1 w:1)
	/// Proof: `SmartContractModule::ContractsToBillAt` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractBillingInformationByID` (r:0 w:1)
	/// Proof: `SmartContractModule::ContractBillingInformationByID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::NodeContractResources` (r:0 w:1)
	/// Proof: `SmartContractModule::NodeContractResources` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractIDByNodeIDAndHash` (r:0 w:1)
	/// Proof: `SmartContractModule::ContractIDByNodeIDAndHash` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn cancel_contract() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `1650`
		//  Estimated: `7590`
		// Minimum execution time: 77_096_000 picoseconds.
		Weight::from_parts(78_488_000, 7590)
			.saturating_add(RocksDbWeight::get().reads(13_u64))
			.saturating_add(RocksDbWeight::get().writes(7_u64))
	}
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractIDByNameRegistration` (r:1 w:1)
	/// Proof: `SmartContractModule::ContractIDByNameRegistration` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractID` (r:1 w:1)
	/// Proof: `SmartContractModule::ContractID` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::BillingFrequency` (r:1 w:0)
	/// Proof: `SmartContractModule::BillingFrequency` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractsToBillAt` (r:1 w:1)
	/// Proof: `SmartContractModule::ContractsToBillAt` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `Timestamp::Now` (r:1 w:0)
	/// Proof: `Timestamp::Now` (`max_values`: Some(1), `max_size`: Some(8), added: 503, mode: `MaxEncodedLen`)
	/// Storage: `SmartContractModule::Contracts` (r:0 w:1)
	/// Proof: `SmartContractModule::Contracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractPaymentState` (r:0 w:1)
	/// Proof: `SmartContractModule::ContractPaymentState` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn create_name_contract() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `340`
		//  Estimated: `3805`
		// Minimum execution time: 24_797_000 picoseconds.
		Weight::from_parts(25_157_000, 3805)
			.saturating_add(RocksDbWeight::get().reads(6_u64))
			.saturating_add(RocksDbWeight::get().writes(5_u64))
	}
	/// Storage: `SmartContractModule::Contracts` (r:1 w:1)
	/// Proof: `SmartContractModule::Contracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Twins` (r:1 w:0)
	/// Proof: `TfgridModule::Twins` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::PricingPolicies` (r:1 w:0)
	/// Proof: `TfgridModule::PricingPolicies` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractPaymentState` (r:1 w:1)
	/// Proof: `SmartContractModule::ContractPaymentState` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::TwinBoundedAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinBoundedAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `Timestamp::Now` (r:1 w:0)
	/// Proof: `Timestamp::Now` (`max_values`: Some(1), `max_size`: Some(8), added: 503, mode: `MaxEncodedLen`)
	/// Storage: `SmartContractModule::BillingFrequency` (r:1 w:0)
	/// Proof: `SmartContractModule::BillingFrequency` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractsToBillAt` (r:1 w:1)
	/// Proof: `SmartContractModule::ContractsToBillAt` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractIDByNameRegistration` (r:0 w:1)
	/// Proof: `SmartContractModule::ContractIDByNameRegistration` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn cancel_name_contract() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `949`
		//  Estimated: `4414`
		// Minimum execution time: 51_929_000 picoseconds.
		Weight::from_parts(53_511_000, 4414)
			.saturating_add(RocksDbWeight::get().reads(8_u64))
			.saturating_add(RocksDbWeight::get().writes(4_u64))
	}
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::NodeIdByTwinID` (r:1 w:0)
	/// Proof: `TfgridModule::NodeIdByTwinID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Nodes` (r:1 w:0)
	/// Proof: `TfgridModule::Nodes` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Farms` (r:1 w:0)
	/// Proof: `TfgridModule::Farms` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::PricingPolicies` (r:1 w:0)
	/// Proof: `TfgridModule::PricingPolicies` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::Contracts` (r:1 w:0)
	/// Proof: `SmartContractModule::Contracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractBillingInformationByID` (r:1 w:1)
	/// Proof: `SmartContractModule::ContractBillingInformationByID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn add_nru_reports() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `1286`
		//  Estimated: `4751`
		// Minimum execution time: 38_173_000 picoseconds.
		Weight::from_parts(38_663_000, 4751)
			.saturating_add(RocksDbWeight::get().reads(7_u64))
			.saturating_add(RocksDbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::NodeIdByTwinID` (r:1 w:0)
	/// Proof: `TfgridModule::NodeIdByTwinID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::Contracts` (r:1 w:0)
	/// Proof: `SmartContractModule::Contracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::NodeContractResources` (r:0 w:1)
	/// Proof: `SmartContractModule::NodeContractResources` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn report_contract_resources() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `765`
		//  Estimated: `4230`
		// Minimum execution time: 23_385_000 picoseconds.
		Weight::from_parts(23_876_000, 4230)
			.saturating_add(RocksDbWeight::get().reads(3_u64))
			.saturating_add(RocksDbWeight::get().writes(1_u64))
	}
	/// Storage: `SmartContractModule::ActiveRentContractForNode` (r:1 w:1)
	/// Proof: `SmartContractModule::ActiveRentContractForNode` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Nodes` (r:1 w:0)
	/// Proof: `TfgridModule::Nodes` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Farms` (r:1 w:0)
	/// Proof: `TfgridModule::Farms` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ActiveNodeContracts` (r:1 w:0)
	/// Proof: `SmartContractModule::ActiveNodeContracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractID` (r:1 w:1)
	/// Proof: `SmartContractModule::ContractID` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::BillingFrequency` (r:1 w:0)
	/// Proof: `SmartContractModule::BillingFrequency` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractsToBillAt` (r:1 w:1)
	/// Proof: `SmartContractModule::ContractsToBillAt` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `Timestamp::Now` (r:1 w:0)
	/// Proof: `Timestamp::Now` (`max_values`: Some(1), `max_size`: Some(8), added: 503, mode: `MaxEncodedLen`)
	/// Storage: `SmartContractModule::Contracts` (r:0 w:1)
	/// Proof: `SmartContractModule::Contracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractPaymentState` (r:0 w:1)
	/// Proof: `SmartContractModule::ContractPaymentState` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn create_rent_contract() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `776`
		//  Estimated: `4241`
		// Minimum execution time: 34_906_000 picoseconds.
		Weight::from_parts(35_367_000, 4241)
			.saturating_add(RocksDbWeight::get().reads(9_u64))
			.saturating_add(RocksDbWeight::get().writes(5_u64))
	}
	/// Storage: `SmartContractModule::Contracts` (r:1 w:1)
	/// Proof: `SmartContractModule::Contracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Twins` (r:2 w:0)
	/// Proof: `TfgridModule::Twins` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ActiveNodeContracts` (r:1 w:0)
	/// Proof: `SmartContractModule::ActiveNodeContracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::PricingPolicies` (r:1 w:0)
	/// Proof: `TfgridModule::PricingPolicies` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Nodes` (r:1 w:0)
	/// Proof: `TfgridModule::Nodes` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Farms` (r:1 w:0)
	/// Proof: `TfgridModule::Farms` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractPaymentState` (r:1 w:1)
	/// Proof: `SmartContractModule::ContractPaymentState` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::TwinBoundedAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinBoundedAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `Timestamp::Now` (r:1 w:0)
	/// Proof: `Timestamp::Now` (`max_values`: Some(1), `max_size`: Some(8), added: 503, mode: `MaxEncodedLen`)
	/// Storage: `TfgridModule::NodePower` (r:1 w:0)
	/// Proof: `TfgridModule::NodePower` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::DedicatedNodesExtraFee` (r:1 w:0)
	/// Proof: `SmartContractModule::DedicatedNodesExtraFee` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::BillingFrequency` (r:1 w:0)
	/// Proof: `SmartContractModule::BillingFrequency` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractsToBillAt` (r:1 w:1)
	/// Proof: `SmartContractModule::ContractsToBillAt` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ActiveRentContractForNode` (r:0 w:1)
	/// Proof: `SmartContractModule::ActiveRentContractForNode` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn cancel_rent_contract() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `1607`
		//  Estimated: `7547`
		// Minimum execution time: 72_007_000 picoseconds.
		Weight::from_parts(73_199_000, 7547)
			.saturating_add(RocksDbWeight::get().reads(14_u64))
			.saturating_add(RocksDbWeight::get().writes(4_u64))
	}
	/// Storage: `SmartContractModule::SolutionProviderID` (r:1 w:1)
	/// Proof: `SmartContractModule::SolutionProviderID` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::SolutionProviders` (r:0 w:1)
	/// Proof: `SmartContractModule::SolutionProviders` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn create_solution_provider() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `37`
		//  Estimated: `1522`
		// Minimum execution time: 9_798_000 picoseconds.
		Weight::from_parts(10_129_000, 1522)
			.saturating_add(RocksDbWeight::get().reads(1_u64))
			.saturating_add(RocksDbWeight::get().writes(2_u64))
	}
	/// Storage: `SmartContractModule::SolutionProviders` (r:1 w:1)
	/// Proof: `SmartContractModule::SolutionProviders` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn approve_solution_provider() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `215`
		//  Estimated: `3680`
		// Minimum execution time: 12_794_000 picoseconds.
		Weight::from_parts(12_955_000, 3680)
			.saturating_add(RocksDbWeight::get().reads(1_u64))
			.saturating_add(RocksDbWeight::get().writes(1_u64))
	}
	/// Storage: `SmartContractModule::Contracts` (r:1 w:0)
	/// Proof: `SmartContractModule::Contracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Twins` (r:2 w:0)
	/// Proof: `TfgridModule::Twins` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::PricingPolicies` (r:1 w:0)
	/// Proof: `TfgridModule::PricingPolicies` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Nodes` (r:1 w:0)
	/// Proof: `TfgridModule::Nodes` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Farms` (r:1 w:0)
	/// Proof: `TfgridModule::Farms` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractPaymentState` (r:1 w:1)
	/// Proof: `SmartContractModule::ContractPaymentState` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `System::Account` (r:1 w:1)
	/// Proof: `System::Account` (`max_values`: None, `max_size`: Some(128), added: 2603, mode: `MaxEncodedLen`)
	/// Storage: `TfgridModule::TwinBoundedAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinBoundedAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `Timestamp::Now` (r:1 w:0)
	/// Proof: `Timestamp::Now` (`max_values`: Some(1), `max_size`: Some(8), added: 503, mode: `MaxEncodedLen`)
	/// Storage: `SmartContractModule::ContractBillingInformationByID` (r:1 w:1)
	/// Proof: `SmartContractModule::ContractBillingInformationByID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::NodeContractResources` (r:1 w:0)
	/// Proof: `SmartContractModule::NodeContractResources` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ActiveRentContractForNode` (r:1 w:0)
	/// Proof: `SmartContractModule::ActiveRentContractForNode` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TFTPriceModule::AverageTftPrice` (r:1 w:0)
	/// Proof: `TFTPriceModule::AverageTftPrice` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `TFTPriceModule::MinTftPrice` (r:1 w:0)
	/// Proof: `TFTPriceModule::MinTftPrice` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `TFTPriceModule::MaxTftPrice` (r:1 w:0)
	/// Proof: `TFTPriceModule::MaxTftPrice` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `Session::Validators` (r:1 w:0)
	/// Proof: `Session::Validators` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	fn bill_contract_for_block() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `2077`
		//  Estimated: `8017`
		// Minimum execution time: 87_206_000 picoseconds.
		Weight::from_parts(88_408_000, 8017)
			.saturating_add(RocksDbWeight::get().reads(17_u64))
			.saturating_add(RocksDbWeight::get().writes(3_u64))
	}
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:2 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ServiceContractID` (r:1 w:1)
	/// Proof: `SmartContractModule::ServiceContractID` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ServiceContracts` (r:0 w:1)
	/// Proof: `SmartContractModule::ServiceContracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn service_contract_create() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `395`
		//  Estimated: `6335`
		// Minimum execution time: 18_244_000 picoseconds.
		Weight::from_parts(18_726_000, 6335)
			.saturating_add(RocksDbWeight::get().reads(3_u64))
			.saturating_add(RocksDbWeight::get().writes(2_u64))
	}
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ServiceContracts` (r:1 w:1)
	/// Proof: `SmartContractModule::ServiceContracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn service_contract_set_metadata() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `489`
		//  Estimated: `3954`
		// Minimum execution time: 16_522_000 picoseconds.
		Weight::from_parts(16_892_000, 3954)
			.saturating_add(RocksDbWeight::get().reads(2_u64))
			.saturating_add(RocksDbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ServiceContracts` (r:1 w:1)
	/// Proof: `SmartContractModule::ServiceContracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn service_contract_set_fees() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `489`
		//  Estimated: `3954`
		// Minimum execution time: 16_201_000 picoseconds.
		Weight::from_parts(16_482_000, 3954)
			.saturating_add(RocksDbWeight::get().reads(2_u64))
			.saturating_add(RocksDbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ServiceContracts` (r:1 w:1)
	/// Proof: `SmartContractModule::ServiceContracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn service_contract_approve() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `502`
		//  Estimated: `3967`
		// Minimum execution time: 16_461_000 picoseconds.
		Weight::from_parts(16_982_000, 3967)
			.saturating_add(RocksDbWeight::get().reads(2_u64))
			.saturating_add(RocksDbWeight::get().writes(1_u64))
	}
	/// Storage: `SmartContractModule::ServiceContracts` (r:1 w:1)
	/// Proof: `SmartContractModule::ServiceContracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn service_contract_reject() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `502`
		//  Estimated: `3967`
		// Minimum execution time: 17_202_000 picoseconds.
		Weight::from_parts(17_463_000, 3967)
			.saturating_add(RocksDbWeight::get().reads(2_u64))
			.saturating_add(RocksDbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ServiceContracts` (r:1 w:1)
	/// Proof: `SmartContractModule::ServiceContracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn service_contract_cancel() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `502`
		//  Estimated: `3967`
		// Minimum execution time: 15_760_000 picoseconds.
		Weight::from_parts(16_351_000, 3967)
			.saturating_add(RocksDbWeight::get().reads(2_u64))
			.saturating_add(RocksDbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ServiceContracts` (r:1 w:1)
	/// Proof: `SmartContractModule::ServiceContracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `Timestamp::Now` (r:1 w:0)
	/// Proof: `Timestamp::Now` (`max_values`: Some(1), `max_size`: Some(8), added: 503, mode: `MaxEncodedLen`)
	/// Storage: `TfgridModule::Twins` (r:2 w:0)
	/// Proof: `TfgridModule::Twins` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `System::Account` (r:1 w:0)
	/// Proof: `System::Account` (`max_values`: None, `max_size`: Some(128), added: 2603, mode: `MaxEncodedLen`)
	fn service_contract_bill() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `841`
		//  Estimated: `6781`
		// Minimum execution time: 28_654_000 picoseconds.
		Weight::from_parts(29_275_000, 6781)
			.saturating_add(RocksDbWeight::get().reads(6_u64))
			.saturating_add(RocksDbWeight::get().writes(1_u64))
	}
	/// Storage: `SmartContractModule::BillingFrequency` (r:1 w:1)
	/// Proof: `SmartContractModule::BillingFrequency` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	fn change_billing_frequency() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `37`
		//  Estimated: `1522`
		// Minimum execution time: 7_474_000 picoseconds.
		Weight::from_parts(7_865_000, 1522)
			.saturating_add(RocksDbWeight::get().reads(1_u64))
			.saturating_add(RocksDbWeight::get().writes(1_u64))
	}
	/// Storage: `SmartContractModule::SolutionProviders` (r:1 w:0)
	/// Proof: `SmartContractModule::SolutionProviders` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::Contracts` (r:1 w:1)
	/// Proof: `SmartContractModule::Contracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn attach_solution_provider_id() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `965`
		//  Estimated: `4430`
		// Minimum execution time: 21_992_000 picoseconds.
		Weight::from_parts(22_443_000, 4430)
			.saturating_add(RocksDbWeight::get().reads(3_u64))
			.saturating_add(RocksDbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Nodes` (r:1 w:0)
	/// Proof: `TfgridModule::Nodes` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Farms` (r:1 w:0)
	/// Proof: `TfgridModule::Farms` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ActiveNodeContracts` (r:1 w:0)
	/// Proof: `SmartContractModule::ActiveNodeContracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ActiveRentContractForNode` (r:1 w:0)
	/// Proof: `SmartContractModule::ActiveRentContractForNode` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::DedicatedNodesExtraFee` (r:0 w:1)
	/// Proof: `SmartContractModule::DedicatedNodesExtraFee` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn set_dedicated_node_extra_fee() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `733`
		//  Estimated: `4198`
		// Minimum execution time: 23_004_000 picoseconds.
		Weight::from_parts(23_434_000, 4198)
			.saturating_add(RocksDbWeight::get().reads(5_u64))
			.saturating_add(RocksDbWeight::get().writes(1_u64))
	}
	/// Storage: `SmartContractModule::Contracts` (r:1 w:1)
	/// Proof: `SmartContractModule::Contracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ActiveNodeContracts` (r:1 w:1)
	/// Proof: `SmartContractModule::ActiveNodeContracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Twins` (r:2 w:0)
	/// Proof: `TfgridModule::Twins` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::PricingPolicies` (r:1 w:0)
	/// Proof: `TfgridModule::PricingPolicies` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Nodes` (r:1 w:0)
	/// Proof: `TfgridModule::Nodes` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Farms` (r:1 w:0)
	/// Proof: `TfgridModule::Farms` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractPaymentState` (r:1 w:1)
	/// Proof: `SmartContractModule::ContractPaymentState` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::TwinBoundedAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinBoundedAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `Timestamp::Now` (r:1 w:0)
	/// Proof: `Timestamp::Now` (`max_values`: Some(1), `max_size`: Some(8), added: 503, mode: `MaxEncodedLen`)
	/// Storage: `SmartContractModule::ActiveRentContractForNode` (r:1 w:0)
	/// Proof: `SmartContractModule::ActiveRentContractForNode` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::BillingFrequency` (r:1 w:0)
	/// Proof: `SmartContractModule::BillingFrequency` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractsToBillAt` (r:1 w:1)
	/// Proof: `SmartContractModule::ContractsToBillAt` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractBillingInformationByID` (r:0 w:1)
	/// Proof: `SmartContractModule::ContractBillingInformationByID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::NodeContractResources` (r:0 w:1)
	/// Proof: `SmartContractModule::NodeContractResources` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ContractIDByNodeIDAndHash` (r:0 w:1)
	/// Proof: `SmartContractModule::ContractIDByNodeIDAndHash` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn cancel_contract_collective() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `1650`
		//  Estimated: `7590`
		// Minimum execution time: 74_531_000 picoseconds.
		Weight::from_parts(75_944_000, 7590)
			.saturating_add(RocksDbWeight::get().reads(13_u64))
			.saturating_add(RocksDbWeight::get().writes(7_u64))
	}
}
