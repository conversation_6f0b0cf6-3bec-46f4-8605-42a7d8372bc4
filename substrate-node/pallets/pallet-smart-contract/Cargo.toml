[package]
authors.workspace = true
documentation.workspace = true
edition.workspace = true
homepage.workspace = true
license-file.workspace = true
readme.workspace = true
repository.workspace = true
version.workspace = true
name = "pallet-smart-contract"
description = "Contracts on tfchain"

[package.metadata.docs.rs]
targets = ['x86_64-unknown-linux-gnu']

[dependencies]
log.workspace = true
parity-scale-codec = {workspace = true, features = ["derive"]}
scale-info = { workspace = true, features = ["derive"] }
parking_lot.workspace = true

# Support
tfchain-support.workspace = true

# Custom pallets
pallet-tft-price.workspace = true
pallet-tfgrid.workspace = true

# Substrate stuff
substrate-fixed = { git = 'https://github.com/encointer/substrate-fixed.git', rev = "b33d186888c60f38adafcfc0ec3a21aab263aef1" }
pallet-balances.workspace = true
frame-support.workspace = true
frame-system.workspace = true
pallet-collective.workspace = true
sp-runtime.workspace = true
sp-std.workspace = true
pallet-timestamp.workspace = true
sp-io.workspace = true
frame-try-runtime.workspace = true
sp-core.workspace = true
pallet-authorship.workspace = true
pallet-session.workspace = true
substrate-validator-set.workspace = true

# Benchmarking
frame-benchmarking = { workspace = true, optional = true }

[dev-dependencies]
parking_lot = '0.12.1'
sp-keystore.workspace = true
env_logger = "*"

[features]
default = ['std']
std = [
	'pallet-balances/std',
	'frame-support/std',
	'frame-system/std',
	'sp-runtime/std',
	'sp-std/std',
    'pallet-timestamp/std',
	'pallet-tfgrid/std',
	'pallet-tft-price/std',
	'parity-scale-codec/std',
	'log/std',
	'tfchain-support/std',
	'scale-info/std',
	'frame-benchmarking/std',
	'sp-io/std',
	"pallet-collective/std",
	'frame-try-runtime/std',
	'sp-core/std',
	'pallet-authorship/std',
	'pallet-session/std',
	'substrate-fixed/std',
	'substrate-validator-set/std'
]
runtime-benchmarks = [
	"frame-benchmarking/runtime-benchmarks",
	"pallet-balances/runtime-benchmarks",
	"frame-support/runtime-benchmarks",
	"frame-system/runtime-benchmarks",
]
try-runtime = [
	"frame-support/try-runtime"
] 