
//! Autogenerated weights for pallet_kvstore
//!
//! THIS FILE WAS AUTO-GENERATED USING THE SUBSTRATE BENCHMARK CLI VERSION 4.0.0-dev
//! DATE: 2024-10-01, STEPS: `50`, REPEAT: `20`, LOW RANGE: `[]`, HIGH RANGE: `[]`
//! WORST CASE MAP SIZE: `1000000`
//! HOSTNAME: `585a9f003813`, CPU: `AMD Ryzen 7 5800X 8-Core Processor`
//! EXECUTION: , WASM-EXECUTION: Compiled, CHAIN: Some("dev"), DB CACHE: 1024

// Executed Command:
// ./target/production/tfchain
// benchmark
// pallet
// --chain=dev
// --wasm-execution=compiled
// --pallet=pallet-kvstore
// --extrinsic=*
// --steps=50
// --repeat=20
// --heap-pages=409
// --output
// ./pallets/pallet-kvstore/src/weights.rs
// --template
// ./.maintain/frame-weight-template.hbs

#![cfg_attr(rustfmt, rustfmt_skip)]
#![allow(unused_parens)]
#![allow(unused_imports)]
#![allow(missing_docs)]

use frame_support::{traits::Get, weights::{Weight, constants::RocksDbWeight}};
use core::marker::PhantomData;

/// Weight functions needed for pallet_kvstore.
pub trait WeightInfo {
	fn set() -> Weight;
	fn delete() -> Weight;
}

/// Weights for pallet_kvstore using the Substrate node and recommended hardware.
pub struct SubstrateWeight<T>(PhantomData<T>);
impl<T: frame_system::Config> WeightInfo for SubstrateWeight<T> {
	/// Storage: `TFKVStore::TFKVStore` (r:0 w:1)
	/// Proof: `TFKVStore::TFKVStore` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn set() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `0`
		//  Estimated: `0`
		// Minimum execution time: 6_903_000 picoseconds.
		Weight::from_parts(7_153_000, 0)
			.saturating_add(T::DbWeight::get().writes(1_u64))
	}
	/// Storage: `TFKVStore::TFKVStore` (r:1 w:1)
	/// Proof: `TFKVStore::TFKVStore` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn delete() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `146`
		//  Estimated: `3611`
		// Minimum execution time: 12_274_000 picoseconds.
		Weight::from_parts(12_734_000, 3611)
			.saturating_add(T::DbWeight::get().reads(1_u64))
			.saturating_add(T::DbWeight::get().writes(1_u64))
	}
}

// For backwards compatibility and tests
impl WeightInfo for () {
	/// Storage: `TFKVStore::TFKVStore` (r:0 w:1)
	/// Proof: `TFKVStore::TFKVStore` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn set() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `0`
		//  Estimated: `0`
		// Minimum execution time: 6_903_000 picoseconds.
		Weight::from_parts(7_153_000, 0)
			.saturating_add(RocksDbWeight::get().writes(1_u64))
	}
	/// Storage: `TFKVStore::TFKVStore` (r:1 w:1)
	/// Proof: `TFKVStore::TFKVStore` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn delete() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `146`
		//  Estimated: `3611`
		// Minimum execution time: 12_274_000 picoseconds.
		Weight::from_parts(12_734_000, 3611)
			.saturating_add(RocksDbWeight::get().reads(1_u64))
			.saturating_add(RocksDbWeight::get().writes(1_u64))
	}
}
