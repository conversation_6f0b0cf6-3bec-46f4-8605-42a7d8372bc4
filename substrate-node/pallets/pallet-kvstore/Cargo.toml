[package]
authors.workspace = true
documentation.workspace = true
edition.workspace = true
homepage.workspace = true
license-file.workspace = true
readme.workspace = true
repository.workspace = true
version.workspace = true
name = "pallet-kvstore"
description = "Kvstore on tfchain"

[package.metadata.docs.rs]
targets = ['x86_64-unknown-linux-gnu']

[dependencies]
parity-scale-codec = {workspace = true, features = ["derive"]}
scale-info = { workspace = true, features = ["derive"] }

# Substrate stuff
frame-support.workspace = true
frame-system.workspace = true
sp-runtime.workspace = true
sp-std.workspace = true
sp-storage.workspace = true
sp-io.workspace = true

# Benchmarking
frame-benchmarking.workspace = true

[dev-dependencies]
sp-core.workspace = true

[features]
default = ['std']
std = [
    'parity-scale-codec/std',
    'frame-support/std',
    'frame-system/std',
    'sp-runtime/std',
    'sp-std/std',
    'sp-storage/std',
    'sp-io/std',
	'scale-info/std',
	'frame-benchmarking/std',
]
runtime-benchmarks = [
	"frame-benchmarking/runtime-benchmarks",
]
try-runtime = [
    "frame-support/try-runtime",
]