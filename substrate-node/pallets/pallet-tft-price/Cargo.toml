[package]
authors.workspace = true
documentation.workspace = true
edition.workspace = true
homepage.workspace = true
license-file.workspace = true
readme.workspace = true
repository.workspace = true
version.workspace = true
name = "pallet-tft-price"
description = "TFT price oracle on tfchain"

[package.metadata.docs.rs]
targets = ['x86_64-unknown-linux-gnu']

[dependencies]
log.workspace = true
serde = { workspace = true, features = ["derive"], default-features = false }
lite-json.workspace = true
parking_lot.workspace = true
serde_json = { workspace = true, features = ["alloc"] }
parity-scale-codec = {workspace = true, features = ["derive"]}
scale-info = { workspace = true, features = ["derive"] }

# Support
tfchain-support.workspace = true

# Substrate stuff
substrate-fixed = { git = 'https://github.com/encointer/substrate-fixed.git', rev = "b33d186888c60f38adafcfc0ec3a21aab263aef1" }
frame-support.workspace = true
frame-system.workspace = true
sp-runtime.workspace = true
sp-core.workspace = true
sp-io.workspace = true
pallet-authorship.workspace = true
pallet-session.workspace = true
sp-std.workspace = true
sp-keystore = { workspace = true, optional = true }
substrate-validator-set.workspace = true

frame-benchmarking.workspace = true

[features]
default = ['std']
std = [
	'parity-scale-codec/std',
	'frame-support/std',
	'frame-system/std',
	'frame-benchmarking/std',
	'lite-json/std',
	'sp-io/std',
	'sp-runtime/std',
	'sp-core/std',
	'sp-std/std',
	'sp-keystore',
	'serde/std',
	'log/std',
	'scale-info/std',
	'pallet-authorship/std',
	'pallet-session/std',
	'tfchain-support/std',
	'serde_json/std',
	'substrate-fixed/std'
]
runtime-benchmarks = [
	"frame-benchmarking/runtime-benchmarks",
]
try-runtime = [
    "frame-support/try-runtime",
]