
//! Autogenerated weights for pallet_tft_price
//!
//! THIS FILE WAS AUTO-GENERATED USING THE SUBSTRATE BENCHMARK CLI VERSION 4.0.0-dev
//! DATE: 2024-10-01, STEPS: `50`, REPEAT: `20`, LOW RANGE: `[]`, HIGH RANGE: `[]`
//! WORST CASE MAP SIZE: `1000000`
//! HOSTNAME: `585a9f003813`, CPU: `AMD Ryzen 7 5800X 8-Core Processor`
//! EXECUTION: , WASM-EXECUTION: Compiled, CHAIN: Some("dev"), DB CACHE: 1024

// Executed Command:
// ./target/production/tfchain
// benchmark
// pallet
// --chain=dev
// --wasm-execution=compiled
// --pallet=pallet-tft-price
// --extrinsic=*
// --steps=50
// --repeat=20
// --heap-pages=409
// --output
// ./pallets/pallet-tft-price/src/weights.rs
// --template
// ./.maintain/frame-weight-template.hbs

#![cfg_attr(rustfmt, rustfmt_skip)]
#![allow(unused_parens)]
#![allow(unused_imports)]
#![allow(missing_docs)]

use frame_support::{traits::Get, weights::{Weight, constants::RocksDbWeight}};
use core::marker::PhantomData;

/// Weight functions needed for pallet_tft_price.
pub trait WeightInfo {
	fn set_prices() -> Weight;
	fn set_min_tft_price() -> Weight;
	fn set_max_tft_price() -> Weight;
}

/// Weights for pallet_tft_price using the Substrate node and recommended hardware.
pub struct SubstrateWeight<T>(PhantomData<T>);
impl<T: frame_system::Config> WeightInfo for SubstrateWeight<T> {
	/// Storage: `Session::Validators` (r:1 w:0)
	/// Proof: `Session::Validators` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `TFTPriceModule::BufferRange` (r:1 w:1)
	/// Proof: `TFTPriceModule::BufferRange` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `TFTPriceModule::TftPriceHistory` (r:2 w:1)
	/// Proof: `TFTPriceModule::TftPriceHistory` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TFTPriceModule::MinTftPrice` (r:1 w:0)
	/// Proof: `TFTPriceModule::MinTftPrice` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `TFTPriceModule::MaxTftPrice` (r:1 w:0)
	/// Proof: `TFTPriceModule::MaxTftPrice` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `TFTPriceModule::TftPrice` (r:0 w:1)
	/// Proof: `TFTPriceModule::TftPrice` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `TFTPriceModule::LastBlockSet` (r:0 w:1)
	/// Proof: `TFTPriceModule::LastBlockSet` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `TFTPriceModule::AverageTftPrice` (r:0 w:1)
	/// Proof: `TFTPriceModule::AverageTftPrice` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	fn set_prices() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `235`
		//  Estimated: `6175`
		// Minimum execution time: 29_515_000 picoseconds.
		Weight::from_parts(30_608_000, 6175)
			.saturating_add(T::DbWeight::get().reads(6_u64))
			.saturating_add(T::DbWeight::get().writes(5_u64))
	}
	/// Storage: `TFTPriceModule::MaxTftPrice` (r:1 w:0)
	/// Proof: `TFTPriceModule::MaxTftPrice` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `TFTPriceModule::MinTftPrice` (r:0 w:1)
	/// Proof: `TFTPriceModule::MinTftPrice` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	fn set_min_tft_price() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `93`
		//  Estimated: `1578`
		// Minimum execution time: 5_149_000 picoseconds.
		Weight::from_parts(5_340_000, 1578)
			.saturating_add(T::DbWeight::get().reads(1_u64))
			.saturating_add(T::DbWeight::get().writes(1_u64))
	}
	/// Storage: `TFTPriceModule::MinTftPrice` (r:1 w:0)
	/// Proof: `TFTPriceModule::MinTftPrice` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `TFTPriceModule::MaxTftPrice` (r:0 w:1)
	/// Proof: `TFTPriceModule::MaxTftPrice` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	fn set_max_tft_price() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `93`
		//  Estimated: `1578`
		// Minimum execution time: 5_129_000 picoseconds.
		Weight::from_parts(5_190_000, 1578)
			.saturating_add(T::DbWeight::get().reads(1_u64))
			.saturating_add(T::DbWeight::get().writes(1_u64))
	}
}

// For backwards compatibility and tests
impl WeightInfo for () {
	/// Storage: `Session::Validators` (r:1 w:0)
	/// Proof: `Session::Validators` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `TFTPriceModule::BufferRange` (r:1 w:1)
	/// Proof: `TFTPriceModule::BufferRange` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `TFTPriceModule::TftPriceHistory` (r:2 w:1)
	/// Proof: `TFTPriceModule::TftPriceHistory` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TFTPriceModule::MinTftPrice` (r:1 w:0)
	/// Proof: `TFTPriceModule::MinTftPrice` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `TFTPriceModule::MaxTftPrice` (r:1 w:0)
	/// Proof: `TFTPriceModule::MaxTftPrice` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `TFTPriceModule::TftPrice` (r:0 w:1)
	/// Proof: `TFTPriceModule::TftPrice` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `TFTPriceModule::LastBlockSet` (r:0 w:1)
	/// Proof: `TFTPriceModule::LastBlockSet` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `TFTPriceModule::AverageTftPrice` (r:0 w:1)
	/// Proof: `TFTPriceModule::AverageTftPrice` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	fn set_prices() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `235`
		//  Estimated: `6175`
		// Minimum execution time: 29_515_000 picoseconds.
		Weight::from_parts(30_608_000, 6175)
			.saturating_add(RocksDbWeight::get().reads(6_u64))
			.saturating_add(RocksDbWeight::get().writes(5_u64))
	}
	/// Storage: `TFTPriceModule::MaxTftPrice` (r:1 w:0)
	/// Proof: `TFTPriceModule::MaxTftPrice` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `TFTPriceModule::MinTftPrice` (r:0 w:1)
	/// Proof: `TFTPriceModule::MinTftPrice` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	fn set_min_tft_price() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `93`
		//  Estimated: `1578`
		// Minimum execution time: 5_149_000 picoseconds.
		Weight::from_parts(5_340_000, 1578)
			.saturating_add(RocksDbWeight::get().reads(1_u64))
			.saturating_add(RocksDbWeight::get().writes(1_u64))
	}
	/// Storage: `TFTPriceModule::MinTftPrice` (r:1 w:0)
	/// Proof: `TFTPriceModule::MinTftPrice` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `TFTPriceModule::MaxTftPrice` (r:0 w:1)
	/// Proof: `TFTPriceModule::MaxTftPrice` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	fn set_max_tft_price() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `93`
		//  Estimated: `1578`
		// Minimum execution time: 5_129_000 picoseconds.
		Weight::from_parts(5_190_000, 1578)
			.saturating_add(RocksDbWeight::get().reads(1_u64))
			.saturating_add(RocksDbWeight::get().writes(1_u64))
	}
}
