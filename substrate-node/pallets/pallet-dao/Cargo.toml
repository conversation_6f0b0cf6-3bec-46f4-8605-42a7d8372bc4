[package]
authors.workspace = true
documentation.workspace = true
edition.workspace = true
homepage.workspace = true
license-file.workspace = true
readme.workspace = true
repository.workspace = true
version.workspace = true
name = "pallet-dao"
description = "DAO on tfchain"

[package.metadata.docs.rs]
targets = ["x86_64-unknown-linux-gnu"]

[dependencies]
log.workspace = true
serde.workspace = true
parity-scale-codec = {workspace = true, features = ["derive"]}
scale-info = { workspace = true, features = ["derive"] }

frame-benchmarking = { workspace = true, optional = true }
frame-support.workspace = true
frame-system.workspace = true
pallet-membership.workspace = true
pallet-collective.workspace = true
pallet-timestamp.workspace = true
sp-runtime.workspace = true
sp-std.workspace = true
sp-io.workspace = true

tfchain-support.workspace = true
pallet-tfgrid.workspace = true

[dev-dependencies]
sp-core.workspace = true
env_logger = "*"

[features]
default = ["std"]
std = [
	"parity-scale-codec/std",
	"frame-support/std",
	"frame-system/std",
	"frame-benchmarking/std",
	"sp-runtime/std",
	"sp-std/std",
	"sp-io/std",
	"pallet-membership/std",
	"pallet-collective/std",
	"pallet-timestamp/std",
	"pallet-tfgrid/std",
	"tfchain-support/std",
	"scale-info/std",
	"serde/std",
	"log/std",
]
runtime-benchmarks = [
	"frame-benchmarking/runtime-benchmarks",
	"pallet-collective/runtime-benchmarks",
]
try-runtime = [
    "frame-support/try-runtime",
]