[package]
authors.workspace = true
documentation.workspace = true
edition.workspace = true
homepage.workspace = true
license-file.workspace = true
readme.workspace = true
repository.workspace = true
version.workspace = true
name = "pallet-tfgrid"
description = "Tfgrid store on tfchain"

[package.metadata.docs.rs]
targets = ['x86_64-unknown-linux-gnu']

[dependencies]
log.workspace = true
hex.workspace = true
serde_json = { workspace = true, features = ["alloc"] }
parity-scale-codec = {workspace = true, features = ["derive"]}
scale-info = { workspace = true, features = ["derive"] }
valip.workspace = true

# Support
tfchain-support.workspace = true

# Substrate stuff
pallet-balances.workspace = true
frame-support.workspace = true
frame-system.workspace = true
sp-runtime.workspace = true
sp-std.workspace = true
sp-storage.workspace = true
pallet-timestamp.workspace = true
sp-core.workspace = true
sp-io.workspace = true
sp-weights.workspace = true
frame-try-runtime.workspace = true

# Benchmarking
frame-benchmarking = { workspace = true, optional = true }

[dev-dependencies]
hex-literal.workspace = true
pallet-membership.workspace = true
pallet-collective.workspace = true
env_logger = "*"

[features]
default = ['std']
std = [
	'parity-scale-codec/std',
	'pallet-balances/std',
	'frame-support/std',
	'frame-system/std',
	'sp-runtime/std',
	'sp-std/std',
	'sp-storage/std',
	'pallet-timestamp/std',
	'sp-core/std',
	'sp-io/std',
	'frame-benchmarking/std',
	'tfchain-support/std',
	'scale-info/std',
	'hex/std',
	'log/std',
	'frame-try-runtime/std',
	'pallet-membership/std',
	'pallet-collective/std',
	'serde_json/std'
]
runtime-benchmarks = [
	"frame-benchmarking/runtime-benchmarks",
	"pallet-collective/runtime-benchmarks",
]
try-runtime = [
	"frame-support/try-runtime"
]