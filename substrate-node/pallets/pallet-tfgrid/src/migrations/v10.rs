use crate::*;
use frame_support::{traits::Get, traits::OnRuntimeUpgrade};
use log::{debug, info};
use sp_std::{collections::btree_map::BTreeMap, marker::PhantomData};
use sp_std::{vec, vec::Vec};
use sp_weights::Weight;

#[cfg(feature = "try-runtime")]
use frame_support::ensure;
#[cfg(feature = "try-runtime")]
use parity_scale_codec::{Decode, Encode};
#[cfg(feature = "try-runtime")]
use sp_runtime::DispatchError;

pub struct FixFarmNodeIndexMap<T: Config>(PhantomData<T>);

impl<T: Config> OnRuntimeUpgrade for FixFarmNodeIndexMap<T> {
    #[cfg(feature = "try-runtime")]
    fn pre_upgrade() -> Result<Vec<u8>, sp_runtime::TryRuntimeError> {
        info!("current pallet version: {:?}", PalletVersion::<T>::get());
        ensure!(
            PalletVersion::<T>::get() >= types::StorageVersion::V9Struct,
            DispatchError::Other("Unexpected pallet version")
        );

        let nodes_count: u64 = Nodes::<T>::iter().count() as u64;
        log::info!(
            "🔎 FixFarmNodeIndexMap pre migration: Number of existing nodes {:?}",
            nodes_count
        );

        info!("👥  TFGrid pallet to V10 passes PRE migrate checks ✅",);
        Ok(nodes_count.encode())
    }

    fn on_runtime_upgrade() -> Weight {
        if PalletVersion::<T>::get() == types::StorageVersion::V9Struct {
            add_farm_nodes_index::<T>()
        } else {
            info!(" >>> Unused TFGrid pallet V10 migration");
            Weight::zero()
        }
    }

    #[cfg(feature = "try-runtime")]
    fn post_upgrade(pre_nodes_count: Vec<u8>) -> Result<(), sp_runtime::TryRuntimeError> {
        info!("current pallet version: {:?}", PalletVersion::<T>::get());
        ensure!(
            PalletVersion::<T>::get() >= types::StorageVersion::V10Struct,
            DispatchError::Other("Unexpected pallet version")
        );

        // Check number of nodes against pre-check result
        let pre_nodes_count: u64 = Decode::decode(&mut pre_nodes_count.as_slice())
            .expect("the state parameter should be something that was generated by pre_upgrade");
        ensure!(
            Nodes::<T>::iter().count() as u64 == pre_nodes_count,
            DispatchError::Other("Number of nodes migrated does not match")
        );

        info!(
            "👥  TFGrid pallet migration to {:?} passes POST migrate checks ✅",
            Pallet::<T>::pallet_version()
        );

        Ok(())
    }
}

pub fn add_farm_nodes_index<T: Config>() -> frame_support::weights::Weight {
    info!(" >>> Migrating nodes storage...");

    let _ = NodesByFarmID::<T>::clear(0, None);

    let mut reads = 0;
    let mut writes = 0;

    let mut farms_with_nodes: BTreeMap<u32, Vec<u32>> = BTreeMap::new();
    for (_, node) in Nodes::<T>::iter() {
        // Add index of farm - list (nodes)
        farms_with_nodes
            .entry(node.farm_id)
            .or_insert(vec![])
            .push(node.id);

        reads += 1;
    }

    for (farm_id, nodes) in farms_with_nodes.iter() {
        debug!(
            "inserting nodes: {:?} with farm id: {:?}",
            nodes.clone(),
            farm_id
        );
        NodesByFarmID::<T>::insert(farm_id, nodes);
        writes += 1;
    }

    // Update pallet storage version
    PalletVersion::<T>::set(types::StorageVersion::V10Struct);
    info!(" <<< Storage version upgraded");

    // Return the weight consumed by the migration.
    T::DbWeight::get().reads_writes(reads, writes)
}
