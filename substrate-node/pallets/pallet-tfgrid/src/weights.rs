
//! Autogenerated weights for pallet_tfgrid
//!
//! THIS FILE WAS AUTO-GENERATED USING THE SUBSTRATE BENCHMARK CLI VERSION 4.0.0-dev
//! DATE: 2024-10-01, STEPS: `50`, REPEAT: `20`, LOW RANGE: `[]`, HIGH RANGE: `[]`
//! WORST CASE MAP SIZE: `1000000`
//! HOSTNAME: `585a9f003813`, CPU: `AMD Ryzen 7 5800X 8-Core Processor`
//! EXECUTION: , WASM-EXECUTION: Compiled, CHAIN: Some("dev"), DB CACHE: 1024

// Executed Command:
// ./target/production/tfchain
// benchmark
// pallet
// --chain=dev
// --wasm-execution=compiled
// --pallet=pallet-tfgrid
// --extrinsic=*
// --steps=50
// --repeat=20
// --heap-pages=409
// --output
// ./pallets/pallet-tfgrid/src/weights.rs
// --template
// ./.maintain/frame-weight-template.hbs

#![cfg_attr(rustfmt, rustfmt_skip)]
#![allow(unused_parens)]
#![allow(unused_imports)]
#![allow(missing_docs)]

use frame_support::{traits::Get, weights::{Weight, constants::RocksDbWeight}};
use core::marker::PhantomData;

/// Weight functions needed for pallet_tfgrid.
pub trait WeightInfo {
	fn set_storage_version() -> Weight;
	fn create_farm() -> Weight;
	fn update_farm() -> Weight;
	fn add_stellar_payout_v2address() -> Weight;
	fn set_farm_certification() -> Weight;
	fn add_farm_ip() -> Weight;
	fn remove_farm_ip() -> Weight;
	fn create_node() -> Weight;
	fn update_node() -> Weight;
	fn set_node_certification() -> Weight;
	fn report_uptime() -> Weight;
	fn add_node_public_config() -> Weight;
	fn delete_node() -> Weight;
	fn create_twin() -> Weight;
	fn update_twin() -> Weight;
	fn create_pricing_policy() -> Weight;
	fn update_pricing_policy() -> Weight;
	fn create_farming_policy() -> Weight;
	fn user_accept_tc() -> Weight;
	fn delete_node_farm() -> Weight;
	fn set_farm_dedicated() -> Weight;
	fn force_reset_farm_ip() -> Weight;
	fn set_connection_price() -> Weight;
	fn add_node_certifier() -> Weight;
	fn remove_node_certifier() -> Weight;
	fn update_farming_policy() -> Weight;
	fn attach_policy_to_farm() -> Weight;
	fn set_zos_version() -> Weight;
	fn change_power_state() -> Weight;
	fn change_power_target() -> Weight;
	fn bond_twin_account() -> Weight;
	fn report_uptime_v2() -> Weight;
}

/// Weights for pallet_tfgrid using the Substrate node and recommended hardware.
pub struct SubstrateWeight<T>(PhantomData<T>);
impl<T: frame_system::Config> WeightInfo for SubstrateWeight<T> {
	/// Storage: `TfgridModule::PalletVersion` (r:0 w:1)
	/// Proof: `TfgridModule::PalletVersion` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	fn set_storage_version() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `0`
		//  Estimated: `0`
		// Minimum execution time: 3_788_000 picoseconds.
		Weight::from_parts(4_018_000, 0)
			.saturating_add(T::DbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Twins` (r:1 w:0)
	/// Proof: `TfgridModule::Twins` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::FarmIdByName` (r:1 w:1)
	/// Proof: `TfgridModule::FarmIdByName` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::FarmID` (r:1 w:1)
	/// Proof: `TfgridModule::FarmID` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Farms` (r:0 w:1)
	/// Proof: `TfgridModule::Farms` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn create_farm() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `496`
		//  Estimated: `3961`
		// Minimum execution time: 19_638_000 picoseconds.
		Weight::from_parts(20_018_000, 3961)
			.saturating_add(T::DbWeight::get().reads(4_u64))
			.saturating_add(T::DbWeight::get().writes(3_u64))
	}
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Farms` (r:1 w:1)
	/// Proof: `TfgridModule::Farms` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::FarmIdByName` (r:1 w:2)
	/// Proof: `TfgridModule::FarmIdByName` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn update_farm() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `507`
		//  Estimated: `3972`
		// Minimum execution time: 21_551_000 picoseconds.
		Weight::from_parts(22_363_000, 3972)
			.saturating_add(T::DbWeight::get().reads(3_u64))
			.saturating_add(T::DbWeight::get().writes(3_u64))
	}
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Farms` (r:1 w:0)
	/// Proof: `TfgridModule::Farms` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::FarmPayoutV2AddressByFarmID` (r:0 w:1)
	/// Proof: `TfgridModule::FarmPayoutV2AddressByFarmID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn add_stellar_payout_v2address() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `453`
		//  Estimated: `3918`
		// Minimum execution time: 15_699_000 picoseconds.
		Weight::from_parts(16_131_000, 3918)
			.saturating_add(T::DbWeight::get().reads(2_u64))
			.saturating_add(T::DbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::Farms` (r:1 w:1)
	/// Proof: `TfgridModule::Farms` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn set_farm_certification() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `412`
		//  Estimated: `3877`
		// Minimum execution time: 13_215_000 picoseconds.
		Weight::from_parts(13_456_000, 3877)
			.saturating_add(T::DbWeight::get().reads(1_u64))
			.saturating_add(T::DbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::Farms` (r:1 w:1)
	/// Proof: `TfgridModule::Farms` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Twins` (r:1 w:0)
	/// Proof: `TfgridModule::Twins` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn add_farm_ip() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `569`
		//  Estimated: `4034`
		// Minimum execution time: 17_754_000 picoseconds.
		Weight::from_parts(18_194_000, 4034)
			.saturating_add(T::DbWeight::get().reads(2_u64))
			.saturating_add(T::DbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::Farms` (r:1 w:1)
	/// Proof: `TfgridModule::Farms` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Twins` (r:1 w:0)
	/// Proof: `TfgridModule::Twins` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn remove_farm_ip() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `569`
		//  Estimated: `4034`
		// Minimum execution time: 16_962_000 picoseconds.
		Weight::from_parts(17_342_000, 4034)
			.saturating_add(T::DbWeight::get().reads(2_u64))
			.saturating_add(T::DbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::Farms` (r:1 w:0)
	/// Proof: `TfgridModule::Farms` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::NodeIdByTwinID` (r:1 w:1)
	/// Proof: `TfgridModule::NodeIdByTwinID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::NodeID` (r:1 w:1)
	/// Proof: `TfgridModule::NodeID` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `Timestamp::Now` (r:1 w:0)
	/// Proof: `Timestamp::Now` (`max_values`: Some(1), `max_size`: Some(8), added: 503, mode: `MaxEncodedLen`)
	/// Storage: `TfgridModule::ConnectionPrice` (r:1 w:0)
	/// Proof: `TfgridModule::ConnectionPrice` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::FarmingPoliciesMap` (r:4 w:0)
	/// Proof: `TfgridModule::FarmingPoliciesMap` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::NodesByFarmID` (r:1 w:1)
	/// Proof: `TfgridModule::NodesByFarmID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `Dao::FarmWeight` (r:1 w:1)
	/// Proof: `Dao::FarmWeight` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Nodes` (r:0 w:1)
	/// Proof: `TfgridModule::Nodes` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn create_node() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `905`
		//  Estimated: `11795`
		// Minimum execution time: 47_349_000 picoseconds.
		Weight::from_parts(48_843_000, 11795)
			.saturating_add(T::DbWeight::get().reads(12_u64))
			.saturating_add(T::DbWeight::get().writes(5_u64))
	}
	/// Storage: `TfgridModule::Nodes` (r:1 w:1)
	/// Proof: `TfgridModule::Nodes` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Farms` (r:1 w:0)
	/// Proof: `TfgridModule::Farms` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `Dao::FarmWeight` (r:1 w:1)
	/// Proof: `Dao::FarmWeight` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn update_node() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `661`
		//  Estimated: `4126`
		// Minimum execution time: 26_720_000 picoseconds.
		Weight::from_parts(27_242_000, 4126)
			.saturating_add(T::DbWeight::get().reads(4_u64))
			.saturating_add(T::DbWeight::get().writes(2_u64))
	}
	/// Storage: `TfgridModule::Nodes` (r:1 w:1)
	/// Proof: `TfgridModule::Nodes` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::FarmingPoliciesMap` (r:4 w:0)
	/// Proof: `TfgridModule::FarmingPoliciesMap` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Farms` (r:1 w:0)
	/// Proof: `TfgridModule::Farms` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn set_node_certification() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `956`
		//  Estimated: `11846`
		// Minimum execution time: 34_234_000 picoseconds.
		Weight::from_parts(35_077_000, 11846)
			.saturating_add(T::DbWeight::get().reads(6_u64))
			.saturating_add(T::DbWeight::get().writes(1_u64))
	}
	/// Storage: `Timestamp::Now` (r:1 w:0)
	/// Proof: `Timestamp::Now` (`max_values`: Some(1), `max_size`: Some(8), added: 503, mode: `MaxEncodedLen`)
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::NodeIdByTwinID` (r:1 w:0)
	/// Proof: `TfgridModule::NodeIdByTwinID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Nodes` (r:1 w:0)
	/// Proof: `TfgridModule::Nodes` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn report_uptime() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `454`
		//  Estimated: `3919`
		// Minimum execution time: 18_786_000 picoseconds.
		Weight::from_parts(19_336_000, 3919)
			.saturating_add(T::DbWeight::get().reads(4_u64))
	}
	/// Storage: `TfgridModule::Farms` (r:1 w:0)
	/// Proof: `TfgridModule::Farms` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Twins` (r:1 w:0)
	/// Proof: `TfgridModule::Twins` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Nodes` (r:1 w:1)
	/// Proof: `TfgridModule::Nodes` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn add_node_public_config() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `779`
		//  Estimated: `4244`
		// Minimum execution time: 24_206_000 picoseconds.
		Weight::from_parts(25_237_000, 4244)
			.saturating_add(T::DbWeight::get().reads(3_u64))
			.saturating_add(T::DbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::Nodes` (r:1 w:1)
	/// Proof: `TfgridModule::Nodes` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::NodesByFarmID` (r:1 w:1)
	/// Proof: `TfgridModule::NodesByFarmID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ActiveNodeContracts` (r:1 w:0)
	/// Proof: `SmartContractModule::ActiveNodeContracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ActiveRentContractForNode` (r:1 w:0)
	/// Proof: `SmartContractModule::ActiveRentContractForNode` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `Dao::FarmWeight` (r:1 w:1)
	/// Proof: `Dao::FarmWeight` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn delete_node() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `682`
		//  Estimated: `4147`
		// Minimum execution time: 27_802_000 picoseconds.
		Weight::from_parts(28_273_000, 4147)
			.saturating_add(T::DbWeight::get().reads(6_u64))
			.saturating_add(T::DbWeight::get().writes(3_u64))
	}
	/// Storage: `TfgridModule::UsersTermsAndConditions` (r:1 w:0)
	/// Proof: `TfgridModule::UsersTermsAndConditions` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:1)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::TwinID` (r:1 w:1)
	/// Proof: `TfgridModule::TwinID` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Twins` (r:0 w:1)
	/// Proof: `TfgridModule::Twins` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn create_twin() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `235`
		//  Estimated: `3700`
		// Minimum execution time: 14_217_000 picoseconds.
		Weight::from_parts(14_568_000, 3700)
			.saturating_add(T::DbWeight::get().reads(3_u64))
			.saturating_add(T::DbWeight::get().writes(3_u64))
	}
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Twins` (r:1 w:1)
	/// Proof: `TfgridModule::Twins` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn update_twin() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `428`
		//  Estimated: `3893`
		// Minimum execution time: 14_848_000 picoseconds.
		Weight::from_parts(15_440_000, 3893)
			.saturating_add(T::DbWeight::get().reads(2_u64))
			.saturating_add(T::DbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::PricingPolicyIdByName` (r:1 w:1)
	/// Proof: `TfgridModule::PricingPolicyIdByName` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::PricingPolicyID` (r:1 w:1)
	/// Proof: `TfgridModule::PricingPolicyID` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::PricingPolicies` (r:0 w:1)
	/// Proof: `TfgridModule::PricingPolicies` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn create_pricing_policy() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `134`
		//  Estimated: `3599`
		// Minimum execution time: 11_923_000 picoseconds.
		Weight::from_parts(12_324_000, 3599)
			.saturating_add(T::DbWeight::get().reads(2_u64))
			.saturating_add(T::DbWeight::get().writes(3_u64))
	}
	/// Storage: `TfgridModule::PricingPolicies` (r:1 w:1)
	/// Proof: `TfgridModule::PricingPolicies` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::PricingPolicyIdByName` (r:1 w:1)
	/// Proof: `TfgridModule::PricingPolicyIdByName` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::PricingPolicyID` (r:0 w:1)
	/// Proof: `TfgridModule::PricingPolicyID` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	fn update_pricing_policy() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `382`
		//  Estimated: `3847`
		// Minimum execution time: 16_441_000 picoseconds.
		Weight::from_parts(16_851_000, 3847)
			.saturating_add(T::DbWeight::get().reads(2_u64))
			.saturating_add(T::DbWeight::get().writes(3_u64))
	}
	/// Storage: `TfgridModule::FarmingPolicyID` (r:1 w:1)
	/// Proof: `TfgridModule::FarmingPolicyID` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::FarmingPoliciesMap` (r:0 w:1)
	/// Proof: `TfgridModule::FarmingPoliciesMap` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn create_farming_policy() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `134`
		//  Estimated: `1619`
		// Minimum execution time: 10_229_000 picoseconds.
		Weight::from_parts(10_560_000, 1619)
			.saturating_add(T::DbWeight::get().reads(1_u64))
			.saturating_add(T::DbWeight::get().writes(2_u64))
	}
	/// Storage: `Timestamp::Now` (r:1 w:0)
	/// Proof: `Timestamp::Now` (`max_values`: Some(1), `max_size`: Some(8), added: 503, mode: `MaxEncodedLen`)
	/// Storage: `TfgridModule::UsersTermsAndConditions` (r:1 w:1)
	/// Proof: `TfgridModule::UsersTermsAndConditions` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn user_accept_tc() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `140`
		//  Estimated: `3605`
		// Minimum execution time: 7_464_000 picoseconds.
		Weight::from_parts(7_694_000, 3605)
			.saturating_add(T::DbWeight::get().reads(2_u64))
			.saturating_add(T::DbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Nodes` (r:1 w:1)
	/// Proof: `TfgridModule::Nodes` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Farms` (r:1 w:0)
	/// Proof: `TfgridModule::Farms` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::NodesByFarmID` (r:1 w:1)
	/// Proof: `TfgridModule::NodesByFarmID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ActiveNodeContracts` (r:1 w:0)
	/// Proof: `SmartContractModule::ActiveNodeContracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ActiveRentContractForNode` (r:1 w:0)
	/// Proof: `SmartContractModule::ActiveRentContractForNode` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `Dao::FarmWeight` (r:1 w:1)
	/// Proof: `Dao::FarmWeight` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::NodeIdByTwinID` (r:0 w:1)
	/// Proof: `TfgridModule::NodeIdByTwinID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn delete_node_farm() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `828`
		//  Estimated: `4293`
		// Minimum execution time: 32_201_000 picoseconds.
		Weight::from_parts(32_992_000, 4293)
			.saturating_add(T::DbWeight::get().reads(7_u64))
			.saturating_add(T::DbWeight::get().writes(4_u64))
	}
	/// Storage: `TfgridModule::Farms` (r:1 w:1)
	/// Proof: `TfgridModule::Farms` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn set_farm_dedicated() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `412`
		//  Estimated: `3877`
		// Minimum execution time: 13_666_000 picoseconds.
		Weight::from_parts(13_986_000, 3877)
			.saturating_add(T::DbWeight::get().reads(1_u64))
			.saturating_add(T::DbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::Farms` (r:1 w:1)
	/// Proof: `TfgridModule::Farms` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn force_reset_farm_ip() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `412`
		//  Estimated: `3877`
		// Minimum execution time: 13_445_000 picoseconds.
		Weight::from_parts(13_836_000, 3877)
			.saturating_add(T::DbWeight::get().reads(1_u64))
			.saturating_add(T::DbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::ConnectionPrice` (r:0 w:1)
	/// Proof: `TfgridModule::ConnectionPrice` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	fn set_connection_price() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `0`
		//  Estimated: `0`
		// Minimum execution time: 5_570_000 picoseconds.
		Weight::from_parts(5_971_000, 0)
			.saturating_add(T::DbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::AllowedNodeCertifiers` (r:1 w:1)
	/// Proof: `TfgridModule::AllowedNodeCertifiers` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	fn add_node_certifier() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `355`
		//  Estimated: `1840`
		// Minimum execution time: 9_608_000 picoseconds.
		Weight::from_parts(9_899_000, 1840)
			.saturating_add(T::DbWeight::get().reads(1_u64))
			.saturating_add(T::DbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::AllowedNodeCertifiers` (r:1 w:1)
	/// Proof: `TfgridModule::AllowedNodeCertifiers` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	fn remove_node_certifier() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `413`
		//  Estimated: `1898`
		// Minimum execution time: 11_913_000 picoseconds.
		Weight::from_parts(12_253_000, 1898)
			.saturating_add(T::DbWeight::get().reads(1_u64))
			.saturating_add(T::DbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::FarmingPoliciesMap` (r:1 w:1)
	/// Proof: `TfgridModule::FarmingPoliciesMap` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn update_farming_policy() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `294`
		//  Estimated: `3759`
		// Minimum execution time: 12_985_000 picoseconds.
		Weight::from_parts(13_155_000, 3759)
			.saturating_add(T::DbWeight::get().reads(1_u64))
			.saturating_add(T::DbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::FarmingPoliciesMap` (r:1 w:0)
	/// Proof: `TfgridModule::FarmingPoliciesMap` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Farms` (r:1 w:1)
	/// Proof: `TfgridModule::Farms` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::NodesByFarmID` (r:1 w:0)
	/// Proof: `TfgridModule::NodesByFarmID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn attach_policy_to_farm() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `572`
		//  Estimated: `4037`
		// Minimum execution time: 21_440_000 picoseconds.
		Weight::from_parts(22_112_000, 4037)
			.saturating_add(T::DbWeight::get().reads(3_u64))
			.saturating_add(T::DbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::ZosVersion` (r:1 w:1)
	/// Proof: `TfgridModule::ZosVersion` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	fn set_zos_version() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `134`
		//  Estimated: `1619`
		// Minimum execution time: 7_664_000 picoseconds.
		Weight::from_parts(8_085_000, 1619)
			.saturating_add(T::DbWeight::get().reads(1_u64))
			.saturating_add(T::DbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::NodeIdByTwinID` (r:1 w:0)
	/// Proof: `TfgridModule::NodeIdByTwinID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Nodes` (r:1 w:0)
	/// Proof: `TfgridModule::Nodes` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::NodePower` (r:1 w:1)
	/// Proof: `TfgridModule::NodePower` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn change_power_state() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `621`
		//  Estimated: `4086`
		// Minimum execution time: 22_663_000 picoseconds.
		Weight::from_parts(23_244_000, 4086)
			.saturating_add(T::DbWeight::get().reads(4_u64))
			.saturating_add(T::DbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Nodes` (r:1 w:0)
	/// Proof: `TfgridModule::Nodes` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Farms` (r:1 w:0)
	/// Proof: `TfgridModule::Farms` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ActiveNodeContracts` (r:1 w:0)
	/// Proof: `SmartContractModule::ActiveNodeContracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ActiveRentContractForNode` (r:1 w:0)
	/// Proof: `SmartContractModule::ActiveRentContractForNode` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::NodePower` (r:1 w:1)
	/// Proof: `TfgridModule::NodePower` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn change_power_target() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `792`
		//  Estimated: `4257`
		// Minimum execution time: 25_729_000 picoseconds.
		Weight::from_parts(26_470_000, 4257)
			.saturating_add(T::DbWeight::get().reads(6_u64))
			.saturating_add(T::DbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::Twins` (r:1 w:0)
	/// Proof: `TfgridModule::Twins` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::TwinBoundedAccountID` (r:0 w:1)
	/// Proof: `TfgridModule::TwinBoundedAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn bond_twin_account() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `387`
		//  Estimated: `3852`
		// Minimum execution time: 11_743_000 picoseconds.
		Weight::from_parts(12_053_000, 3852)
			.saturating_add(T::DbWeight::get().reads(1_u64))
			.saturating_add(T::DbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::NodeIdByTwinID` (r:1 w:0)
	/// Proof: `TfgridModule::NodeIdByTwinID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Nodes` (r:1 w:0)
	/// Proof: `TfgridModule::Nodes` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `Timestamp::Now` (r:1 w:0)
	/// Proof: `Timestamp::Now` (`max_values`: Some(1), `max_size`: Some(8), added: 503, mode: `MaxEncodedLen`)
	fn report_uptime_v2() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `454`
		//  Estimated: `3919`
		// Minimum execution time: 18_084_000 picoseconds.
		Weight::from_parts(18_465_000, 3919)
			.saturating_add(T::DbWeight::get().reads(4_u64))
	}
}

// For backwards compatibility and tests
impl WeightInfo for () {
	/// Storage: `TfgridModule::PalletVersion` (r:0 w:1)
	/// Proof: `TfgridModule::PalletVersion` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	fn set_storage_version() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `0`
		//  Estimated: `0`
		// Minimum execution time: 3_788_000 picoseconds.
		Weight::from_parts(4_018_000, 0)
			.saturating_add(RocksDbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Twins` (r:1 w:0)
	/// Proof: `TfgridModule::Twins` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::FarmIdByName` (r:1 w:1)
	/// Proof: `TfgridModule::FarmIdByName` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::FarmID` (r:1 w:1)
	/// Proof: `TfgridModule::FarmID` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Farms` (r:0 w:1)
	/// Proof: `TfgridModule::Farms` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn create_farm() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `496`
		//  Estimated: `3961`
		// Minimum execution time: 19_638_000 picoseconds.
		Weight::from_parts(20_018_000, 3961)
			.saturating_add(RocksDbWeight::get().reads(4_u64))
			.saturating_add(RocksDbWeight::get().writes(3_u64))
	}
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Farms` (r:1 w:1)
	/// Proof: `TfgridModule::Farms` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::FarmIdByName` (r:1 w:2)
	/// Proof: `TfgridModule::FarmIdByName` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn update_farm() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `507`
		//  Estimated: `3972`
		// Minimum execution time: 21_551_000 picoseconds.
		Weight::from_parts(22_363_000, 3972)
			.saturating_add(RocksDbWeight::get().reads(3_u64))
			.saturating_add(RocksDbWeight::get().writes(3_u64))
	}
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Farms` (r:1 w:0)
	/// Proof: `TfgridModule::Farms` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::FarmPayoutV2AddressByFarmID` (r:0 w:1)
	/// Proof: `TfgridModule::FarmPayoutV2AddressByFarmID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn add_stellar_payout_v2address() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `453`
		//  Estimated: `3918`
		// Minimum execution time: 15_699_000 picoseconds.
		Weight::from_parts(16_131_000, 3918)
			.saturating_add(RocksDbWeight::get().reads(2_u64))
			.saturating_add(RocksDbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::Farms` (r:1 w:1)
	/// Proof: `TfgridModule::Farms` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn set_farm_certification() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `412`
		//  Estimated: `3877`
		// Minimum execution time: 13_215_000 picoseconds.
		Weight::from_parts(13_456_000, 3877)
			.saturating_add(RocksDbWeight::get().reads(1_u64))
			.saturating_add(RocksDbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::Farms` (r:1 w:1)
	/// Proof: `TfgridModule::Farms` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Twins` (r:1 w:0)
	/// Proof: `TfgridModule::Twins` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn add_farm_ip() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `569`
		//  Estimated: `4034`
		// Minimum execution time: 17_754_000 picoseconds.
		Weight::from_parts(18_194_000, 4034)
			.saturating_add(RocksDbWeight::get().reads(2_u64))
			.saturating_add(RocksDbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::Farms` (r:1 w:1)
	/// Proof: `TfgridModule::Farms` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Twins` (r:1 w:0)
	/// Proof: `TfgridModule::Twins` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn remove_farm_ip() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `569`
		//  Estimated: `4034`
		// Minimum execution time: 16_962_000 picoseconds.
		Weight::from_parts(17_342_000, 4034)
			.saturating_add(RocksDbWeight::get().reads(2_u64))
			.saturating_add(RocksDbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::Farms` (r:1 w:0)
	/// Proof: `TfgridModule::Farms` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::NodeIdByTwinID` (r:1 w:1)
	/// Proof: `TfgridModule::NodeIdByTwinID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::NodeID` (r:1 w:1)
	/// Proof: `TfgridModule::NodeID` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `Timestamp::Now` (r:1 w:0)
	/// Proof: `Timestamp::Now` (`max_values`: Some(1), `max_size`: Some(8), added: 503, mode: `MaxEncodedLen`)
	/// Storage: `TfgridModule::ConnectionPrice` (r:1 w:0)
	/// Proof: `TfgridModule::ConnectionPrice` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::FarmingPoliciesMap` (r:4 w:0)
	/// Proof: `TfgridModule::FarmingPoliciesMap` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::NodesByFarmID` (r:1 w:1)
	/// Proof: `TfgridModule::NodesByFarmID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `Dao::FarmWeight` (r:1 w:1)
	/// Proof: `Dao::FarmWeight` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Nodes` (r:0 w:1)
	/// Proof: `TfgridModule::Nodes` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn create_node() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `905`
		//  Estimated: `11795`
		// Minimum execution time: 47_349_000 picoseconds.
		Weight::from_parts(48_843_000, 11795)
			.saturating_add(RocksDbWeight::get().reads(12_u64))
			.saturating_add(RocksDbWeight::get().writes(5_u64))
	}
	/// Storage: `TfgridModule::Nodes` (r:1 w:1)
	/// Proof: `TfgridModule::Nodes` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Farms` (r:1 w:0)
	/// Proof: `TfgridModule::Farms` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `Dao::FarmWeight` (r:1 w:1)
	/// Proof: `Dao::FarmWeight` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn update_node() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `661`
		//  Estimated: `4126`
		// Minimum execution time: 26_720_000 picoseconds.
		Weight::from_parts(27_242_000, 4126)
			.saturating_add(RocksDbWeight::get().reads(4_u64))
			.saturating_add(RocksDbWeight::get().writes(2_u64))
	}
	/// Storage: `TfgridModule::Nodes` (r:1 w:1)
	/// Proof: `TfgridModule::Nodes` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::FarmingPoliciesMap` (r:4 w:0)
	/// Proof: `TfgridModule::FarmingPoliciesMap` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Farms` (r:1 w:0)
	/// Proof: `TfgridModule::Farms` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn set_node_certification() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `956`
		//  Estimated: `11846`
		// Minimum execution time: 34_234_000 picoseconds.
		Weight::from_parts(35_077_000, 11846)
			.saturating_add(RocksDbWeight::get().reads(6_u64))
			.saturating_add(RocksDbWeight::get().writes(1_u64))
	}
	/// Storage: `Timestamp::Now` (r:1 w:0)
	/// Proof: `Timestamp::Now` (`max_values`: Some(1), `max_size`: Some(8), added: 503, mode: `MaxEncodedLen`)
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::NodeIdByTwinID` (r:1 w:0)
	/// Proof: `TfgridModule::NodeIdByTwinID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Nodes` (r:1 w:0)
	/// Proof: `TfgridModule::Nodes` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn report_uptime() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `454`
		//  Estimated: `3919`
		// Minimum execution time: 18_786_000 picoseconds.
		Weight::from_parts(19_336_000, 3919)
			.saturating_add(RocksDbWeight::get().reads(4_u64))
	}
	/// Storage: `TfgridModule::Farms` (r:1 w:0)
	/// Proof: `TfgridModule::Farms` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Twins` (r:1 w:0)
	/// Proof: `TfgridModule::Twins` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Nodes` (r:1 w:1)
	/// Proof: `TfgridModule::Nodes` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn add_node_public_config() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `779`
		//  Estimated: `4244`
		// Minimum execution time: 24_206_000 picoseconds.
		Weight::from_parts(25_237_000, 4244)
			.saturating_add(RocksDbWeight::get().reads(3_u64))
			.saturating_add(RocksDbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::Nodes` (r:1 w:1)
	/// Proof: `TfgridModule::Nodes` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::NodesByFarmID` (r:1 w:1)
	/// Proof: `TfgridModule::NodesByFarmID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ActiveNodeContracts` (r:1 w:0)
	/// Proof: `SmartContractModule::ActiveNodeContracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ActiveRentContractForNode` (r:1 w:0)
	/// Proof: `SmartContractModule::ActiveRentContractForNode` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `Dao::FarmWeight` (r:1 w:1)
	/// Proof: `Dao::FarmWeight` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn delete_node() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `682`
		//  Estimated: `4147`
		// Minimum execution time: 27_802_000 picoseconds.
		Weight::from_parts(28_273_000, 4147)
			.saturating_add(RocksDbWeight::get().reads(6_u64))
			.saturating_add(RocksDbWeight::get().writes(3_u64))
	}
	/// Storage: `TfgridModule::UsersTermsAndConditions` (r:1 w:0)
	/// Proof: `TfgridModule::UsersTermsAndConditions` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:1)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::TwinID` (r:1 w:1)
	/// Proof: `TfgridModule::TwinID` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Twins` (r:0 w:1)
	/// Proof: `TfgridModule::Twins` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn create_twin() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `235`
		//  Estimated: `3700`
		// Minimum execution time: 14_217_000 picoseconds.
		Weight::from_parts(14_568_000, 3700)
			.saturating_add(RocksDbWeight::get().reads(3_u64))
			.saturating_add(RocksDbWeight::get().writes(3_u64))
	}
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Twins` (r:1 w:1)
	/// Proof: `TfgridModule::Twins` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn update_twin() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `428`
		//  Estimated: `3893`
		// Minimum execution time: 14_848_000 picoseconds.
		Weight::from_parts(15_440_000, 3893)
			.saturating_add(RocksDbWeight::get().reads(2_u64))
			.saturating_add(RocksDbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::PricingPolicyIdByName` (r:1 w:1)
	/// Proof: `TfgridModule::PricingPolicyIdByName` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::PricingPolicyID` (r:1 w:1)
	/// Proof: `TfgridModule::PricingPolicyID` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::PricingPolicies` (r:0 w:1)
	/// Proof: `TfgridModule::PricingPolicies` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn create_pricing_policy() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `134`
		//  Estimated: `3599`
		// Minimum execution time: 11_923_000 picoseconds.
		Weight::from_parts(12_324_000, 3599)
			.saturating_add(RocksDbWeight::get().reads(2_u64))
			.saturating_add(RocksDbWeight::get().writes(3_u64))
	}
	/// Storage: `TfgridModule::PricingPolicies` (r:1 w:1)
	/// Proof: `TfgridModule::PricingPolicies` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::PricingPolicyIdByName` (r:1 w:1)
	/// Proof: `TfgridModule::PricingPolicyIdByName` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::PricingPolicyID` (r:0 w:1)
	/// Proof: `TfgridModule::PricingPolicyID` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	fn update_pricing_policy() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `382`
		//  Estimated: `3847`
		// Minimum execution time: 16_441_000 picoseconds.
		Weight::from_parts(16_851_000, 3847)
			.saturating_add(RocksDbWeight::get().reads(2_u64))
			.saturating_add(RocksDbWeight::get().writes(3_u64))
	}
	/// Storage: `TfgridModule::FarmingPolicyID` (r:1 w:1)
	/// Proof: `TfgridModule::FarmingPolicyID` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::FarmingPoliciesMap` (r:0 w:1)
	/// Proof: `TfgridModule::FarmingPoliciesMap` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn create_farming_policy() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `134`
		//  Estimated: `1619`
		// Minimum execution time: 10_229_000 picoseconds.
		Weight::from_parts(10_560_000, 1619)
			.saturating_add(RocksDbWeight::get().reads(1_u64))
			.saturating_add(RocksDbWeight::get().writes(2_u64))
	}
	/// Storage: `Timestamp::Now` (r:1 w:0)
	/// Proof: `Timestamp::Now` (`max_values`: Some(1), `max_size`: Some(8), added: 503, mode: `MaxEncodedLen`)
	/// Storage: `TfgridModule::UsersTermsAndConditions` (r:1 w:1)
	/// Proof: `TfgridModule::UsersTermsAndConditions` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn user_accept_tc() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `140`
		//  Estimated: `3605`
		// Minimum execution time: 7_464_000 picoseconds.
		Weight::from_parts(7_694_000, 3605)
			.saturating_add(RocksDbWeight::get().reads(2_u64))
			.saturating_add(RocksDbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Nodes` (r:1 w:1)
	/// Proof: `TfgridModule::Nodes` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Farms` (r:1 w:0)
	/// Proof: `TfgridModule::Farms` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::NodesByFarmID` (r:1 w:1)
	/// Proof: `TfgridModule::NodesByFarmID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ActiveNodeContracts` (r:1 w:0)
	/// Proof: `SmartContractModule::ActiveNodeContracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ActiveRentContractForNode` (r:1 w:0)
	/// Proof: `SmartContractModule::ActiveRentContractForNode` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `Dao::FarmWeight` (r:1 w:1)
	/// Proof: `Dao::FarmWeight` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::NodeIdByTwinID` (r:0 w:1)
	/// Proof: `TfgridModule::NodeIdByTwinID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn delete_node_farm() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `828`
		//  Estimated: `4293`
		// Minimum execution time: 32_201_000 picoseconds.
		Weight::from_parts(32_992_000, 4293)
			.saturating_add(RocksDbWeight::get().reads(7_u64))
			.saturating_add(RocksDbWeight::get().writes(4_u64))
	}
	/// Storage: `TfgridModule::Farms` (r:1 w:1)
	/// Proof: `TfgridModule::Farms` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn set_farm_dedicated() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `412`
		//  Estimated: `3877`
		// Minimum execution time: 13_666_000 picoseconds.
		Weight::from_parts(13_986_000, 3877)
			.saturating_add(RocksDbWeight::get().reads(1_u64))
			.saturating_add(RocksDbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::Farms` (r:1 w:1)
	/// Proof: `TfgridModule::Farms` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn force_reset_farm_ip() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `412`
		//  Estimated: `3877`
		// Minimum execution time: 13_445_000 picoseconds.
		Weight::from_parts(13_836_000, 3877)
			.saturating_add(RocksDbWeight::get().reads(1_u64))
			.saturating_add(RocksDbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::ConnectionPrice` (r:0 w:1)
	/// Proof: `TfgridModule::ConnectionPrice` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	fn set_connection_price() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `0`
		//  Estimated: `0`
		// Minimum execution time: 5_570_000 picoseconds.
		Weight::from_parts(5_971_000, 0)
			.saturating_add(RocksDbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::AllowedNodeCertifiers` (r:1 w:1)
	/// Proof: `TfgridModule::AllowedNodeCertifiers` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	fn add_node_certifier() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `355`
		//  Estimated: `1840`
		// Minimum execution time: 9_608_000 picoseconds.
		Weight::from_parts(9_899_000, 1840)
			.saturating_add(RocksDbWeight::get().reads(1_u64))
			.saturating_add(RocksDbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::AllowedNodeCertifiers` (r:1 w:1)
	/// Proof: `TfgridModule::AllowedNodeCertifiers` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	fn remove_node_certifier() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `413`
		//  Estimated: `1898`
		// Minimum execution time: 11_913_000 picoseconds.
		Weight::from_parts(12_253_000, 1898)
			.saturating_add(RocksDbWeight::get().reads(1_u64))
			.saturating_add(RocksDbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::FarmingPoliciesMap` (r:1 w:1)
	/// Proof: `TfgridModule::FarmingPoliciesMap` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn update_farming_policy() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `294`
		//  Estimated: `3759`
		// Minimum execution time: 12_985_000 picoseconds.
		Weight::from_parts(13_155_000, 3759)
			.saturating_add(RocksDbWeight::get().reads(1_u64))
			.saturating_add(RocksDbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::FarmingPoliciesMap` (r:1 w:0)
	/// Proof: `TfgridModule::FarmingPoliciesMap` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Farms` (r:1 w:1)
	/// Proof: `TfgridModule::Farms` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::NodesByFarmID` (r:1 w:0)
	/// Proof: `TfgridModule::NodesByFarmID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn attach_policy_to_farm() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `572`
		//  Estimated: `4037`
		// Minimum execution time: 21_440_000 picoseconds.
		Weight::from_parts(22_112_000, 4037)
			.saturating_add(RocksDbWeight::get().reads(3_u64))
			.saturating_add(RocksDbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::ZosVersion` (r:1 w:1)
	/// Proof: `TfgridModule::ZosVersion` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	fn set_zos_version() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `134`
		//  Estimated: `1619`
		// Minimum execution time: 7_664_000 picoseconds.
		Weight::from_parts(8_085_000, 1619)
			.saturating_add(RocksDbWeight::get().reads(1_u64))
			.saturating_add(RocksDbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::NodeIdByTwinID` (r:1 w:0)
	/// Proof: `TfgridModule::NodeIdByTwinID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Nodes` (r:1 w:0)
	/// Proof: `TfgridModule::Nodes` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::NodePower` (r:1 w:1)
	/// Proof: `TfgridModule::NodePower` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn change_power_state() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `621`
		//  Estimated: `4086`
		// Minimum execution time: 22_663_000 picoseconds.
		Weight::from_parts(23_244_000, 4086)
			.saturating_add(RocksDbWeight::get().reads(4_u64))
			.saturating_add(RocksDbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Nodes` (r:1 w:0)
	/// Proof: `TfgridModule::Nodes` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Farms` (r:1 w:0)
	/// Proof: `TfgridModule::Farms` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ActiveNodeContracts` (r:1 w:0)
	/// Proof: `SmartContractModule::ActiveNodeContracts` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `SmartContractModule::ActiveRentContractForNode` (r:1 w:0)
	/// Proof: `SmartContractModule::ActiveRentContractForNode` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::NodePower` (r:1 w:1)
	/// Proof: `TfgridModule::NodePower` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn change_power_target() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `792`
		//  Estimated: `4257`
		// Minimum execution time: 25_729_000 picoseconds.
		Weight::from_parts(26_470_000, 4257)
			.saturating_add(RocksDbWeight::get().reads(6_u64))
			.saturating_add(RocksDbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::Twins` (r:1 w:0)
	/// Proof: `TfgridModule::Twins` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::TwinBoundedAccountID` (r:0 w:1)
	/// Proof: `TfgridModule::TwinBoundedAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn bond_twin_account() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `387`
		//  Estimated: `3852`
		// Minimum execution time: 11_743_000 picoseconds.
		Weight::from_parts(12_053_000, 3852)
			.saturating_add(RocksDbWeight::get().reads(1_u64))
			.saturating_add(RocksDbWeight::get().writes(1_u64))
	}
	/// Storage: `TfgridModule::TwinIdByAccountID` (r:1 w:0)
	/// Proof: `TfgridModule::TwinIdByAccountID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::NodeIdByTwinID` (r:1 w:0)
	/// Proof: `TfgridModule::NodeIdByTwinID` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TfgridModule::Nodes` (r:1 w:0)
	/// Proof: `TfgridModule::Nodes` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `Timestamp::Now` (r:1 w:0)
	/// Proof: `Timestamp::Now` (`max_values`: Some(1), `max_size`: Some(8), added: 503, mode: `MaxEncodedLen`)
	fn report_uptime_v2() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `454`
		//  Estimated: `3919`
		// Minimum execution time: 18_084_000 picoseconds.
		Weight::from_parts(18_465_000, 3919)
			.saturating_add(RocksDbWeight::get().reads(4_u64))
	}
}
