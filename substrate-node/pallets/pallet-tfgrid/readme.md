# Tfgrid Module

A pallet that serves as a registry for all the users, nodes, farmers on the Threefold Grid.

## Overview

The Tfgrid module provides functions for:

- Creating / Updating / a twin (digital representation of a physical person)
- Creating / Updating / a farm
- Creating / Updating a [ZOS](https://github.com/threefoldtech/zos) node
- Certifying nodes and adding / removing allowed certfiers
- Reporting node uptime
- Managing pricing policies
- Managing farming policies
- Setting the global ZOS version

## Terminology

- Twin: a digital representation of a physical person. A twin is linked to a user's keypair. A twin is unique per keypair.
- Farm: a digital representation of a physical farm. A farm is linked to a twins keypair. A farm is unique per twin. A farm can have a list of Public IPS which are routable on the internet.
- Node: a digital representation of a physical ZOS node (a machine running ZOS). Nodes are linked to a farm and a farm can have multiple nodes.
- Certification: A node can be marked as certified, meaning it's hardware is certified by a trusted party. This pallet allows for the certification of nodes. This node earns more farming rewards.
- Pricing Policy: A pricing policy is a set of prices for the usage of resources on a node. A pricing policy is linked to a farm. By default, a farm will have the in genesis configured pricing policy.
- Farming Policy: A farming policy is a set of rules that determine how much a farmer earns for farming (being online). A farming policy is linked to a farm. By default, a farm will have the in genesis configured farming policy.
- Entity: An entity is a digital representation of a physical entity (a company, a foundation, ...). An entity can have multiple twins.
- Farm Certification: A farm can be marked as certified, meaning it's adhering to a set of rules defined by Threefold. This farm earns more farming rewards.

For more information browse our [manual](https://manual.grid.tf/intro/intro_readme.html)

## Genesis Config

- `su_price_value`: The SU price value in mUSD/hour, this is the price of 1 SU in mUSD per hour
- `su_price_unit`: The unit of 1 SU (e.g. GB, TB, ...)
- `nu_price_value`: The NU price value in mUSD/hour, this is the price of 1 NU in mUSD per hour
- `nu_price_unit`: The unit of 1 NU (e.g. GB, TB, ...)
- `ipu_price_value`: The IPU price value in mUSD/hour, this is the price of 1 IPU in mUSD per hour
- `ipu_price_unit`: The unit of 1 IPU (e.g. GB, TB, ...)
- `cu_price_value`: The CU price value in mUSD/hour, this is the price of 1 CU in mUSD per hour
- `cu_price_unit`: The unit of 1 CU (e.g. GB, TB, ...)
- `domain_name_price_value`: The price of a domain name in mUSD per hour
- `unique_name_price_value`: The price of a unique name in mUSD per hour
- `foundation_account_id`: The account id of the foundation account, this account will receive all the fees paid by users
- `sales_account_id`: The account id of the sales account, this account will receive all the fees paid by users
- `discount_for_dedication_node`: The discount in % for prices for a dedicated node
- `farming_policy_diy_cu`: The amount of CU a DIY farmer earns per hour for being online
- `farming_policy_diy_nu`: The amount of NU a DIY farmer earns per hour for being online
- `farming_policy_diy_su`: The amount of SU a DIY farmer earns per hour for being online
- `farming_policy_diy_ipu`: The amount of IPU a DIY farmer earns per hour for being online
- `farming_policy_minimal_uptime`: The minimal uptime a DIY farmer needs to have to earn farming rewards
- `farming_policy_certified_cu`: The amount of CU a certified farmer earns per hour for being online
- `farming_policy_certified_nu`: The amount of NU a certified farmer earns per hour for being online
- `farming_policy_certified_su`: The amount of SU a certified farmer earns per hour for being online
- `farming_policy_certified_ipu`: The amount of IPU a certified farmer earns per hour for being online
- `farming_policy_minimal_uptime`: The minimal uptime a certified farmer needs to have to earn farming rewards
- `connection_price`: The price of TFT when connecting a node

## Interface

Dispatchable functions of this pallet.

- `set_storage_version`: Set the storage version, only callable by a configurable origin.
- `create_farm`: Create a farm
- `update_farm`: Update a farm
- `add_stellar_payout_v2address`: Add a stellar payout address to a farm
- `set_farm_certification`: Set the certification of a farm, only callable by a configurable origin.
- `add_farm_ip`: Add an Public IP to a farm
- `remove_farm_ip`: Remove a Public IP from a farm
- `create_node`: Creates a node
- `update_node`: Updates a node
- `set_node_certification`: Set the certification of a node, only callable by a configurable origin or allowed Node Certifiers.
- `report_uptime`: Report uptime for a node
- `add_node_public_config`: Add a public network config to a node
- `delete_node`: Delete a node
- `create_entity`: Create an entity
- `update_entity`: Update an entity
- `delete_entity`: Delete an entity
- `create_twin`: Create a twin
- `update_twin`: Update a twin
- `add_twin_entity`: Add an entity to a twin
- `delete_twin_entity`: Delete an entity from a twin
- `create_pricing_policy`: Create a pricing policy
- `update_pricing_policy`: Update a pricing policy
- `create_farming_policy`: Create a farming policy
- `user_accept_tc`: Accept the terms and conditions (must be called before creating a twin)
- `delete_node_farmer`: Delete a node with farmer's key
- `force_reset_farm_ip`: Force reset a farm's public IP, can only be called by a configurable origin.
- `set_connection_price`: Set the connection price, can only be called by a configurable origin.
- `add_node_certifier`: Add a node certifier, can only be called by a configurable origin.
- `remove_node_certifier`: Remove a node certifier, can only be called by a configurable origin.
- `update_farming_policy`: Update a farming policy, can only be called by a configurable origin.
- `attach_policy_to_farm`: Attaches a farming policy to a farm, can only be called by a configurable origin.
- `set_zos_version`: Set the global ZOS version, can only be called by a configurable origin.
- `change_power_state`: Change the power state of a node, can only be called by the node.
- `change_power_target`: Change the power target of a node, can only be called by the node.
- `bond_twin_account`: Bond an account to a twin.
- `report_uptime_v2`: Report uptime for a node, v2 version.