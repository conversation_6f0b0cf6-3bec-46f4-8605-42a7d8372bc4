
//! Autogenerated weights for pallet_validator
//!
//! THIS FILE WAS AUTO-GENERATED USING THE SUBSTRATE BENCHMARK CLI VERSION 4.0.0-dev
//! DATE: 2024-10-01, STEPS: `50`, REPEAT: `20`, LOW RANGE: `[]`, HIGH RANGE: `[]`
//! WORST CASE MAP SIZE: `1000000`
//! HOSTNAME: `585a9f003813`, CPU: `AMD Ryzen 7 5800X 8-Core Processor`
//! EXECUTION: , WASM-EXECUTION: Compiled, CHAIN: Some("dev"), DB CACHE: 1024

// Executed Command:
// ./target/production/tfchain
// benchmark
// pallet
// --chain=dev
// --wasm-execution=compiled
// --pallet=pallet-validator
// --extrinsic=*
// --steps=50
// --repeat=20
// --heap-pages=409
// --output
// ./pallets/pallet-validator/src/weights.rs
// --template
// ./.maintain/frame-weight-template.hbs

#![cfg_attr(rustfmt, rustfmt_skip)]
#![allow(unused_parens)]
#![allow(unused_imports)]
#![allow(missing_docs)]

use frame_support::{traits::Get, weights::{Weight, constants::RocksDbWeight}};
use core::marker::PhantomData;

/// Weight functions needed for pallet_validator.
pub trait WeightInfo {
	fn create_validator_request() -> Weight;
	fn activate_validator_node() -> Weight;
	fn change_validator_node_account() -> Weight;
	fn bond() -> Weight;
	fn approve_validator() -> Weight;
	fn remove_validator() -> Weight;
}

/// Weights for pallet_validator using the Substrate node and recommended hardware.
pub struct SubstrateWeight<T>(PhantomData<T>);
impl<T: frame_system::Config> WeightInfo for SubstrateWeight<T> {
	/// Storage: `Validator::Validator` (r:1 w:1)
	/// Proof: `Validator::Validator` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn create_validator_request() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `42`
		//  Estimated: `3507`
		// Minimum execution time: 8_877_000 picoseconds.
		Weight::from_parts(9_368_000, 3507)
			.saturating_add(T::DbWeight::get().reads(1_u64))
			.saturating_add(T::DbWeight::get().writes(1_u64))
	}
	/// Storage: `Validator::Validator` (r:1 w:1)
	/// Proof: `Validator::Validator` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `ValidatorSet::Validators` (r:1 w:1)
	/// Proof: `ValidatorSet::Validators` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `ValidatorSet::ApprovedValidators` (r:1 w:1)
	/// Proof: `ValidatorSet::ApprovedValidators` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	fn activate_validator_node() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `365`
		//  Estimated: `3830`
		// Minimum execution time: 24_096_000 picoseconds.
		Weight::from_parts(24_637_000, 3830)
			.saturating_add(T::DbWeight::get().reads(3_u64))
			.saturating_add(T::DbWeight::get().writes(3_u64))
	}
	/// Storage: `Validator::Validator` (r:1 w:1)
	/// Proof: `Validator::Validator` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `ValidatorSet::Validators` (r:1 w:1)
	/// Proof: `ValidatorSet::Validators` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `ValidatorSet::ApprovedValidators` (r:1 w:1)
	/// Proof: `ValidatorSet::ApprovedValidators` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	fn change_validator_node_account() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `431`
		//  Estimated: `3896`
		// Minimum execution time: 32_832_000 picoseconds.
		Weight::from_parts(33_994_000, 3896)
			.saturating_add(T::DbWeight::get().reads(3_u64))
			.saturating_add(T::DbWeight::get().writes(3_u64))
	}
	/// Storage: `Validator::Bonded` (r:1 w:1)
	/// Proof: `Validator::Bonded` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn bond() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `42`
		//  Estimated: `3507`
		// Minimum execution time: 8_286_000 picoseconds.
		Weight::from_parts(8_506_000, 3507)
			.saturating_add(T::DbWeight::get().reads(1_u64))
			.saturating_add(T::DbWeight::get().writes(1_u64))
	}
	/// Storage: `CouncilMembership::Members` (r:1 w:1)
	/// Proof: `CouncilMembership::Members` (`max_values`: Some(1), `max_size`: Some(3202), added: 3697, mode: `MaxEncodedLen`)
	/// Storage: `Validator::Validator` (r:1 w:1)
	/// Proof: `Validator::Validator` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `Council::Proposals` (r:1 w:0)
	/// Proof: `Council::Proposals` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `Council::Members` (r:0 w:1)
	/// Proof: `Council::Members` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `Council::Prime` (r:0 w:1)
	/// Proof: `Council::Prime` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	fn approve_validator() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `494`
		//  Estimated: `4687`
		// Minimum execution time: 24_105_000 picoseconds.
		Weight::from_parts(24_918_000, 4687)
			.saturating_add(T::DbWeight::get().reads(3_u64))
			.saturating_add(T::DbWeight::get().writes(4_u64))
	}
	/// Storage: `CouncilMembership::Members` (r:1 w:1)
	/// Proof: `CouncilMembership::Members` (`max_values`: Some(1), `max_size`: Some(3202), added: 3697, mode: `MaxEncodedLen`)
	/// Storage: `Validator::Validator` (r:1 w:1)
	/// Proof: `Validator::Validator` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `Council::Proposals` (r:1 w:0)
	/// Proof: `Council::Proposals` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `CouncilMembership::Prime` (r:1 w:0)
	/// Proof: `CouncilMembership::Prime` (`max_values`: Some(1), `max_size`: Some(32), added: 527, mode: `MaxEncodedLen`)
	/// Storage: `Council::Members` (r:0 w:1)
	/// Proof: `Council::Members` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `Council::Prime` (r:0 w:1)
	/// Proof: `Council::Prime` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	fn remove_validator() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `558`
		//  Estimated: `4687`
		// Minimum execution time: 21_501_000 picoseconds.
		Weight::from_parts(22_092_000, 4687)
			.saturating_add(T::DbWeight::get().reads(4_u64))
			.saturating_add(T::DbWeight::get().writes(4_u64))
	}
}

// For backwards compatibility and tests
impl WeightInfo for () {
	/// Storage: `Validator::Validator` (r:1 w:1)
	/// Proof: `Validator::Validator` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn create_validator_request() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `42`
		//  Estimated: `3507`
		// Minimum execution time: 8_877_000 picoseconds.
		Weight::from_parts(9_368_000, 3507)
			.saturating_add(RocksDbWeight::get().reads(1_u64))
			.saturating_add(RocksDbWeight::get().writes(1_u64))
	}
	/// Storage: `Validator::Validator` (r:1 w:1)
	/// Proof: `Validator::Validator` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `ValidatorSet::Validators` (r:1 w:1)
	/// Proof: `ValidatorSet::Validators` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `ValidatorSet::ApprovedValidators` (r:1 w:1)
	/// Proof: `ValidatorSet::ApprovedValidators` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	fn activate_validator_node() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `365`
		//  Estimated: `3830`
		// Minimum execution time: 24_096_000 picoseconds.
		Weight::from_parts(24_637_000, 3830)
			.saturating_add(RocksDbWeight::get().reads(3_u64))
			.saturating_add(RocksDbWeight::get().writes(3_u64))
	}
	/// Storage: `Validator::Validator` (r:1 w:1)
	/// Proof: `Validator::Validator` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `ValidatorSet::Validators` (r:1 w:1)
	/// Proof: `ValidatorSet::Validators` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `ValidatorSet::ApprovedValidators` (r:1 w:1)
	/// Proof: `ValidatorSet::ApprovedValidators` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	fn change_validator_node_account() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `431`
		//  Estimated: `3896`
		// Minimum execution time: 32_832_000 picoseconds.
		Weight::from_parts(33_994_000, 3896)
			.saturating_add(RocksDbWeight::get().reads(3_u64))
			.saturating_add(RocksDbWeight::get().writes(3_u64))
	}
	/// Storage: `Validator::Bonded` (r:1 w:1)
	/// Proof: `Validator::Bonded` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn bond() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `42`
		//  Estimated: `3507`
		// Minimum execution time: 8_286_000 picoseconds.
		Weight::from_parts(8_506_000, 3507)
			.saturating_add(RocksDbWeight::get().reads(1_u64))
			.saturating_add(RocksDbWeight::get().writes(1_u64))
	}
	/// Storage: `CouncilMembership::Members` (r:1 w:1)
	/// Proof: `CouncilMembership::Members` (`max_values`: Some(1), `max_size`: Some(3202), added: 3697, mode: `MaxEncodedLen`)
	/// Storage: `Validator::Validator` (r:1 w:1)
	/// Proof: `Validator::Validator` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `Council::Proposals` (r:1 w:0)
	/// Proof: `Council::Proposals` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `Council::Members` (r:0 w:1)
	/// Proof: `Council::Members` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `Council::Prime` (r:0 w:1)
	/// Proof: `Council::Prime` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	fn approve_validator() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `494`
		//  Estimated: `4687`
		// Minimum execution time: 24_105_000 picoseconds.
		Weight::from_parts(24_918_000, 4687)
			.saturating_add(RocksDbWeight::get().reads(3_u64))
			.saturating_add(RocksDbWeight::get().writes(4_u64))
	}
	/// Storage: `CouncilMembership::Members` (r:1 w:1)
	/// Proof: `CouncilMembership::Members` (`max_values`: Some(1), `max_size`: Some(3202), added: 3697, mode: `MaxEncodedLen`)
	/// Storage: `Validator::Validator` (r:1 w:1)
	/// Proof: `Validator::Validator` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `Council::Proposals` (r:1 w:0)
	/// Proof: `Council::Proposals` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `CouncilMembership::Prime` (r:1 w:0)
	/// Proof: `CouncilMembership::Prime` (`max_values`: Some(1), `max_size`: Some(32), added: 527, mode: `MaxEncodedLen`)
	/// Storage: `Council::Members` (r:0 w:1)
	/// Proof: `Council::Members` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `Council::Prime` (r:0 w:1)
	/// Proof: `Council::Prime` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	fn remove_validator() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `558`
		//  Estimated: `4687`
		// Minimum execution time: 21_501_000 picoseconds.
		Weight::from_parts(22_092_000, 4687)
			.saturating_add(RocksDbWeight::get().reads(4_u64))
			.saturating_add(RocksDbWeight::get().writes(4_u64))
	}
}
