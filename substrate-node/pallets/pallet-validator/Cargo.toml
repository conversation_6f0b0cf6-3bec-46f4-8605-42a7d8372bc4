[package]
authors.workspace = true
documentation.workspace = true
edition.workspace = true
homepage.workspace = true
license-file.workspace = true
readme.workspace = true
repository.workspace = true
version.workspace = true
name = "pallet-validator"
description = "validator registration pallet"

[package.metadata.docs.rs]
targets = ['x86_64-unknown-linux-gnu']

[dependencies]
parity-scale-codec = {workspace = true, features = ["derive"]}
scale-info = { workspace = true, features = ["derive"] }

sp-std.workspace = true
sp-runtime.workspace = true
frame-support.workspace = true
frame-system.workspace = true
pallet-membership.workspace = true
pallet-collective = { workspace = true, default-features = false }
pallet-session.workspace = true
sp-io.workspace = true

substrate-validator-set = { workspace = true, default-features = false }

# Benchmarking
frame-benchmarking = { workspace = true, optional = true }

[dev-dependencies]
sp-core.workspace = true

[features]
default = ['std']
std = [
    'parity-scale-codec/std',
    'sp-std/std',
    'sp-runtime/std',
    'frame-support/std',
    'frame-system/std',
	'frame-benchmarking/std',
    'substrate-validator-set/std',
    'pallet-membership/std',
    'pallet-collective/std',
    'pallet-session/std',
    'sp-io/std',
	'scale-info/std'
]
runtime-benchmarks = [
	"frame-benchmarking/runtime-benchmarks",
	"pallet-collective/runtime-benchmarks",
]
try-runtime = [
    "frame-support/try-runtime",
]