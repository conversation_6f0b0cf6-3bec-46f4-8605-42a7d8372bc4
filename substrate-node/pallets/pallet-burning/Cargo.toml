[package]
authors.workspace = true
documentation.workspace = true
edition.workspace = true
homepage.workspace = true
license-file.workspace = true
readme.workspace = true
repository.workspace = true
version.workspace = true
name = "pallet-burning"
description = "burning pallet"

[package.metadata.docs.rs]
targets = ["x86_64-unknown-linux-gnu"]

[dependencies]
# Substrate packages
parity-scale-codec = {workspace = true, features = ["derive"]}
scale-info = { workspace = true, features = ["derive"] }

pallet-balances.workspace = true
frame-support.workspace = true
frame-system.workspace = true
sp-runtime.workspace = true
sp-std.workspace = true
sp-storage.workspace = true
pallet-timestamp.workspace = true
sp-io.workspace = true

# Benchmarking
frame-benchmarking = { workspace = true, optional = true }

[dev-dependencies]
sp-core.workspace = true

[features]
default = ['std']
std = [
	'pallet-balances/std',
	'frame-support/std',
	'frame-system/std',
	'frame-benchmarking/std',
	'sp-runtime/std',
	'sp-std/std',
	'sp-storage/std',
    'pallet-timestamp/std',
	'parity-scale-codec/std',
	'sp-io/std',
	'scale-info/std'
]
runtime-benchmarks = [
	"frame-benchmarking/runtime-benchmarks",
]
try-runtime = [
    "frame-support/try-runtime",
]