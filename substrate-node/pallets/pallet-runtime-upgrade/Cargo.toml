[package]
authors.workspace = true
documentation.workspace = true
edition.workspace = true
homepage.workspace = true
license-file.workspace = true
readme.workspace = true
repository.workspace = true
version.workspace = true
name = "pallet-runtime-upgrade"
description = "pallet for runtime upgrades"

[package.metadata.docs.rs]
targets = ['x86_64-unknown-linux-gnu']

[dependencies]
# Substrate packages
parity-scale-codec = {workspace = true, features = ["derive"]}
scale-info = { workspace = true, features = ["derive"] }

frame-support.workspace = true
frame-system.workspace = true
sp-std.workspace = true
sp-io.workspace = true

[features]
default = ['std']
std = [
	'frame-support/std',
	'frame-system/std',
	'sp-std/std',
	'parity-scale-codec/std',
	'sp-io/std',
	'scale-info/std'
]
try-runtime = [
    "frame-support/try-runtime",
]