
//! Autogenerated weights for pallet_tft_bridge
//!
//! THIS FILE WAS AUTO-GENERATED USING THE SUBSTRATE BENCHMARK CLI VERSION 4.0.0-dev
//! DATE: 2024-10-01, STEPS: `50`, REPEAT: `20`, LOW RANGE: `[]`, HIGH RANGE: `[]`
//! WORST CASE MAP SIZE: `1000000`
//! HOSTNAME: `585a9f003813`, CPU: `AMD Ryzen 7 5800X 8-Core Processor`
//! EXECUTION: , WASM-EXECUTION: Compiled, CHAIN: Some("dev"), DB CACHE: 1024

// Executed Command:
// ./target/production/tfchain
// benchmark
// pallet
// --chain=dev
// --wasm-execution=compiled
// --pallet=pallet-tft-bridge
// --extrinsic=*
// --steps=50
// --repeat=20
// --heap-pages=409
// --output
// ./pallets/pallet-tft-bridge/src/weights.rs
// --template
// ./.maintain/frame-weight-template.hbs

#![cfg_attr(rustfmt, rustfmt_skip)]
#![allow(unused_parens)]
#![allow(unused_imports)]
#![allow(missing_docs)]

use frame_support::{traits::Get, weights::{Weight, constants::RocksDbWeight}};
use core::marker::PhantomData;

/// Weight functions needed for pallet_tft_bridge.
pub trait WeightInfo {
	fn add_bridge_validator() -> Weight;
	fn remove_bridge_validator() -> Weight;
	fn set_fee_account() -> Weight;
	fn set_withdraw_fee() -> Weight;
	fn set_deposit_fee() -> Weight;
	fn swap_to_stellar() -> Weight;
	fn propose_or_vote_mint_transaction() -> Weight;
	fn propose_burn_transaction_or_add_sig() -> Weight;
	fn set_burn_transaction_executed() -> Weight;
	fn create_refund_transaction_or_add_sig() -> Weight;
	fn set_refund_transaction_executed() -> Weight;
}

/// Weights for pallet_tft_bridge using the Substrate node and recommended hardware.
pub struct SubstrateWeight<T>(PhantomData<T>);
impl<T: frame_system::Config> WeightInfo for SubstrateWeight<T> {
	/// Storage: `TFTBridgeModule::Validators` (r:1 w:1)
	/// Proof: `TFTBridgeModule::Validators` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	fn add_bridge_validator() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `256`
		//  Estimated: `1741`
		// Minimum execution time: 7_324_000 picoseconds.
		Weight::from_parts(7_545_000, 1741)
			.saturating_add(T::DbWeight::get().reads(1_u64))
			.saturating_add(T::DbWeight::get().writes(1_u64))
	}
	/// Storage: `TFTBridgeModule::Validators` (r:1 w:1)
	/// Proof: `TFTBridgeModule::Validators` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	fn remove_bridge_validator() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `289`
		//  Estimated: `1774`
		// Minimum execution time: 7_154_000 picoseconds.
		Weight::from_parts(7_384_000, 1774)
			.saturating_add(T::DbWeight::get().reads(1_u64))
			.saturating_add(T::DbWeight::get().writes(1_u64))
	}
	/// Storage: `TFTBridgeModule::FeeAccount` (r:0 w:1)
	/// Proof: `TFTBridgeModule::FeeAccount` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	fn set_fee_account() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `0`
		//  Estimated: `0`
		// Minimum execution time: 2_484_000 picoseconds.
		Weight::from_parts(2_575_000, 0)
			.saturating_add(T::DbWeight::get().writes(1_u64))
	}
	/// Storage: `TFTBridgeModule::WithdrawFee` (r:0 w:1)
	/// Proof: `TFTBridgeModule::WithdrawFee` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	fn set_withdraw_fee() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `0`
		//  Estimated: `0`
		// Minimum execution time: 2_324_000 picoseconds.
		Weight::from_parts(2_435_000, 0)
			.saturating_add(T::DbWeight::get().writes(1_u64))
	}
	/// Storage: `TFTBridgeModule::DepositFee` (r:0 w:1)
	/// Proof: `TFTBridgeModule::DepositFee` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	fn set_deposit_fee() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `0`
		//  Estimated: `0`
		// Minimum execution time: 2_275_000 picoseconds.
		Weight::from_parts(2_505_000, 0)
			.saturating_add(T::DbWeight::get().writes(1_u64))
	}
	/// Storage: `TFTBridgeModule::WithdrawFee` (r:1 w:0)
	/// Proof: `TFTBridgeModule::WithdrawFee` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `TFTBridgeModule::FeeAccount` (r:1 w:0)
	/// Proof: `TFTBridgeModule::FeeAccount` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `System::Account` (r:1 w:1)
	/// Proof: `System::Account` (`max_values`: None, `max_size`: Some(128), added: 2603, mode: `MaxEncodedLen`)
	/// Storage: `TFTBridgeModule::BurnTransactionID` (r:1 w:1)
	/// Proof: `TFTBridgeModule::BurnTransactionID` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `TFTBridgeModule::BurnTransactions` (r:0 w:1)
	/// Proof: `TFTBridgeModule::BurnTransactions` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn swap_to_stellar() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `253`
		//  Estimated: `3593`
		// Minimum execution time: 43_462_000 picoseconds.
		Weight::from_parts(44_163_000, 3593)
			.saturating_add(T::DbWeight::get().reads(4_u64))
			.saturating_add(T::DbWeight::get().writes(3_u64))
	}
	/// Storage: `TFTBridgeModule::Validators` (r:1 w:0)
	/// Proof: `TFTBridgeModule::Validators` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `TFTBridgeModule::ExecutedMintTransactions` (r:1 w:1)
	/// Proof: `TFTBridgeModule::ExecutedMintTransactions` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TFTBridgeModule::MintTransactions` (r:1 w:1)
	/// Proof: `TFTBridgeModule::MintTransactions` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TFTBridgeModule::DepositFee` (r:1 w:0)
	/// Proof: `TFTBridgeModule::DepositFee` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `TFTBridgeModule::FeeAccount` (r:1 w:0)
	/// Proof: `TFTBridgeModule::FeeAccount` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `System::Account` (r:1 w:1)
	/// Proof: `System::Account` (`max_values`: None, `max_size`: Some(128), added: 2603, mode: `MaxEncodedLen`)
	fn propose_or_vote_mint_transaction() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `499`
		//  Estimated: `3964`
		// Minimum execution time: 60_224_000 picoseconds.
		Weight::from_parts(61_235_000, 3964)
			.saturating_add(T::DbWeight::get().reads(6_u64))
			.saturating_add(T::DbWeight::get().writes(3_u64))
	}
	/// Storage: `TFTBridgeModule::Validators` (r:1 w:0)
	/// Proof: `TFTBridgeModule::Validators` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `TFTBridgeModule::ExecutedBurnTransactions` (r:1 w:0)
	/// Proof: `TFTBridgeModule::ExecutedBurnTransactions` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TFTBridgeModule::BurnTransactions` (r:1 w:1)
	/// Proof: `TFTBridgeModule::BurnTransactions` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn propose_burn_transaction_or_add_sig() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `631`
		//  Estimated: `4096`
		// Minimum execution time: 25_489_000 picoseconds.
		Weight::from_parts(26_390_000, 4096)
			.saturating_add(T::DbWeight::get().reads(3_u64))
			.saturating_add(T::DbWeight::get().writes(1_u64))
	}
	/// Storage: `TFTBridgeModule::Validators` (r:1 w:0)
	/// Proof: `TFTBridgeModule::Validators` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `TFTBridgeModule::ExecutedBurnTransactions` (r:1 w:1)
	/// Proof: `TFTBridgeModule::ExecutedBurnTransactions` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TFTBridgeModule::BurnTransactions` (r:1 w:1)
	/// Proof: `TFTBridgeModule::BurnTransactions` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn set_burn_transaction_executed() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `571`
		//  Estimated: `4036`
		// Minimum execution time: 18_204_000 picoseconds.
		Weight::from_parts(18_515_000, 4036)
			.saturating_add(T::DbWeight::get().reads(3_u64))
			.saturating_add(T::DbWeight::get().writes(2_u64))
	}
	/// Storage: `TFTBridgeModule::Validators` (r:1 w:0)
	/// Proof: `TFTBridgeModule::Validators` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `TFTBridgeModule::RefundTransactions` (r:1 w:1)
	/// Proof: `TFTBridgeModule::RefundTransactions` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn create_refund_transaction_or_add_sig() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `385`
		//  Estimated: `3850`
		// Minimum execution time: 21_551_000 picoseconds.
		Weight::from_parts(21_821_000, 3850)
			.saturating_add(T::DbWeight::get().reads(2_u64))
			.saturating_add(T::DbWeight::get().writes(1_u64))
	}
	/// Storage: `TFTBridgeModule::Validators` (r:1 w:0)
	/// Proof: `TFTBridgeModule::Validators` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `TFTBridgeModule::ExecutedRefundTransactions` (r:1 w:1)
	/// Proof: `TFTBridgeModule::ExecutedRefundTransactions` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TFTBridgeModule::RefundTransactions` (r:1 w:1)
	/// Proof: `TFTBridgeModule::RefundTransactions` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn set_refund_transaction_executed() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `560`
		//  Estimated: `4025`
		// Minimum execution time: 18_625_000 picoseconds.
		Weight::from_parts(18_946_000, 4025)
			.saturating_add(T::DbWeight::get().reads(3_u64))
			.saturating_add(T::DbWeight::get().writes(2_u64))
	}
}

// For backwards compatibility and tests
impl WeightInfo for () {
	/// Storage: `TFTBridgeModule::Validators` (r:1 w:1)
	/// Proof: `TFTBridgeModule::Validators` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	fn add_bridge_validator() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `256`
		//  Estimated: `1741`
		// Minimum execution time: 7_324_000 picoseconds.
		Weight::from_parts(7_545_000, 1741)
			.saturating_add(RocksDbWeight::get().reads(1_u64))
			.saturating_add(RocksDbWeight::get().writes(1_u64))
	}
	/// Storage: `TFTBridgeModule::Validators` (r:1 w:1)
	/// Proof: `TFTBridgeModule::Validators` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	fn remove_bridge_validator() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `289`
		//  Estimated: `1774`
		// Minimum execution time: 7_154_000 picoseconds.
		Weight::from_parts(7_384_000, 1774)
			.saturating_add(RocksDbWeight::get().reads(1_u64))
			.saturating_add(RocksDbWeight::get().writes(1_u64))
	}
	/// Storage: `TFTBridgeModule::FeeAccount` (r:0 w:1)
	/// Proof: `TFTBridgeModule::FeeAccount` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	fn set_fee_account() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `0`
		//  Estimated: `0`
		// Minimum execution time: 2_484_000 picoseconds.
		Weight::from_parts(2_575_000, 0)
			.saturating_add(RocksDbWeight::get().writes(1_u64))
	}
	/// Storage: `TFTBridgeModule::WithdrawFee` (r:0 w:1)
	/// Proof: `TFTBridgeModule::WithdrawFee` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	fn set_withdraw_fee() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `0`
		//  Estimated: `0`
		// Minimum execution time: 2_324_000 picoseconds.
		Weight::from_parts(2_435_000, 0)
			.saturating_add(RocksDbWeight::get().writes(1_u64))
	}
	/// Storage: `TFTBridgeModule::DepositFee` (r:0 w:1)
	/// Proof: `TFTBridgeModule::DepositFee` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	fn set_deposit_fee() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `0`
		//  Estimated: `0`
		// Minimum execution time: 2_275_000 picoseconds.
		Weight::from_parts(2_505_000, 0)
			.saturating_add(RocksDbWeight::get().writes(1_u64))
	}
	/// Storage: `TFTBridgeModule::WithdrawFee` (r:1 w:0)
	/// Proof: `TFTBridgeModule::WithdrawFee` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `TFTBridgeModule::FeeAccount` (r:1 w:0)
	/// Proof: `TFTBridgeModule::FeeAccount` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `System::Account` (r:1 w:1)
	/// Proof: `System::Account` (`max_values`: None, `max_size`: Some(128), added: 2603, mode: `MaxEncodedLen`)
	/// Storage: `TFTBridgeModule::BurnTransactionID` (r:1 w:1)
	/// Proof: `TFTBridgeModule::BurnTransactionID` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `TFTBridgeModule::BurnTransactions` (r:0 w:1)
	/// Proof: `TFTBridgeModule::BurnTransactions` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn swap_to_stellar() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `253`
		//  Estimated: `3593`
		// Minimum execution time: 43_462_000 picoseconds.
		Weight::from_parts(44_163_000, 3593)
			.saturating_add(RocksDbWeight::get().reads(4_u64))
			.saturating_add(RocksDbWeight::get().writes(3_u64))
	}
	/// Storage: `TFTBridgeModule::Validators` (r:1 w:0)
	/// Proof: `TFTBridgeModule::Validators` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `TFTBridgeModule::ExecutedMintTransactions` (r:1 w:1)
	/// Proof: `TFTBridgeModule::ExecutedMintTransactions` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TFTBridgeModule::MintTransactions` (r:1 w:1)
	/// Proof: `TFTBridgeModule::MintTransactions` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TFTBridgeModule::DepositFee` (r:1 w:0)
	/// Proof: `TFTBridgeModule::DepositFee` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `TFTBridgeModule::FeeAccount` (r:1 w:0)
	/// Proof: `TFTBridgeModule::FeeAccount` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `System::Account` (r:1 w:1)
	/// Proof: `System::Account` (`max_values`: None, `max_size`: Some(128), added: 2603, mode: `MaxEncodedLen`)
	fn propose_or_vote_mint_transaction() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `499`
		//  Estimated: `3964`
		// Minimum execution time: 60_224_000 picoseconds.
		Weight::from_parts(61_235_000, 3964)
			.saturating_add(RocksDbWeight::get().reads(6_u64))
			.saturating_add(RocksDbWeight::get().writes(3_u64))
	}
	/// Storage: `TFTBridgeModule::Validators` (r:1 w:0)
	/// Proof: `TFTBridgeModule::Validators` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `TFTBridgeModule::ExecutedBurnTransactions` (r:1 w:0)
	/// Proof: `TFTBridgeModule::ExecutedBurnTransactions` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TFTBridgeModule::BurnTransactions` (r:1 w:1)
	/// Proof: `TFTBridgeModule::BurnTransactions` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn propose_burn_transaction_or_add_sig() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `631`
		//  Estimated: `4096`
		// Minimum execution time: 25_489_000 picoseconds.
		Weight::from_parts(26_390_000, 4096)
			.saturating_add(RocksDbWeight::get().reads(3_u64))
			.saturating_add(RocksDbWeight::get().writes(1_u64))
	}
	/// Storage: `TFTBridgeModule::Validators` (r:1 w:0)
	/// Proof: `TFTBridgeModule::Validators` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `TFTBridgeModule::ExecutedBurnTransactions` (r:1 w:1)
	/// Proof: `TFTBridgeModule::ExecutedBurnTransactions` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TFTBridgeModule::BurnTransactions` (r:1 w:1)
	/// Proof: `TFTBridgeModule::BurnTransactions` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn set_burn_transaction_executed() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `571`
		//  Estimated: `4036`
		// Minimum execution time: 18_204_000 picoseconds.
		Weight::from_parts(18_515_000, 4036)
			.saturating_add(RocksDbWeight::get().reads(3_u64))
			.saturating_add(RocksDbWeight::get().writes(2_u64))
	}
	/// Storage: `TFTBridgeModule::Validators` (r:1 w:0)
	/// Proof: `TFTBridgeModule::Validators` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `TFTBridgeModule::RefundTransactions` (r:1 w:1)
	/// Proof: `TFTBridgeModule::RefundTransactions` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn create_refund_transaction_or_add_sig() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `385`
		//  Estimated: `3850`
		// Minimum execution time: 21_551_000 picoseconds.
		Weight::from_parts(21_821_000, 3850)
			.saturating_add(RocksDbWeight::get().reads(2_u64))
			.saturating_add(RocksDbWeight::get().writes(1_u64))
	}
	/// Storage: `TFTBridgeModule::Validators` (r:1 w:0)
	/// Proof: `TFTBridgeModule::Validators` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `TFTBridgeModule::ExecutedRefundTransactions` (r:1 w:1)
	/// Proof: `TFTBridgeModule::ExecutedRefundTransactions` (`max_values`: None, `max_size`: None, mode: `Measured`)
	/// Storage: `TFTBridgeModule::RefundTransactions` (r:1 w:1)
	/// Proof: `TFTBridgeModule::RefundTransactions` (`max_values`: None, `max_size`: None, mode: `Measured`)
	fn set_refund_transaction_executed() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `560`
		//  Estimated: `4025`
		// Minimum execution time: 18_625_000 picoseconds.
		Weight::from_parts(18_946_000, 4025)
			.saturating_add(RocksDbWeight::get().reads(3_u64))
			.saturating_add(RocksDbWeight::get().writes(2_u64))
	}
}
