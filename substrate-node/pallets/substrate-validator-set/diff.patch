diff --git a/src/lib.rs b/src/lib.rs
index 4f65d9c..67a3cb7 100644
--- a/src/lib.rs
+++ b/src/lib.rs
@@ -25,9 +25,6 @@ pub mod pallet {
 	pub trait Config: frame_system::Config + pallet_session::Config {
 		/// Because this pallet emits events, it depends on the runtime's definition of an event
 		type Event: From<Event<Self>> + IsType<<Self as frame_system::Config>::Event>;
-
-		/// Origin for adding or removing a validator.
-		type ExternalOrigin: EnsureOrigin<Self::RuntimeOrigin>;
 	}
 
 	#[pallet::pallet]
@@ -89,7 +86,7 @@ pub mod pallet {
 		/// New validator's session keys should be set in session module before calling this.
 		#[pallet::weight(0)]
 		pub fn add_validator(origin: OriginFor<T>, validator_id: T::AccountId) -> DispatchResultWithPostInfo {
-			T::ExternalOrigin::ensure_origin(origin)?;
+			ensure_root(origin)?;
 
 			let mut validators: Vec<T::AccountId>;
 
@@ -113,7 +110,7 @@ pub mod pallet {
 		/// Remove a validator using root/sudo privileges.
 		#[pallet::weight(0)]
 		pub fn remove_validator(origin: OriginFor<T>, validator_id: T::AccountId) -> DispatchResultWithPostInfo {
-			T::ExternalOrigin::ensure_origin(origin)?;
+			ensure_root(origin)?;
 			let mut validators = <Validators<T>>::get().ok_or(Error::<T>::NoValidators)?;
 
 			// Assuming that this will be a PoA network for enterprise use-cases,