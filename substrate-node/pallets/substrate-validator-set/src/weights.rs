
//! Autogenerated weights for substrate_validator_set
//!
//! THIS FILE WAS AUTO-GENERATED USING THE SUBSTRATE BENCHMARK CLI VERSION 4.0.0-dev
//! DATE: 2024-10-01, STEPS: `50`, REPEAT: `20`, LOW RANGE: `[]`, HIGH RANGE: `[]`
//! WORST CASE MAP SIZE: `1000000`
//! HOSTNAME: `585a9f003813`, CPU: `AMD Ryzen 7 5800X 8-Core Processor`
//! EXECUTION: , WASM-EXECUTION: Compiled, CHAIN: Some("dev"), DB CACHE: 1024

// Executed Command:
// ./target/production/tfchain
// benchmark
// pallet
// --chain=dev
// --wasm-execution=compiled
// --pallet=substrate-validator-set
// --extrinsic=*
// --steps=50
// --repeat=20
// --heap-pages=409
// --output
// ./pallets/substrate-validator-set/src/weights.rs
// --template
// ./.maintain/frame-weight-template.hbs

#![cfg_attr(rustfmt, rustfmt_skip)]
#![allow(unused_parens)]
#![allow(unused_imports)]
#![allow(missing_docs)]

use frame_support::{traits::Get, weights::{Weight, constants::RocksDbWeight}};
use core::marker::PhantomData;

/// Weight functions needed for substrate_validator_set.
pub trait WeightInfo {
	fn add_validator() -> Weight;
	fn remove_validator() -> Weight;
	fn add_validator_again() -> Weight;
}

/// Weights for substrate_validator_set using the Substrate node and recommended hardware.
pub struct SubstrateWeight<T>(PhantomData<T>);
impl<T: frame_system::Config> WeightInfo for SubstrateWeight<T> {
	/// Storage: `ValidatorSet::Validators` (r:1 w:1)
	/// Proof: `ValidatorSet::Validators` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `ValidatorSet::ApprovedValidators` (r:1 w:1)
	/// Proof: `ValidatorSet::ApprovedValidators` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	fn add_validator() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `139`
		//  Estimated: `1624`
		// Minimum execution time: 13_986_000 picoseconds.
		Weight::from_parts(14_347_000, 1624)
			.saturating_add(T::DbWeight::get().reads(2_u64))
			.saturating_add(T::DbWeight::get().writes(2_u64))
	}
	/// Storage: `ValidatorSet::Validators` (r:1 w:1)
	/// Proof: `ValidatorSet::Validators` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `ValidatorSet::ApprovedValidators` (r:1 w:0)
	/// Proof: `ValidatorSet::ApprovedValidators` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	fn remove_validator() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `205`
		//  Estimated: `1690`
		// Minimum execution time: 11_381_000 picoseconds.
		Weight::from_parts(11_673_000, 1690)
			.saturating_add(T::DbWeight::get().reads(2_u64))
			.saturating_add(T::DbWeight::get().writes(1_u64))
	}
	/// Storage: `ValidatorSet::ApprovedValidators` (r:1 w:0)
	/// Proof: `ValidatorSet::ApprovedValidators` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `ValidatorSet::Validators` (r:1 w:1)
	/// Proof: `ValidatorSet::Validators` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	fn add_validator_again() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `172`
		//  Estimated: `1657`
		// Minimum execution time: 12_584_000 picoseconds.
		Weight::from_parts(12_894_000, 1657)
			.saturating_add(T::DbWeight::get().reads(2_u64))
			.saturating_add(T::DbWeight::get().writes(1_u64))
	}
}

// For backwards compatibility and tests
impl WeightInfo for () {
	/// Storage: `ValidatorSet::Validators` (r:1 w:1)
	/// Proof: `ValidatorSet::Validators` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `ValidatorSet::ApprovedValidators` (r:1 w:1)
	/// Proof: `ValidatorSet::ApprovedValidators` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	fn add_validator() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `139`
		//  Estimated: `1624`
		// Minimum execution time: 13_986_000 picoseconds.
		Weight::from_parts(14_347_000, 1624)
			.saturating_add(RocksDbWeight::get().reads(2_u64))
			.saturating_add(RocksDbWeight::get().writes(2_u64))
	}
	/// Storage: `ValidatorSet::Validators` (r:1 w:1)
	/// Proof: `ValidatorSet::Validators` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `ValidatorSet::ApprovedValidators` (r:1 w:0)
	/// Proof: `ValidatorSet::ApprovedValidators` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	fn remove_validator() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `205`
		//  Estimated: `1690`
		// Minimum execution time: 11_381_000 picoseconds.
		Weight::from_parts(11_673_000, 1690)
			.saturating_add(RocksDbWeight::get().reads(2_u64))
			.saturating_add(RocksDbWeight::get().writes(1_u64))
	}
	/// Storage: `ValidatorSet::ApprovedValidators` (r:1 w:0)
	/// Proof: `ValidatorSet::ApprovedValidators` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	/// Storage: `ValidatorSet::Validators` (r:1 w:1)
	/// Proof: `ValidatorSet::Validators` (`max_values`: Some(1), `max_size`: None, mode: `Measured`)
	fn add_validator_again() -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `172`
		//  Estimated: `1657`
		// Minimum execution time: 12_584_000 picoseconds.
		Weight::from_parts(12_894_000, 1657)
			.saturating_add(RocksDbWeight::get().reads(2_u64))
			.saturating_add(RocksDbWeight::get().writes(1_u64))
	}
}
