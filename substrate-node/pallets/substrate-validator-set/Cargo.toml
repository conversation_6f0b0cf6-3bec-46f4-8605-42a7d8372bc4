[package]
authors.workspace = true
documentation.workspace = true
edition.workspace = true
homepage.workspace = true
license-file.workspace = true
readme.workspace = true
repository.workspace = true
version.workspace = true
name = "substrate-validator-set"
description = "substrate validator set pallet"

[dependencies]
parity-scale-codec = {workspace = true, features = ["derive"]}
scale-info = { workspace = true, features = ["derive"] }
log.workspace = true
serde = { features = ['derive'], optional = true, workspace = true }

sp-std.workspace = true
sp-runtime.workspace = true
sp-staking.workspace = true
frame-support.workspace = true
frame-system.workspace = true
pallet-session.workspace = true
sp-io.workspace = true
sp-weights.workspace = true
sp-state-machine.workspace = true
frame-benchmarking.workspace = true

[dev-dependencies]
sp-core.workspace = true

[features]
default = ['std']
std = [
    'parity-scale-codec/std',
    'sp-std/std',
    'sp-runtime/std',
    'frame-support/std',
	'frame-benchmarking/std',
    'sp-staking/std',
    'serde',
    'frame-system/std',
    'pallet-session/std',
    'sp-io/std',
    'scale-info/std',
    'log/std'
]
runtime-benchmarks = [
	"frame-benchmarking/runtime-benchmarks",
]
try-runtime = [
    "frame-support/try-runtime",
]