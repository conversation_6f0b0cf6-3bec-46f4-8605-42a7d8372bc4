# This file is automatically @generated by Car<PERSON>.
# It is not intended for manual editing.
version = 3

[[package]]
name = "Inflector"
version = "0.11.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fe438c63458706e03479442743baae6c88256498e6431708f6dfc520a26515d3"
dependencies = [
 "lazy_static",
 "regex",
]

[[package]]
name = "addr2line"
version = "0.19.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a76fd60b23679b7d19bd066031410fb7e458ccc5e958eb5c325888ce4baedc97"
dependencies = [
 "gimli 0.27.3",
]

[[package]]
name = "addr2line"
version = "0.21.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8a30b2e23b9e17a9f90641c7ab1549cd9b44f296d3ccbf309d2863cfe398a0cb"
dependencies = [
 "gimli 0.28.1",
]

[[package]]
name = "adler"
version = "1.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f26201604c87b1e01bd3d98f8d5d9a8fcbb815e8cedb41ffccbeb4bf593a35fe"

[[package]]
name = "aead"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d122413f284cf2d62fb1b7db97e02edb8cda96d769b16e443a4f6195e35662b0"
dependencies = [
 "crypto-common",
 "generic-array 0.14.7",
]

[[package]]
name = "aes"
version = "0.8.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ac1f845298e95f983ff1944b728ae08b8cebab80d684f0a832ed0fc74dfa27e2"
dependencies = [
 "cfg-if",
 "cipher",
 "cpufeatures",
]

[[package]]
name = "aes-gcm"
version = "0.10.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "831010a0f742e1209b3bcea8fab6a8e149051ba6099432c8cb2cc117dec3ead1"
dependencies = [
 "aead",
 "aes",
 "cipher",
 "ctr",
 "ghash",
 "subtle",
]

[[package]]
name = "ahash"
version = "0.7.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5a824f2aa7e75a0c98c5a504fceb80649e9c35265d44525b5f94de4771a395cd"
dependencies = [
 "getrandom 0.2.12",
 "once_cell",
 "version_check",
]

[[package]]
name = "ahash"
version = "0.8.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "77c3a9648d43b9cd48db467b3f87fdd6e146bcc88ab0180006cef2179fe11d01"
dependencies = [
 "cfg-if",
 "getrandom 0.2.12",
 "once_cell",
 "version_check",
 "zerocopy",
]

[[package]]
name = "aho-corasick"
version = "1.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b2969dcb958b36655471fc61f7e416fa76033bdd4bfed0678d8fee1e2d07a1f0"
dependencies = [
 "memchr",
]

[[package]]
name = "android-tzdata"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e999941b234f3131b00bc13c22d06e8c5ff726d1b6318ac7eb276997bbb4fef0"

[[package]]
name = "android_system_properties"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "819e7219dbd41043ac279b19830f2efc897156490d7fd6ea916720117ee66311"
dependencies = [
 "libc",
]

[[package]]
name = "ansi_term"
version = "0.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d52a9bb7ec0cf484c551830a7ce27bd20d67eac647e1befb56b0be4ee39a55d2"
dependencies = [
 "winapi",
]

[[package]]
name = "anstream"
version = "0.6.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6e2e1ebcb11de5c03c67de28a7df593d32191b44939c482e97702baaaa6ab6a5"
dependencies = [
 "anstyle",
 "anstyle-parse",
 "anstyle-query",
 "anstyle-wincon",
 "colorchoice",
 "utf8parse",
]

[[package]]
name = "anstyle"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7079075b41f533b8c61d2a4d073c4676e1f8b249ff94a393b0595db304e0dd87"

[[package]]
name = "anstyle-parse"
version = "0.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c75ac65da39e5fe5ab759307499ddad880d724eed2f6ce5b5e8a26f4f387928c"
dependencies = [
 "utf8parse",
]

[[package]]
name = "anstyle-query"
version = "1.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e28923312444cdd728e4738b3f9c9cac739500909bb3d3c94b43551b16517648"
dependencies = [
 "windows-sys 0.52.0",
]

[[package]]
name = "anstyle-wincon"
version = "3.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1cd54b81ec8d6180e24654d0b371ad22fc3dd083b6ff8ba325b72e00c87660a7"
dependencies = [
 "anstyle",
 "windows-sys 0.52.0",
]

[[package]]
name = "anyhow"
version = "1.0.79"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "080e9890a082662b09c1ad45f567faeeb47f22b5fb23895fbe1e651e718e25ca"

[[package]]
name = "approx"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cab112f0a86d568ea0e627cc1d6be74a1e9cd55214684db5561995f6dad897c6"
dependencies = [
 "num-traits",
]

[[package]]
name = "aquamarine"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d1da02abba9f9063d786eab1509833ebb2fac0f966862ca59439c76b9c566760"
dependencies = [
 "include_dir",
 "itertools",
 "proc-macro-error",
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "ark-bls12-381"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c775f0d12169cba7aae4caeb547bb6a50781c7449a8aa53793827c9ec4abf488"
dependencies = [
 "ark-ec",
 "ark-ff",
 "ark-serialize",
 "ark-std",
]

[[package]]
name = "ark-ec"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "defd9a439d56ac24968cca0571f598a61bc8c55f71d50a89cda591cb750670ba"
dependencies = [
 "ark-ff",
 "ark-poly",
 "ark-serialize",
 "ark-std",
 "derivative",
 "hashbrown 0.13.2",
 "itertools",
 "num-traits",
 "zeroize",
]

[[package]]
name = "ark-ed-on-bls12-381-bandersnatch"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f9cde0f2aa063a2a5c28d39b47761aa102bda7c13c84fc118a61b87c7b2f785c"
dependencies = [
 "ark-bls12-381",
 "ark-ec",
 "ark-ff",
 "ark-std",
]

[[package]]
name = "ark-ff"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ec847af850f44ad29048935519032c33da8aa03340876d351dfab5660d2966ba"
dependencies = [
 "ark-ff-asm",
 "ark-ff-macros",
 "ark-serialize",
 "ark-std",
 "derivative",
 "digest 0.10.7",
 "itertools",
 "num-bigint",
 "num-traits",
 "paste 1.0.14",
 "rustc_version",
 "zeroize",
]

[[package]]
name = "ark-ff-asm"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3ed4aa4fe255d0bc6d79373f7e31d2ea147bcf486cba1be5ba7ea85abdb92348"
dependencies = [
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "ark-ff-macros"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7abe79b0e4288889c4574159ab790824d0033b9fdcb2a112a3182fac2e514565"
dependencies = [
 "num-bigint",
 "num-traits",
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "ark-poly"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d320bfc44ee185d899ccbadfa8bc31aab923ce1558716e1997a1e74057fe86bf"
dependencies = [
 "ark-ff",
 "ark-serialize",
 "ark-std",
 "derivative",
 "hashbrown 0.13.2",
]

[[package]]
name = "ark-scale"
version = "0.0.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "49b08346a3e38e2be792ef53ee168623c9244d968ff00cd70fb9932f6fe36393"
dependencies = [
 "ark-ec",
 "ark-ff",
 "ark-serialize",
 "ark-std",
 "parity-scale-codec 3.6.9",
]

[[package]]
name = "ark-scale"
version = "0.0.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5f69c00b3b529be29528a6f2fd5fa7b1790f8bed81b9cdca17e326538545a179"
dependencies = [
 "ark-ec",
 "ark-ff",
 "ark-serialize",
 "ark-std",
 "parity-scale-codec 3.6.9",
 "scale-info",
]

[[package]]
name = "ark-secret-scalar"
version = "0.0.2"
source = "git+https://github.com/w3f/ring-vrf?rev=3119f51#3119f51b54b69308abfb0671f6176cb125ae1bf1"
dependencies = [
 "ark-ec",
 "ark-ff",
 "ark-serialize",
 "ark-std",
 "ark-transcript",
 "digest 0.10.7",
 "rand_core 0.6.4",
 "zeroize",
]

[[package]]
name = "ark-serialize"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "adb7b85a02b83d2f22f89bd5cac66c9c89474240cb6207cb1efc16d098e822a5"
dependencies = [
 "ark-serialize-derive",
 "ark-std",
 "digest 0.10.7",
 "num-bigint",
]

[[package]]
name = "ark-serialize-derive"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ae3281bc6d0fd7e549af32b52511e1302185bd688fd3359fa36423346ff682ea"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "ark-std"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "94893f1e0c6eeab764ade8dc4c0db24caf4fe7cbbaafc0eba0a9030f447b5185"
dependencies = [
 "num-traits",
 "rand 0.8.5",
]

[[package]]
name = "ark-transcript"
version = "0.0.2"
source = "git+https://github.com/w3f/ring-vrf?rev=3119f51#3119f51b54b69308abfb0671f6176cb125ae1bf1"
dependencies = [
 "ark-ff",
 "ark-serialize",
 "ark-std",
 "digest 0.10.7",
 "rand_core 0.6.4",
 "sha3",
]

[[package]]
name = "array-bytes"
version = "6.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6f840fb7195bcfc5e17ea40c26e5ce6d5b9ce5d584466e17703209657e459ae0"

[[package]]
name = "arrayref"
version = "0.3.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6b4930d2cb77ce62f89ee5d5289b4ac049559b1c45539271f5ed4fdc7db34545"

[[package]]
name = "arrayvec"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "23b62fc65de8e4e7f52534fb52b0f3ed04746ae267519eef2a83941e8085068b"

[[package]]
name = "arrayvec"
version = "0.7.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "96d30a06541fbafbc7f82ed10c06164cfbd2c401138f6addd8404629c4b16711"

[[package]]
name = "asn1-rs"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7f6fd5ddaf0351dff5b8da21b2fb4ff8e08ddd02857f0bf69c47639106c0fff0"
dependencies = [
 "asn1-rs-derive",
 "asn1-rs-impl",
 "displaydoc",
 "nom",
 "num-traits",
 "rusticata-macros",
 "thiserror",
 "time",
]

[[package]]
name = "asn1-rs-derive"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "726535892e8eae7e70657b4c8ea93d26b8553afb1ce617caee529ef96d7dee6c"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.109",
 "synstructure",
]

[[package]]
name = "asn1-rs-impl"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2777730b2039ac0f95f093556e61b6d26cebed5393ca6f152717777cec3a42ed"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "async-channel"
version = "1.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "81953c529336010edd6d8e358f886d9581267795c61b19475b71314bffa46d35"
dependencies = [
 "concurrent-queue",
 "event-listener 2.5.3",
 "futures-core",
]

[[package]]
name = "async-io"
version = "2.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fb41eb19024a91746eba0773aa5e16036045bbf45733766661099e182ea6a744"
dependencies = [
 "async-lock 3.3.0",
 "cfg-if",
 "concurrent-queue",
 "futures-io",
 "futures-lite",
 "parking",
 "polling",
 "rustix 0.38.30",
 "slab",
 "tracing",
 "windows-sys 0.52.0",
]

[[package]]
name = "async-lock"
version = "2.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "287272293e9d8c41773cec55e365490fe034813a2f172f502d6ddcf75b2f582b"
dependencies = [
 "event-listener 2.5.3",
]

[[package]]
name = "async-lock"
version = "3.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d034b430882f8381900d3fe6f0aaa3ad94f2cb4ac519b429692a1bc2dda4ae7b"
dependencies = [
 "event-listener 4.0.3",
 "event-listener-strategy",
 "pin-project-lite 0.2.13",
]

[[package]]
name = "async-recursion"
version = "1.0.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5fd55a5ba1179988837d24ab4c7cc8ed6efdeff578ede0416b4225a5fca35bd0"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.48",
]

[[package]]
name = "async-trait"
version = "0.1.77"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c980ee35e870bd1a4d2c8294d4c04d0499e67bca1e4b5cefcc693c2fa00caea9"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.48",
]

[[package]]
name = "asynchronous-codec"
version = "0.6.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4057f2c32adbb2fc158e22fb38433c8e9bbf76b75a4732c7c0cbaf695fb65568"
dependencies = [
 "bytes",
 "futures-sink",
 "futures-util",
 "memchr",
 "pin-project-lite 0.2.13",
]

[[package]]
name = "atty"
version = "0.2.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d9b39be18770d11421cdb1b9947a45dd3f37e93092cbf377614828a319d5fee8"
dependencies = [
 "hermit-abi 0.1.19",
 "libc",
 "winapi",
]

[[package]]
name = "autocfg"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d468802bab17cbc0cc575e9b053f41e72aa36bfa6b7f55e3529ffa43161b97fa"

[[package]]
name = "backtrace"
version = "0.3.69"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2089b7e3f35b9dd2d0ed921ead4f6d318c27680d4a5bd167b3ee120edb105837"
dependencies = [
 "addr2line 0.21.0",
 "cc",
 "cfg-if",
 "libc",
 "miniz_oxide",
 "object 0.32.2",
 "rustc-demangle",
]

[[package]]
name = "bandersnatch_vrfs"
version = "0.0.1"
source = "git+https://github.com/w3f/ring-vrf?rev=3119f51#3119f51b54b69308abfb0671f6176cb125ae1bf1"
dependencies = [
 "ark-bls12-381",
 "ark-ec",
 "ark-ed-on-bls12-381-bandersnatch",
 "ark-ff",
 "ark-scale 0.0.12",
 "ark-serialize",
 "ark-std",
 "dleq_vrf",
 "fflonk",
 "merlin 3.0.0",
 "rand_chacha 0.3.1",
 "rand_core 0.6.4",
 "ring 0.1.0",
 "sha2 0.10.8",
 "zeroize",
]

[[package]]
name = "base-x"
version = "0.2.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4cbbc9d0964165b47557570cce6c952866c2678457aca742aafc9fb771d30270"

[[package]]
name = "base16ct"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4c7f02d4ea65f2c1853089ffd8d2787bdbc63de2f0d29dedbcf8ccdfa0ccd4cf"

[[package]]
name = "base64"
version = "0.13.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9e1b586273c5702936fe7b7d6896644d8be71e6314cfe09d3167c95f712589e8"

[[package]]
name = "base64"
version = "0.21.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9d297deb1925b89f2ccc13d7635fa0714f12c87adce1c75356b39ca9b7178567"

[[package]]
name = "base64ct"
version = "1.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8c3c1a368f70d6cf7302d78f8f7093da241fb8e8807c05cc9e51a125895a6d5b"

[[package]]
name = "beef"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3a8241f3ebb85c056b509d4327ad0358fbbba6ffb340bf388f26350aeda225b1"
dependencies = [
 "serde",
]

[[package]]
name = "bincode"
version = "1.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b1f45e9417d87227c7a56d22e471c6206462cba514c7590c09aff4cf6d1ddcad"
dependencies = [
 "serde",
]

[[package]]
name = "bindgen"
version = "0.65.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cfdf7b466f9a4903edc73f95d6d2bcd5baf8ae620638762244d3f60143643cc5"
dependencies = [
 "bitflags 1.3.2",
 "cexpr",
 "clang-sys",
 "lazy_static",
 "lazycell",
 "peeking_take_while",
 "prettyplease 0.2.16",
 "proc-macro2",
 "quote",
 "regex",
 "rustc-hash",
 "shlex",
 "syn 2.0.48",
]

[[package]]
name = "bitflags"
version = "1.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bef38d45163c2f1dde094a7dfd33ccf595c92905c8f8f4fdc18d06fb1037718a"

[[package]]
name = "bitflags"
version = "2.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ed570934406eb16438a4e976b1b4500774099c13b8cb96eec99f620f05090ddf"

[[package]]
name = "bitvec"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1bc2832c24239b0141d5674bb9174f9d68a8b5b3f2753311927c172ca46f7e9c"
dependencies = [
 "funty",
 "radium",
 "tap",
 "wyz",
]

[[package]]
name = "blake2"
version = "0.10.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "46502ad458c9a52b69d4d4d32775c788b7a1b85e8bc9d482d92250fc0e3f8efe"
dependencies = [
 "digest 0.10.7",
]

[[package]]
name = "blake2b_simd"
version = "1.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "23285ad32269793932e830392f2fe2f83e26488fd3ec778883a93c8323735780"
dependencies = [
 "arrayref",
 "arrayvec 0.7.4",
 "constant_time_eq",
]

[[package]]
name = "blake2s_simd"
version = "1.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "94230421e395b9920d23df13ea5d77a20e1725331f90fbbf6df6040b33f756ae"
dependencies = [
 "arrayref",
 "arrayvec 0.7.4",
 "constant_time_eq",
]

[[package]]
name = "blake3"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0231f06152bf547e9c2b5194f247cd97aacf6dcd8b15d8e5ec0663f64580da87"
dependencies = [
 "arrayref",
 "arrayvec 0.7.4",
 "cc",
 "cfg-if",
 "constant_time_eq",
]

[[package]]
name = "block-buffer"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c0940dc441f31689269e10ac70eb1002a3a1d3ad1390e030043662eb7fe4688b"
dependencies = [
 "block-padding",
 "byte-tools",
 "byteorder",
 "generic-array 0.12.4",
]

[[package]]
name = "block-buffer"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4152116fd6e9dadb291ae18fc1ec3575ed6d84c29642d97890f4b4a3417297e4"
dependencies = [
 "generic-array 0.14.7",
]

[[package]]
name = "block-buffer"
version = "0.10.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3078c7629b62d3f0439517fa394996acacc5cbc91c5a20d8c658e77abd503a71"
dependencies = [
 "generic-array 0.14.7",
]

[[package]]
name = "block-padding"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fa79dedbb091f449f1f39e53edf88d5dbe95f895dae6135a8d7b881fb5af73f5"
dependencies = [
 "byte-tools",
]

[[package]]
name = "bounded-collections"
version = "0.1.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ca548b6163b872067dc5eb82fd130c56881435e30367d2073594a3d9744120dd"
dependencies = [
 "log",
 "parity-scale-codec 3.6.9",
 "scale-info",
 "serde",
]

[[package]]
name = "bs58"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "771fe0050b883fcc3ea2359b1a96bcfbc090b7116eae7c3c512c7a083fdf23d3"

[[package]]
name = "bs58"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f5353f36341f7451062466f0b755b96ac3a9547e4d7f6b70d603fc721a7d7896"
dependencies = [
 "tinyvec",
]

[[package]]
name = "bstr"
version = "1.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c48f0051a4b4c5e0b6d365cd04af53aeaa209e3cc15ec2cdb69e73cc87fbd0dc"
dependencies = [
 "memchr",
 "serde",
]

[[package]]
name = "build-helper"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bdce191bf3fa4995ce948c8c83b4640a1745457a149e73c6db75b4ffe36aad5f"
dependencies = [
 "semver 0.6.0",
]

[[package]]
name = "bumpalo"
version = "3.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7f30e7476521f6f8af1a1c4c0b8cc94f0bee37d91763d0ca2665f299b6cd8aec"

[[package]]
name = "byte-slice-cast"
version = "1.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c3ac9f8b63eca6fd385229b3675f6cc0dc5c8a5c8a54a59d4f52ffd670d87b0c"

[[package]]
name = "byte-tools"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e3b5ca7a04898ad4bcd41c90c5285445ff5b791899bb1b0abdd2a2aa791211d7"

[[package]]
name = "bytemuck"
version = "1.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "374d28ec25809ee0e23827c2ab573d729e293f281dfe393500e7ad618baa61c6"

[[package]]
name = "byteorder"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1fd0f2584146f6f2ef48085050886acf353beff7305ebd1ae69500e27c67f64b"

[[package]]
name = "bytes"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a2bd12c1caf447e69cd4528f47f94d203fd2582878ecb9e9465484c4148a8223"

[[package]]
name = "bzip2-sys"
version = "0.1.11+1.0.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "736a955f3fa7875102d57c82b8cac37ec45224a07fd32d58f9f7a186b6cd4cdc"
dependencies = [
 "cc",
 "libc",
 "pkg-config",
]

[[package]]
name = "camino"
version = "1.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c59e92b5a388f549b863a7bea62612c09f24c8393560709a54558a9abdfb3b9c"
dependencies = [
 "serde",
]

[[package]]
name = "cargo-platform"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ceed8ef69d8518a5dda55c07425450b58a4e1946f4951eab6d7191ee86c2443d"
dependencies = [
 "serde",
]

[[package]]
name = "cargo_metadata"
version = "0.15.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eee4243f1f26fc7a42710e7439c149e2b10b05472f88090acce52632f231a73a"
dependencies = [
 "camino",
 "cargo-platform",
 "semver 1.0.21",
 "serde",
 "serde_json",
 "thiserror",
]

[[package]]
name = "cc"
version = "1.0.83"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f1174fb0b6ec23863f8b971027804a42614e347eafb0a95bf0b12cdae21fc4d0"
dependencies = [
 "jobserver",
 "libc",
]

[[package]]
name = "cexpr"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6fac387a98bb7c37292057cffc56d62ecb629900026402633ae9160df93a8766"
dependencies = [
 "nom",
]

[[package]]
name = "cfg-expr"
version = "0.15.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6100bc57b6209840798d95cb2775684849d332f7bd788db2a8c8caf7ef82a41a"
dependencies = [
 "smallvec",
]

[[package]]
name = "cfg-if"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "baf1de4339761588bc0619e3cbc0120ee582ebb74b53b4efbf79117bd2da40fd"

[[package]]
name = "cfg_aliases"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fd16c4719339c4530435d38e511904438d07cce7950afa3718a84ac36c10e89e"

[[package]]
name = "chacha20"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c3613f74bd2eac03dad61bd53dbe620703d4371614fe0bc3b9f04dd36fe4e818"
dependencies = [
 "cfg-if",
 "cipher",
 "cpufeatures",
]

[[package]]
name = "chacha20poly1305"
version = "0.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "10cd79432192d1c0f4e1a0fef9527696cc039165d729fb41b3f4f4f354c2dc35"
dependencies = [
 "aead",
 "chacha20",
 "cipher",
 "poly1305",
 "zeroize",
]

[[package]]
name = "chrono"
version = "0.4.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7f2c685bad3eb3d45a01354cedb7d5faa66194d1d58ba6e267a8de788f79db38"
dependencies = [
 "android-tzdata",
 "iana-time-zone",
 "js-sys",
 "num-traits",
 "wasm-bindgen",
 "windows-targets 0.48.5",
]

[[package]]
name = "cid"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b9b68e3193982cd54187d71afdb2a271ad4cf8af157858e9cb911b91321de143"
dependencies = [
 "core2",
 "multibase",
 "multihash",
 "serde",
 "unsigned-varint",
]

[[package]]
name = "cipher"
version = "0.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "773f3b9af64447d2ce9850330c473515014aa235e6a783b02db81ff39e4a3dad"
dependencies = [
 "crypto-common",
 "inout",
 "zeroize",
]

[[package]]
name = "clang-sys"
version = "1.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "67523a3b4be3ce1989d607a828d036249522dd9c1c8de7f4dd2dae43a37369d1"
dependencies = [
 "glob",
 "libc",
 "libloading",
]

[[package]]
name = "clap"
version = "4.4.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e578d6ec4194633722ccf9544794b71b1385c3c027efe0c55db226fc880865c"
dependencies = [
 "clap_builder",
 "clap_derive",
]

[[package]]
name = "clap_builder"
version = "4.4.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4df4df40ec50c46000231c914968278b1eb05098cf8f1b3a518a95030e71d1c7"
dependencies = [
 "anstream",
 "anstyle",
 "clap_lex",
 "strsim",
]

[[package]]
name = "clap_derive"
version = "4.4.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cf9804afaaf59a91e75b022a30fb7229a7901f60c755489cc61c9b423b836442"
dependencies = [
 "heck",
 "proc-macro2",
 "quote",
 "syn 2.0.48",
]

[[package]]
name = "clap_lex"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "702fc72eb24e5a1e48ce58027a675bc24edd52096d5397d4aea7c6dd9eca0bd1"

[[package]]
name = "codespan-reporting"
version = "0.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3538270d33cc669650c4b093848450d380def10c331d38c768e34cac80576e6e"
dependencies = [
 "termcolor",
 "unicode-width",
]

[[package]]
name = "colorchoice"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "acbf1af155f9b9ef647e42cdc158db4b64a1b61f743629225fde6f3e0be2a7c7"

[[package]]
name = "comfy-table"
version = "7.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7c64043d6c7b7a4c58e39e7efccfdea7b93d885a795d0c054a69dbbf4dd52686"
dependencies = [
 "strum 0.25.0",
 "strum_macros 0.25.3",
 "unicode-width",
]

[[package]]
name = "common"
version = "0.1.0"
source = "git+https://github.com/w3f/ring-proof?rev=0e948f3#0e948f3c28cbacecdd3020403c4841c0eb339213"
dependencies = [
 "ark-ec",
 "ark-ff",
 "ark-poly",
 "ark-serialize",
 "ark-std",
 "fflonk",
 "merlin 3.0.0",
]

[[package]]
name = "common-path"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2382f75942f4b3be3690fe4f86365e9c853c1587d6ee58212cebf6e2a9ccd101"

[[package]]
name = "concurrent-queue"
version = "2.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d16048cd947b08fa32c24458a22f5dc5e835264f689f4f5653210c69fd107363"
dependencies = [
 "crossbeam-utils",
]

[[package]]
name = "console"
version = "0.15.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0e1f83fc076bd6dd27517eacdf25fef6c4dfe5f1d7448bafaaf3a26f13b5e4eb"
dependencies = [
 "encode_unicode",
 "lazy_static",
 "libc",
 "unicode-width",
 "windows-sys 0.52.0",
]

[[package]]
name = "const-oid"
version = "0.9.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c2459377285ad874054d797f3ccebf984978aa39129f6eafde5cdc8315b612f8"

[[package]]
name = "const-random"
version = "0.1.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5aaf16c9c2c612020bcfd042e170f6e32de9b9d75adb5277cdbbd2e2c8c8299a"
dependencies = [
 "const-random-macro",
]

[[package]]
name = "const-random-macro"
version = "0.1.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f9d839f2a20b0aee515dc581a6172f2321f96cab76c1a38a4c584a194955390e"
dependencies = [
 "getrandom 0.2.12",
 "once_cell",
 "tiny-keccak",
]

[[package]]
name = "constant_time_eq"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f7144d30dcf0fafbce74250a3963025d8d52177934239851c917d29f1df280c2"

[[package]]
name = "core-foundation"
version = "0.9.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "91e195e091a93c46f7102ec7818a2aa394e1e1771c3ab4825963fa03e45afb8f"
dependencies = [
 "core-foundation-sys",
 "libc",
]

[[package]]
name = "core-foundation-sys"
version = "0.8.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "06ea2b9bc92be3c2baa9334a323ebca2d6f074ff852cd1d7b11064035cd3868f"

[[package]]
name = "core2"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b49ba7ef1ad6107f8824dbe97de947cbaac53c44e7f9756a1fba0d37c1eec505"
dependencies = [
 "memchr",
]

[[package]]
name = "cpp_demangle"
version = "0.3.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eeaa953eaad386a53111e47172c2fedba671e5684c8dd601a5f474f4f118710f"
dependencies = [
 "cfg-if",
]

[[package]]
name = "cpufeatures"
version = "0.2.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "53fe5e26ff1b7aef8bca9c6080520cfb8d9333c7568e1829cef191a9723e5504"
dependencies = [
 "libc",
]

[[package]]
name = "cranelift-bforest"
version = "0.95.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1277fbfa94bc82c8ec4af2ded3e639d49ca5f7f3c7eeab2c66accd135ece4e70"
dependencies = [
 "cranelift-entity",
]

[[package]]
name = "cranelift-codegen"
version = "0.95.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c6e8c31ad3b2270e9aeec38723888fe1b0ace3bea2b06b3f749ccf46661d3220"
dependencies = [
 "bumpalo",
 "cranelift-bforest",
 "cranelift-codegen-meta",
 "cranelift-codegen-shared",
 "cranelift-entity",
 "cranelift-isle",
 "gimli 0.27.3",
 "hashbrown 0.13.2",
 "log",
 "regalloc2",
 "smallvec",
 "target-lexicon",
]

[[package]]
name = "cranelift-codegen-meta"
version = "0.95.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c8ac5ac30d62b2d66f12651f6b606dbdfd9c2cfd0908de6b387560a277c5c9da"
dependencies = [
 "cranelift-codegen-shared",
]

[[package]]
name = "cranelift-codegen-shared"
version = "0.95.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dd82b8b376247834b59ed9bdc0ddeb50f517452827d4a11bccf5937b213748b8"

[[package]]
name = "cranelift-entity"
version = "0.95.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "40099d38061b37e505e63f89bab52199037a72b931ad4868d9089ff7268660b0"
dependencies = [
 "serde",
]

[[package]]
name = "cranelift-frontend"
version = "0.95.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "64a25d9d0a0ae3079c463c34115ec59507b4707175454f0eee0891e83e30e82d"
dependencies = [
 "cranelift-codegen",
 "log",
 "smallvec",
 "target-lexicon",
]

[[package]]
name = "cranelift-isle"
version = "0.95.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "80de6a7d0486e4acbd5f9f87ec49912bf4c8fb6aea00087b989685460d4469ba"

[[package]]
name = "cranelift-native"
version = "0.95.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bb6b03e0e03801c4b3fd8ce0758a94750c07a44e7944cc0ffbf0d3f2e7c79b00"
dependencies = [
 "cranelift-codegen",
 "libc",
 "target-lexicon",
]

[[package]]
name = "cranelift-wasm"
version = "0.95.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ff3220489a3d928ad91e59dd7aeaa8b3de18afb554a6211213673a71c90737ac"
dependencies = [
 "cranelift-codegen",
 "cranelift-entity",
 "cranelift-frontend",
 "itertools",
 "log",
 "smallvec",
 "wasmparser",
 "wasmtime-types",
]

[[package]]
name = "crc32fast"
version = "1.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b540bd8bc810d3885c6ea91e2018302f68baba2129ab3e88f32389ee9370880d"
dependencies = [
 "cfg-if",
]

[[package]]
name = "crossbeam-deque"
version = "0.8.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "613f8cc01fe9cf1a3eb3d7f488fd2fa8388403e97039e2f73692932e291a770d"
dependencies = [
 "crossbeam-epoch",
 "crossbeam-utils",
]

[[package]]
name = "crossbeam-epoch"
version = "0.9.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5b82ac4a3c2ca9c3460964f020e1402edd5753411d7737aa39c3714ad1b5420e"
dependencies = [
 "crossbeam-utils",
]

[[package]]
name = "crossbeam-utils"
version = "0.8.19"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "248e3bacc7dc6baa3b21e405ee045c3047101a49145e7e9eca583ab4c2ca5345"

[[package]]
name = "crunchy"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7a81dae078cea95a014a339291cec439d2f232ebe854a9d672b796c6afafa9b7"

[[package]]
name = "crypto-bigint"
version = "0.5.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0dc92fb57ca44df6db8059111ab3af99a63d5d0f8375d9972e319a379c6bab76"
dependencies = [
 "generic-array 0.14.7",
 "rand_core 0.6.4",
 "subtle",
 "zeroize",
]

[[package]]
name = "crypto-common"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1bfb12502f3fc46cca1bb51ac28df9d618d813cdc3d2f25b9fe775a34af26bb3"
dependencies = [
 "generic-array 0.14.7",
 "rand_core 0.6.4",
 "typenum",
]

[[package]]
name = "crypto-mac"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b584a330336237c1eecd3e94266efb216c56ed91225d634cb2991c5f3fd1aeab"
dependencies = [
 "generic-array 0.14.7",
 "subtle",
]

[[package]]
name = "crypto-mac"
version = "0.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b1d1a86f49236c215f271d40892d5fc950490551400b02ef360692c29815c714"
dependencies = [
 "generic-array 0.14.7",
 "subtle",
]

[[package]]
name = "ctr"
version = "0.9.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0369ee1ad671834580515889b80f2ea915f23b8be8d0daa4bbaf2ac5c7590835"
dependencies = [
 "cipher",
]

[[package]]
name = "curve25519-dalek"
version = "2.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4a9b85542f99a2dfa2a1b8e192662741c9859a846b296bef1c92ef9b58b5a216"
dependencies = [
 "byteorder",
 "digest 0.8.1",
 "rand_core 0.5.1",
 "subtle",
 "zeroize",
]

[[package]]
name = "curve25519-dalek"
version = "3.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b9fdf9972b2bd6af2d913799d9ebc165ea4d2e65878e329d9c6b372c4491b61"
dependencies = [
 "byteorder",
 "digest 0.9.0",
 "rand_core 0.5.1",
 "subtle",
 "zeroize",
]

[[package]]
name = "curve25519-dalek"
version = "4.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e89b8c6a2e4b1f45971ad09761aafb85514a84744b67a95e32c3cc1352d1f65c"
dependencies = [
 "cfg-if",
 "cpufeatures",
 "curve25519-dalek-derive",
 "digest 0.10.7",
 "fiat-crypto",
 "platforms",
 "rustc_version",
 "subtle",
 "zeroize",
]

[[package]]
name = "curve25519-dalek-derive"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f46882e17999c6cc590af592290432be3bce0428cb0d5f8b6715e4dc7b383eb3"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.48",
]

[[package]]
name = "cxx"
version = "1.0.115"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8de00f15a6fa069c99b88c5c78c4541d0e7899a33b86f7480e23df2431fce0bc"
dependencies = [
 "cc",
 "cxxbridge-flags",
 "cxxbridge-macro",
 "link-cplusplus",
]

[[package]]
name = "cxx-build"
version = "1.0.115"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0a71e1e631fa2f2f5f92e8b0d860a00c198c6771623a6cefcc863e3554f0d8d6"
dependencies = [
 "cc",
 "codespan-reporting",
 "once_cell",
 "proc-macro2",
 "quote",
 "scratch",
 "syn 2.0.48",
]

[[package]]
name = "cxxbridge-flags"
version = "1.0.115"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6f3fed61d56ba497c4efef9144dfdbaa25aa58f2f6b3a7cf441d4591c583745c"

[[package]]
name = "cxxbridge-macro"
version = "1.0.115"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8908e380a8efd42150c017b0cfa31509fc49b6d47f7cb6b33e93ffb8f4e3661e"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.48",
]

[[package]]
name = "data-encoding"
version = "2.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7e962a19be5cfc3f3bf6dd8f61eb50107f356ad6270fbb3ed41476571db78be5"

[[package]]
name = "data-encoding-macro"
version = "0.1.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "20c01c06f5f429efdf2bae21eb67c28b3df3cf85b7dd2d8ef09c0838dac5d33e"
dependencies = [
 "data-encoding",
 "data-encoding-macro-internal",
]

[[package]]
name = "data-encoding-macro-internal"
version = "0.1.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0047d07f2c89b17dd631c80450d69841a6b5d7fb17278cbc43d7e4cfcf2576f3"
dependencies = [
 "data-encoding",
 "syn 1.0.109",
]

[[package]]
name = "der"
version = "0.7.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fffa369a668c8af7dbf8b5e56c9f744fbd399949ed171606040001947de40b1c"
dependencies = [
 "const-oid",
 "zeroize",
]

[[package]]
name = "der-parser"
version = "8.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dbd676fbbab537128ef0278adb5576cf363cff6aa22a7b24effe97347cfab61e"
dependencies = [
 "asn1-rs",
 "displaydoc",
 "nom",
 "num-bigint",
 "num-traits",
 "rusticata-macros",
]

[[package]]
name = "deranged"
version = "0.3.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b42b6fa04a440b495c8b04d0e71b707c585f83cb9cb28cf8cd0d976c315e31b4"
dependencies = [
 "powerfmt",
]

[[package]]
name = "derivative"
version = "2.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fcc3dd5e9e9c0b295d6e1e4d811fb6f157d5ffd784b8d202fc62eac8035a770b"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "derive-syn-parse"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e79116f119dd1dba1abf1f3405f03b9b0e79a27a3883864bfebded8a3dc768cd"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "derive_more"
version = "0.99.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4fb810d30a7c1953f91334de7244731fc3f3c10d7fe163338a35b9f640960321"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "difflib"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6184e33543162437515c2e2b48714794e37845ec9851711914eec9d308f6ebe8"

[[package]]
name = "digest"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f3d0c8c8752312f9713efd397ff63acb9f85585afbf179282e720e7704954dd5"
dependencies = [
 "generic-array 0.12.4",
]

[[package]]
name = "digest"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d3dd60d1080a57a05ab032377049e0591415d2b31afd7028356dbf3cc6dcb066"
dependencies = [
 "generic-array 0.14.7",
]

[[package]]
name = "digest"
version = "0.10.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9ed9a281f7bc9b7576e61468ba615a66a5c8cfdff42420a70aa82701a3b1e292"
dependencies = [
 "block-buffer 0.10.4",
 "const-oid",
 "crypto-common",
 "subtle",
]

[[package]]
name = "directories"
version = "4.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f51c5d4ddabd36886dd3e1438cb358cdcb0d7c499cb99cb4ac2e38e18b5cb210"
dependencies = [
 "dirs-sys",
]

[[package]]
name = "directories-next"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "339ee130d97a610ea5a5872d2bbb130fdf68884ff09d3028b81bec8a1ac23bbc"
dependencies = [
 "cfg-if",
 "dirs-sys-next",
]

[[package]]
name = "dirs-sys"
version = "0.3.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1b1d1d91c932ef41c0f2663aa8b0ca0342d444d842c06914aa0a7e352d0bada6"
dependencies = [
 "libc",
 "redox_users",
 "winapi",
]

[[package]]
name = "dirs-sys-next"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4ebda144c4fe02d1f7ea1a7d9641b6fc6b580adcfa024ae48797ecdeb6825b4d"
dependencies = [
 "libc",
 "redox_users",
 "winapi",
]

[[package]]
name = "displaydoc"
version = "0.2.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "487585f4d0c6655fe74905e2504d8ad6908e4db67f744eb140876906c2f3175d"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.48",
]

[[package]]
name = "dleq_vrf"
version = "0.0.2"
source = "git+https://github.com/w3f/ring-vrf?rev=3119f51#3119f51b54b69308abfb0671f6176cb125ae1bf1"
dependencies = [
 "ark-ec",
 "ark-ff",
 "ark-scale 0.0.10",
 "ark-secret-scalar",
 "ark-serialize",
 "ark-std",
 "ark-transcript",
 "arrayvec 0.7.4",
 "rand_core 0.6.4",
 "zeroize",
]

[[package]]
name = "docify"
version = "0.2.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7cc4fd38aaa9fb98ac70794c82a00360d1e165a87fbf96a8a91f9dfc602aaee2"
dependencies = [
 "docify_macros",
]

[[package]]
name = "docify_macros"
version = "0.2.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "63fa215f3a0d40fb2a221b3aa90d8e1fbb8379785a990cb60d62ac71ebdc6460"
dependencies = [
 "common-path",
 "derive-syn-parse",
 "once_cell",
 "proc-macro2",
 "quote",
 "regex",
 "syn 2.0.48",
 "termcolor",
 "toml 0.8.2",
 "walkdir",
]

[[package]]
name = "downcast"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1435fa1053d8b2fbbe9be7e97eca7f33d37b28409959813daefc1446a14247f1"

[[package]]
name = "dtoa"
version = "1.0.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dcbb2bf8e87535c23f7a8a321e364ce21462d0ff10cb6407820e8e96dfff6653"

[[package]]
name = "dyn-clonable"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4e9232f0e607a262ceb9bd5141a3dfb3e4db6994b31989bbfd845878cba59fd4"
dependencies = [
 "dyn-clonable-impl",
 "dyn-clone",
]

[[package]]
name = "dyn-clonable-impl"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "558e40ea573c374cf53507fd240b7ee2f5477df7cfebdb97323ec61c719399c5"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "dyn-clone"
version = "1.0.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "545b22097d44f8a9581187cdf93de7a71e4722bf51200cfaba810865b49a495d"

[[package]]
name = "ecdsa"
version = "0.16.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ee27f32b5c5292967d2d4a9d7f1e0b0aed2c15daded5a60300e4abb9d8020bca"
dependencies = [
 "der",
 "digest 0.10.7",
 "elliptic-curve",
 "rfc6979",
 "signature",
 "spki",
]

[[package]]
name = "ed25519"
version = "2.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "115531babc129696a58c64a4fef0a8bf9e9698629fb97e9e40767d235cfbcd53"
dependencies = [
 "pkcs8",
 "signature",
]

[[package]]
name = "ed25519-dalek"
version = "2.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1f628eaec48bfd21b865dc2950cfa014450c01d2fa2b69a86c2fd5844ec523c0"
dependencies = [
 "curve25519-dalek 4.1.1",
 "ed25519",
 "rand_core 0.6.4",
 "serde",
 "sha2 0.10.8",
 "subtle",
 "zeroize",
]

[[package]]
name = "ed25519-zebra"
version = "3.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7c24f403d068ad0b359e577a77f92392118be3f3c927538f2bb544a5ecd828c6"
dependencies = [
 "curve25519-dalek 3.2.0",
 "hashbrown 0.12.3",
 "hex",
 "rand_core 0.6.4",
 "sha2 0.9.9",
 "zeroize",
]

[[package]]
name = "either"
version = "1.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a26ae43d7bcc3b814de94796a5e736d4029efb0ee900c12e2d54c993ad1a1e07"

[[package]]
name = "elliptic-curve"
version = "0.13.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b5e6043086bf7973472e0c7dff2142ea0b680d30e18d9cc40f267efbf222bd47"
dependencies = [
 "base16ct",
 "crypto-bigint",
 "digest 0.10.7",
 "ff",
 "generic-array 0.14.7",
 "group",
 "pkcs8",
 "rand_core 0.6.4",
 "sec1",
 "subtle",
 "zeroize",
]

[[package]]
name = "encode_unicode"
version = "0.3.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a357d28ed41a50f9c765dbfe56cbc04a64e53e5fc58ba79fbc34c10ef3df831f"

[[package]]
name = "enum-as-inner"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c9720bba047d567ffc8a3cba48bf19126600e249ab7f128e9233e6376976a116"
dependencies = [
 "heck",
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "env_logger"
version = "0.10.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4cd405aab171cb85d6735e5c8d9db038c17d3ca007a4d2c25f337935c3d90580"
dependencies = [
 "humantime",
 "is-terminal",
 "log",
 "regex",
 "termcolor",
]

[[package]]
name = "environmental"
version = "1.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e48c92028aaa870e83d51c64e5d4e0b6981b360c522198c23959f219a4e1b15b"

[[package]]
name = "equivalent"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5443807d6dff69373d433ab9ef5378ad8df50ca6298caf15de6e52e24aaf54d5"

[[package]]
name = "errno"
version = "0.3.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a258e46cdc063eb8519c00b9fc845fc47bcfca4130e2f08e88665ceda8474245"
dependencies = [
 "libc",
 "windows-sys 0.52.0",
]

[[package]]
name = "event-listener"
version = "2.5.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0206175f82b8d6bf6652ff7d71a1e27fd2e4efde587fd368662814d6ec1d9ce0"

[[package]]
name = "event-listener"
version = "4.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "67b215c49b2b248c855fb73579eb1f4f26c38ffdc12973e20e07b91d78d5646e"
dependencies = [
 "concurrent-queue",
 "parking",
 "pin-project-lite 0.2.13",
]

[[package]]
name = "event-listener-strategy"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "958e4d70b6d5e81971bebec42271ec641e7ff4e170a6fa605f2b8a8b65cb97d3"
dependencies = [
 "event-listener 4.0.3",
 "pin-project-lite 0.2.13",
]

[[package]]
name = "exit-future"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e43f2f1833d64e33f15592464d6fdd70f349dda7b1a53088eb83cd94014008c5"
dependencies = [
 "futures",
]

[[package]]
name = "expander"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5f86a749cf851891866c10515ef6c299b5c69661465e9c3bbe7e07a2b77fb0f7"
dependencies = [
 "blake2",
 "fs-err",
 "proc-macro2",
 "quote",
 "syn 2.0.48",
]

[[package]]
name = "fake-simd"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e88a8acf291dafb59c2d96e8f59828f3838bb1a70398823ade51a84de6a6deed"

[[package]]
name = "fallible-iterator"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4443176a9f2c162692bd3d352d745ef9413eec5782a80d8fd6f8a1ac692a07f7"

[[package]]
name = "fastrand"
version = "2.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "25cbce373ec4653f1a01a31e8a5e5ec0c622dc27ff9c4e6606eefef5cbbed4a5"

[[package]]
name = "fdlimit"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2c4c9e43643f5a3be4ca5b67d26b98031ff9db6806c3440ae32e02e3ceac3f1b"
dependencies = [
 "libc",
]

[[package]]
name = "ff"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ded41244b729663b1e574f1b4fb731469f69f79c17667b5d776b16cda0479449"
dependencies = [
 "rand_core 0.6.4",
 "subtle",
]

[[package]]
name = "fflonk"
version = "0.1.0"
source = "git+https://github.com/w3f/fflonk#1e854f35e9a65d08b11a86291405cdc95baa0a35"
dependencies = [
 "ark-ec",
 "ark-ff",
 "ark-poly",
 "ark-serialize",
 "ark-std",
 "merlin 3.0.0",
]

[[package]]
name = "fiat-crypto"
version = "0.2.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "27573eac26f4dd11e2b1916c3fe1baa56407c83c71a773a8ba17ec0bca03b6b7"

[[package]]
name = "file-per-thread-logger"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "84f2e425d9790201ba4af4630191feac6dcc98765b118d4d18e91d23c2353866"
dependencies = [
 "env_logger",
 "log",
]

[[package]]
name = "filetime"
version = "0.2.23"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1ee447700ac8aa0b2f2bd7bc4462ad686ba06baa6727ac149a2d6277f0d240fd"
dependencies = [
 "cfg-if",
 "libc",
 "redox_syscall 0.4.1",
 "windows-sys 0.52.0",
]

[[package]]
name = "finality-grandpa"
version = "0.16.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "36530797b9bf31cd4ff126dcfee8170f86b00cfdcea3269d73133cc0415945c3"
dependencies = [
 "either",
 "futures",
 "futures-timer",
 "log",
 "num-traits",
 "parity-scale-codec 3.6.9",
 "parking_lot 0.12.1",
 "scale-info",
]

[[package]]
name = "fixed-hash"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "835c052cb0c08c1acf6ffd71c022172e18723949c8282f2b9f27efbc51e64534"
dependencies = [
 "byteorder",
 "rand 0.8.5",
 "rustc-hex",
 "static_assertions",
]

[[package]]
name = "fixedbitset"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0ce7134b9999ecaf8bcd65542e436736ef32ddca1b3e06094cb6ec5755203b80"

[[package]]
name = "flate2"
version = "1.0.28"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "46303f565772937ffe1d394a4fac6f411c6013172fadde9dcdb1e147a086940e"
dependencies = [
 "crc32fast",
 "libz-sys",
 "miniz_oxide",
]

[[package]]
name = "float-cmp"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "98de4bbd547a563b716d8dfa9aad1cb19bfab00f4fa09a6a4ed21dbcf44ce9c4"
dependencies = [
 "num-traits",
]

[[package]]
name = "fnv"
version = "1.0.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3f9eec918d3f24069decb9af1554cad7c880e2da24a9afd88aca000531ab82c1"

[[package]]
name = "fork-tree"
version = "3.0.0"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "parity-scale-codec 3.6.9",
]

[[package]]
name = "form_urlencoded"
version = "1.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e13624c2627564efccf4934284bdd98cbaa14e79b0b5a141218e507b3a823456"
dependencies = [
 "percent-encoding",
]

[[package]]
name = "fragile"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6c2141d6d6c8512188a7891b4b01590a45f6dac67afb4f255c4124dbb86d4eaa"

[[package]]
name = "frame-benchmarking"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "frame-support",
 "frame-support-procedural",
 "frame-system",
 "linregress",
 "log",
 "parity-scale-codec 3.6.9",
 "paste 1.0.14",
 "scale-info",
 "serde",
 "sp-api",
 "sp-application-crypto",
 "sp-core",
 "sp-io",
 "sp-runtime",
 "sp-runtime-interface",
 "sp-std 8.0.0",
 "sp-storage",
 "static_assertions",
]

[[package]]
name = "frame-benchmarking-cli"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "Inflector",
 "array-bytes",
 "chrono",
 "clap",
 "comfy-table",
 "frame-benchmarking",
 "frame-support",
 "frame-system",
 "gethostname",
 "handlebars",
 "itertools",
 "lazy_static",
 "linked-hash-map",
 "log",
 "parity-scale-codec 3.6.9",
 "rand 0.8.5",
 "rand_pcg",
 "sc-block-builder",
 "sc-cli",
 "sc-client-api",
 "sc-client-db",
 "sc-executor",
 "sc-service",
 "sc-sysinfo",
 "serde",
 "serde_json",
 "sp-api",
 "sp-blockchain",
 "sp-core",
 "sp-database",
 "sp-externalities",
 "sp-inherents",
 "sp-io",
 "sp-keystore",
 "sp-runtime",
 "sp-state-machine",
 "sp-storage",
 "sp-trie",
 "sp-wasm-interface",
 "thiserror",
 "thousands",
]

[[package]]
name = "frame-election-provider-solution-type"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "proc-macro-crate 1.1.3",
 "proc-macro2",
 "quote",
 "syn 2.0.48",
]

[[package]]
name = "frame-election-provider-support"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "frame-election-provider-solution-type",
 "frame-support",
 "frame-system",
 "parity-scale-codec 3.6.9",
 "scale-info",
 "sp-arithmetic",
 "sp-core",
 "sp-npos-elections",
 "sp-runtime",
 "sp-std 8.0.0",
]

[[package]]
name = "frame-executive"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "frame-support",
 "frame-system",
 "frame-try-runtime",
 "log",
 "parity-scale-codec 3.6.9",
 "scale-info",
 "sp-core",
 "sp-io",
 "sp-runtime",
 "sp-std 8.0.0",
 "sp-tracing",
]

[[package]]
name = "frame-metadata"
version = "16.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "87cf1549fba25a6fcac22785b61698317d958e96cac72a59102ea45b9ae64692"
dependencies = [
 "cfg-if",
 "parity-scale-codec 3.6.9",
 "scale-info",
 "serde",
]

[[package]]
name = "frame-remote-externalities"
version = "0.10.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "async-recursion",
 "futures",
 "indicatif",
 "jsonrpsee",
 "log",
 "parity-scale-codec 3.6.9",
 "serde",
 "sp-core",
 "sp-io",
 "sp-runtime",
 "sp-state-machine",
 "spinners",
 "substrate-rpc-client",
 "tokio",
 "tokio-retry",
]

[[package]]
name = "frame-support"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "aquamarine",
 "bitflags 1.3.2",
 "docify",
 "environmental",
 "frame-metadata",
 "frame-support-procedural",
 "impl-trait-for-tuples",
 "k256",
 "log",
 "macro_magic",
 "parity-scale-codec 3.6.9",
 "paste 1.0.14",
 "scale-info",
 "serde",
 "serde_json",
 "smallvec",
 "sp-api",
 "sp-arithmetic",
 "sp-core",
 "sp-core-hashing-proc-macro",
 "sp-debug-derive",
 "sp-genesis-builder",
 "sp-inherents",
 "sp-io",
 "sp-metadata-ir",
 "sp-runtime",
 "sp-staking",
 "sp-state-machine",
 "sp-std 8.0.0",
 "sp-tracing",
 "sp-weights",
 "static_assertions",
 "tt-call",
]

[[package]]
name = "frame-support-procedural"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "Inflector",
 "cfg-expr",
 "derive-syn-parse",
 "expander",
 "frame-support-procedural-tools",
 "itertools",
 "macro_magic",
 "proc-macro-warning",
 "proc-macro2",
 "quote",
 "syn 2.0.48",
]

[[package]]
name = "frame-support-procedural-tools"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "frame-support-procedural-tools-derive",
 "proc-macro-crate 1.1.3",
 "proc-macro2",
 "quote",
 "syn 2.0.48",
]

[[package]]
name = "frame-support-procedural-tools-derive"
version = "3.0.0"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.48",
]

[[package]]
name = "frame-system"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "cfg-if",
 "frame-support",
 "log",
 "parity-scale-codec 3.6.9",
 "scale-info",
 "serde",
 "sp-core",
 "sp-io",
 "sp-runtime",
 "sp-std 8.0.0",
 "sp-version",
 "sp-weights",
]

[[package]]
name = "frame-system-benchmarking"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "frame-benchmarking",
 "frame-support",
 "frame-system",
 "parity-scale-codec 3.6.9",
 "scale-info",
 "sp-core",
 "sp-runtime",
 "sp-std 8.0.0",
]

[[package]]
name = "frame-system-rpc-runtime-api"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "parity-scale-codec 3.6.9",
 "sp-api",
]

[[package]]
name = "frame-try-runtime"
version = "0.10.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "frame-support",
 "parity-scale-codec 3.6.9",
 "sp-api",
 "sp-runtime",
 "sp-std 8.0.0",
]

[[package]]
name = "fs-err"
version = "2.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "88a41f105fe1d5b6b34b2055e3dc59bb79b46b48b2040b9e6c7b4b5de097aa41"
dependencies = [
 "autocfg",
]

[[package]]
name = "fs2"
version = "0.4.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9564fc758e15025b46aa6643b1b77d047d1a56a1aea6e01002ac0c7026876213"
dependencies = [
 "libc",
 "winapi",
]

[[package]]
name = "funty"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e6d5a32815ae3f33302d95fdcb2ce17862f8c65363dcfd29360480ba1001fc9c"

[[package]]
name = "futures"
version = "0.3.30"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "645c6916888f6cb6350d2550b80fb63e734897a8498abe35cfb732b6487804b0"
dependencies = [
 "futures-channel",
 "futures-core",
 "futures-executor",
 "futures-io",
 "futures-sink",
 "futures-task",
 "futures-util",
]

[[package]]
name = "futures-channel"
version = "0.3.30"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eac8f7d7865dcb88bd4373ab671c8cf4508703796caa2b1985a9ca867b3fcb78"
dependencies = [
 "futures-core",
 "futures-sink",
]

[[package]]
name = "futures-core"
version = "0.3.30"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dfc6580bb841c5a68e9ef15c77ccc837b40a7504914d52e47b8b0e9bbda25a1d"

[[package]]
name = "futures-executor"
version = "0.3.30"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a576fc72ae164fca6b9db127eaa9a9dda0d61316034f33a0a0d4eda41f02b01d"
dependencies = [
 "futures-core",
 "futures-task",
 "futures-util",
 "num_cpus",
]

[[package]]
name = "futures-io"
version = "0.3.30"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a44623e20b9681a318efdd71c299b6b222ed6f231972bfe2f224ebad6311f0c1"

[[package]]
name = "futures-lite"
version = "2.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "445ba825b27408685aaecefd65178908c36c6e96aaf6d8599419d46e624192ba"
dependencies = [
 "futures-core",
 "pin-project-lite 0.2.13",
]

[[package]]
name = "futures-macro"
version = "0.3.30"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "87750cf4b7a4c0625b1529e4c543c2182106e4dedc60a2a6455e00d212c489ac"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.48",
]

[[package]]
name = "futures-rustls"
version = "0.22.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d2411eed028cdf8c8034eaf21f9915f956b6c3abec4d4c7949ee67f0721127bd"
dependencies = [
 "futures-io",
 "rustls 0.20.9",
 "webpki",
]

[[package]]
name = "futures-sink"
version = "0.3.30"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9fb8e00e87438d937621c1c6269e53f536c14d3fbd6a042bb24879e57d474fb5"

[[package]]
name = "futures-task"
version = "0.3.30"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "38d84fa142264698cdce1a9f9172cf383a0c82de1bddcf3092901442c4097004"

[[package]]
name = "futures-timer"
version = "3.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e64b03909df88034c26dc1547e8970b91f98bdb65165d6a4e9110d94263dbb2c"

[[package]]
name = "futures-util"
version = "0.3.30"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3d6401deb83407ab3da39eba7e33987a73c3df0c82b4bb5813ee871c19c41d48"
dependencies = [
 "futures-channel",
 "futures-core",
 "futures-io",
 "futures-macro",
 "futures-sink",
 "futures-task",
 "memchr",
 "pin-project-lite 0.2.13",
 "pin-utils",
 "slab",
]

[[package]]
name = "fxhash"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c31b6d751ae2c7f11320402d34e41349dd1016f8d5d45e48c4312bc8625af50c"
dependencies = [
 "byteorder",
]

[[package]]
name = "generic-array"
version = "0.12.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ffdf9f34f1447443d37393cc6c2b8313aebddcd96906caf34e54c68d8e57d7bd"
dependencies = [
 "typenum",
]

[[package]]
name = "generic-array"
version = "0.14.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "85649ca51fd72272d7821adaf274ad91c288277713d9c18820d8499a7ff69e9a"
dependencies = [
 "typenum",
 "version_check",
 "zeroize",
]

[[package]]
name = "gethostname"
version = "0.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c1ebd34e35c46e00bb73e81363248d627782724609fe1b6396f553f68fe3862e"
dependencies = [
 "libc",
 "winapi",
]

[[package]]
name = "getrandom"
version = "0.1.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8fc3cb4d91f53b50155bdcfd23f6a4c39ae1969c2ae85982b135750cccaf5fce"
dependencies = [
 "cfg-if",
 "libc",
 "wasi 0.9.0+wasi-snapshot-preview1",
]

[[package]]
name = "getrandom"
version = "0.2.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "190092ea657667030ac6a35e305e62fc4dd69fd98ac98631e5d3a2b1575a12b5"
dependencies = [
 "cfg-if",
 "libc",
 "wasi 0.11.0+wasi-snapshot-preview1",
]

[[package]]
name = "ghash"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d930750de5717d2dd0b8c0d42c076c0e884c81a73e6cab859bbd2339c71e3e40"
dependencies = [
 "opaque-debug 0.3.0",
 "polyval",
]

[[package]]
name = "gimli"
version = "0.27.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b6c80984affa11d98d1b88b66ac8853f143217b399d3c74116778ff8fdb4ed2e"
dependencies = [
 "fallible-iterator",
 "indexmap 1.9.3",
 "stable_deref_trait",
]

[[package]]
name = "gimli"
version = "0.28.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4271d37baee1b8c7e4b708028c57d816cf9d2434acb33a549475f78c181f6253"

[[package]]
name = "glob"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d2fabcfbdc87f4758337ca535fb41a6d701b65693ce38287d856d1674551ec9b"

[[package]]
name = "globset"
version = "0.4.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "57da3b9b5b85bd66f31093f8c408b90a74431672542466497dcbdfdc02034be1"
dependencies = [
 "aho-corasick",
 "bstr",
 "log",
 "regex-automata 0.4.3",
 "regex-syntax 0.8.2",
]

[[package]]
name = "group"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f0f9ef7462f7c099f518d754361858f86d8a07af53ba9af0fe635bbccb151a63"
dependencies = [
 "ff",
 "rand_core 0.6.4",
 "subtle",
]

[[package]]
name = "h2"
version = "0.3.24"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bb2c4422095b67ee78da96fbb51a4cc413b3b25883c7717ff7ca1ab31022c9c9"
dependencies = [
 "bytes",
 "fnv",
 "futures-core",
 "futures-sink",
 "futures-util",
 "http",
 "indexmap 2.1.0",
 "slab",
 "tokio",
 "tokio-util",
 "tracing",
]

[[package]]
name = "handlebars"
version = "4.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "faa67bab9ff362228eb3d00bd024a4965d8231bbb7921167f0cfa66c6626b225"
dependencies = [
 "log",
 "pest",
 "pest_derive",
 "serde",
 "serde_json",
 "thiserror",
]

[[package]]
name = "hash-db"
version = "0.16.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8e7d7786361d7425ae2fe4f9e407eb0efaa0840f5212d109cc018c40c35c6ab4"

[[package]]
name = "hash256-std-hasher"
version = "0.15.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "92c171d55b98633f4ed3860808f004099b36c1cc29c42cfc53aa8591b21efcf2"
dependencies = [
 "crunchy",
]

[[package]]
name = "hashbrown"
version = "0.12.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8a9ee70c43aaf417c914396645a0fa852624801b24ebb7ae78fe8272889ac888"
dependencies = [
 "ahash 0.7.7",
]

[[package]]
name = "hashbrown"
version = "0.13.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "43a3c133739dddd0d2990f9a4bdf8eb4b21ef50e4851ca85ab661199821d510e"
dependencies = [
 "ahash 0.8.7",
]

[[package]]
name = "hashbrown"
version = "0.14.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "290f1a1d9242c78d09ce40a5e87e7554ee637af1351968159f4952f028f75604"

[[package]]
name = "heck"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "95505c38b4572b2d910cecb0281560f54b440a19336cbbcb27bf6ce6adc6f5a8"

[[package]]
name = "hermit-abi"
version = "0.1.19"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "62b467343b94ba476dcb2500d242dadbb39557df889310ac77c5d99100aaac33"
dependencies = [
 "libc",
]

[[package]]
name = "hermit-abi"
version = "0.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5d3d0e0f38255e7fa3cf31335b3a56f05febd18025f4db5ef7a0cfb4f8da651f"

[[package]]
name = "hex"
version = "0.4.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7f24254aa9a54b5c858eaee2f5bccdb46aaf0e486a595ed5fd8f86ba55232a70"

[[package]]
name = "hex-literal"
version = "0.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7ebdb29d2ea9ed0083cd8cece49bbd968021bd99b0849edb4a9a7ee0fdf6a4e0"

[[package]]
name = "hkdf"
version = "0.12.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7b5f8eb2ad728638ea2c7d47a21db23b7b58a72ed6a38256b8a1849f15fbbdf7"
dependencies = [
 "hmac 0.12.1",
]

[[package]]
name = "hmac"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "126888268dcc288495a26bf004b38c5fdbb31682f992c84ceb046a1f0fe38840"
dependencies = [
 "crypto-mac 0.8.0",
 "digest 0.9.0",
]

[[package]]
name = "hmac"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2a2a2320eb7ec0ebe8da8f744d7812d9fc4cb4d09344ac01898dbcb6a20ae69b"
dependencies = [
 "crypto-mac 0.11.1",
 "digest 0.9.0",
]

[[package]]
name = "hmac"
version = "0.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6c49c37c09c17a53d937dfbb742eb3a961d65a994e6bcdcf37e7399d0cc8ab5e"
dependencies = [
 "digest 0.10.7",
]

[[package]]
name = "hmac-drbg"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "17ea0a1394df5b6574da6e0c1ade9e78868c9fb0a4e5ef4428e32da4676b85b1"
dependencies = [
 "digest 0.9.0",
 "generic-array 0.14.7",
 "hmac 0.8.1",
]

[[package]]
name = "home"
version = "0.5.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e3d1354bf6b7235cb4a0576c2619fd4ed18183f689b12b006a0ee7329eeff9a5"
dependencies = [
 "windows-sys 0.52.0",
]

[[package]]
name = "hostname"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3c731c3e10504cc8ed35cfe2f1db4c9274c3d35fa486e3b31df46f068ef3e867"
dependencies = [
 "libc",
 "match_cfg",
 "winapi",
]

[[package]]
name = "http"
version = "0.2.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8947b1a6fad4393052c7ba1f4cd97bed3e953a95c79c92ad9b051a04611d9fbb"
dependencies = [
 "bytes",
 "fnv",
 "itoa",
]

[[package]]
name = "http-body"
version = "0.4.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7ceab25649e9960c0311ea418d17bee82c0dcec1bd053b5f9a66e265a693bed2"
dependencies = [
 "bytes",
 "http",
 "pin-project-lite 0.2.13",
]

[[package]]
name = "http-range-header"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "add0ab9360ddbd88cfeb3bd9574a1d85cfdfa14db10b3e21d3700dbc4328758f"

[[package]]
name = "httparse"
version = "1.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d897f394bad6a705d5f4104762e116a75639e470d80901eed05a860a95cb1904"

[[package]]
name = "httpdate"
version = "1.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "df3b46402a9d5adb4c86a0cf463f42e19994e3ee891101b1841f30a545cb49a9"

[[package]]
name = "humantime"
version = "2.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9a3a5bfb195931eeb336b2a7b4d761daec841b97f947d34394601737a7bba5e4"

[[package]]
name = "hyper"
version = "0.14.28"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bf96e135eb83a2a8ddf766e426a841d8ddd7449d5f00d34ea02b41d2f19eef80"
dependencies = [
 "bytes",
 "futures-channel",
 "futures-core",
 "futures-util",
 "h2",
 "http",
 "http-body",
 "httparse",
 "httpdate",
 "itoa",
 "pin-project-lite 0.2.13",
 "socket2 0.5.5",
 "tokio",
 "tower-service",
 "tracing",
 "want",
]

[[package]]
name = "hyper-rustls"
version = "0.24.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ec3efd23720e2049821a693cbc7e65ea87c72f1c58ff2f9522ff332b1491e590"
dependencies = [
 "futures-util",
 "http",
 "hyper",
 "log",
 "rustls 0.21.10",
 "rustls-native-certs",
 "tokio",
 "tokio-rustls",
 "webpki-roots 0.25.3",
]

[[package]]
name = "iana-time-zone"
version = "0.1.59"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b6a67363e2aa4443928ce15e57ebae94fd8949958fd1223c4cfc0cd473ad7539"
dependencies = [
 "android_system_properties",
 "core-foundation-sys",
 "iana-time-zone-haiku",
 "js-sys",
 "wasm-bindgen",
 "windows-core 0.52.0",
]

[[package]]
name = "iana-time-zone-haiku"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f31827a206f56af32e590ba56d5d2d085f558508192593743f16b2306495269f"
dependencies = [
 "cc",
]

[[package]]
name = "idna"
version = "0.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "418a0a6fab821475f634efe3ccc45c013f742efe03d853e8d3355d5cb850ecf8"
dependencies = [
 "matches",
 "unicode-bidi",
 "unicode-normalization",
]

[[package]]
name = "idna"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "634d9b1461af396cad843f47fdba5597a4f9e6ddd4bfb6ff5d85028c25cb12f6"
dependencies = [
 "unicode-bidi",
 "unicode-normalization",
]

[[package]]
name = "if-addrs"
version = "0.10.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cabb0019d51a643781ff15c9c8a3e5dedc365c47211270f4e8f82812fedd8f0a"
dependencies = [
 "libc",
 "windows-sys 0.48.0",
]

[[package]]
name = "if-watch"
version = "3.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d6b0422c86d7ce0e97169cc42e04ae643caf278874a7a3c87b8150a220dc7e1e"
dependencies = [
 "async-io",
 "core-foundation",
 "fnv",
 "futures",
 "if-addrs",
 "ipnet",
 "log",
 "rtnetlink",
 "system-configuration",
 "tokio",
 "windows",
]

[[package]]
name = "impl-codec"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ba6a270039626615617f3f36d15fc827041df3b78c439da2cadfa47455a77f2f"
dependencies = [
 "parity-scale-codec 3.6.9",
]

[[package]]
name = "impl-serde"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ebc88fc67028ae3db0c853baa36269d398d5f45b6982f95549ff5def78c935cd"
dependencies = [
 "serde",
]

[[package]]
name = "impl-trait-for-tuples"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "11d7a9f6330b71fea57921c9b61c47ee6e84f72d394754eff6163ae67e7395eb"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "include_dir"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "18762faeff7122e89e0857b02f7ce6fcc0d101d5e9ad2ad7846cc01d61b7f19e"
dependencies = [
 "include_dir_macros",
]

[[package]]
name = "include_dir_macros"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b139284b5cf57ecfa712bcc66950bb635b31aff41c188e8a4cfc758eca374a3f"
dependencies = [
 "proc-macro2",
 "quote",
]

[[package]]
name = "index-fixed"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4161ceaf2f41b6cd3f6502f5da085d4ad4393a51e0c70ed2fce1d5698d798fae"

[[package]]
name = "indexmap"
version = "1.9.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bd070e393353796e801d209ad339e89596eb4c8d430d18ede6a1cced8fafbd99"
dependencies = [
 "autocfg",
 "hashbrown 0.12.3",
 "serde",
]

[[package]]
name = "indexmap"
version = "2.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d530e1a18b1cb4c484e6e34556a0d948706958449fca0cab753d649f2bce3d1f"
dependencies = [
 "equivalent",
 "hashbrown 0.14.3",
]

[[package]]
name = "indicatif"
version = "0.17.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fb28741c9db9a713d93deb3bb9515c20788cef5815265bee4980e87bde7e0f25"
dependencies = [
 "console",
 "instant",
 "number_prefix",
 "portable-atomic",
 "unicode-width",
]

[[package]]
name = "inout"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a0c10553d664a4d0bcff9f4215d0aac67a639cc68ef660840afe309b807bc9f5"
dependencies = [
 "generic-array 0.14.7",
]

[[package]]
name = "instant"
version = "0.1.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7a5bbe824c507c5da5956355e86a746d82e0e1464f65d862cc5e71da70e94b2c"
dependencies = [
 "cfg-if",
]

[[package]]
name = "integer-sqrt"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "276ec31bcb4a9ee45f58bec6f9ec700ae4cf4f4f8f2fa7e06cb406bd5ffdd770"
dependencies = [
 "num-traits",
]

[[package]]
name = "io-lifetimes"
version = "1.0.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eae7b9aee968036d54dce06cebaefd919e4472e753296daccd6d344e3e2df0c2"
dependencies = [
 "hermit-abi 0.3.4",
 "libc",
 "windows-sys 0.48.0",
]

[[package]]
name = "ip_network"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "aa2f047c0a98b2f299aa5d6d7088443570faae494e9ae1305e48be000c9e0eb1"

[[package]]
name = "ipconfig"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b58db92f96b720de98181bbbe63c831e87005ab460c1bf306eb2622b4707997f"
dependencies = [
 "socket2 0.5.5",
 "widestring",
 "windows-sys 0.48.0",
 "winreg",
]

[[package]]
name = "ipnet"
version = "2.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8f518f335dce6725a761382244631d86cf0ccb2863413590b31338feb467f9c3"

[[package]]
name = "is-terminal"
version = "0.4.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0bad00257d07be169d870ab665980b06cdb366d792ad690bf2e76876dc503455"
dependencies = [
 "hermit-abi 0.3.4",
 "rustix 0.38.30",
 "windows-sys 0.52.0",
]

[[package]]
name = "itertools"
version = "0.10.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b0fd2260e829bddf4cb6ea802289de2f86d6a7a690192fbe91b3f46e0f2c8473"
dependencies = [
 "either",
]

[[package]]
name = "itoa"
version = "1.0.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b1a46d1a171d865aa5f83f92695765caa047a9b4cbae2cbf37dbd613a793fd4c"

[[package]]
name = "jobserver"
version = "0.1.27"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8c37f63953c4c63420ed5fd3d6d398c719489b9f872b9fa683262f8edd363c7d"
dependencies = [
 "libc",
]

[[package]]
name = "js-sys"
version = "0.3.67"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9a1d36f1235bc969acba30b7f5990b864423a6068a10f7c90ae8f0112e3a59d1"
dependencies = [
 "wasm-bindgen",
]

[[package]]
name = "jsonrpsee"
version = "0.16.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "367a292944c07385839818bb71c8d76611138e2dedb0677d035b8da21d29c78b"
dependencies = [
 "jsonrpsee-core",
 "jsonrpsee-http-client",
 "jsonrpsee-proc-macros",
 "jsonrpsee-server",
 "jsonrpsee-types",
 "jsonrpsee-ws-client",
 "tracing",
]

[[package]]
name = "jsonrpsee-client-transport"
version = "0.16.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c8b3815d9f5d5de348e5f162b316dc9cdf4548305ebb15b4eb9328e66cf27d7a"
dependencies = [
 "futures-util",
 "http",
 "jsonrpsee-core",
 "jsonrpsee-types",
 "pin-project",
 "rustls-native-certs",
 "soketto",
 "thiserror",
 "tokio",
 "tokio-rustls",
 "tokio-util",
 "tracing",
 "webpki-roots 0.25.3",
]

[[package]]
name = "jsonrpsee-core"
version = "0.16.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2b5dde66c53d6dcdc8caea1874a45632ec0fcf5b437789f1e45766a1512ce803"
dependencies = [
 "anyhow",
 "arrayvec 0.7.4",
 "async-lock 2.8.0",
 "async-trait",
 "beef",
 "futures-channel",
 "futures-timer",
 "futures-util",
 "globset",
 "hyper",
 "jsonrpsee-types",
 "parking_lot 0.12.1",
 "rand 0.8.5",
 "rustc-hash",
 "serde",
 "serde_json",
 "soketto",
 "thiserror",
 "tokio",
 "tracing",
]

[[package]]
name = "jsonrpsee-http-client"
version = "0.16.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7e5f9fabdd5d79344728521bb65e3106b49ec405a78b66fbff073b72b389fa43"
dependencies = [
 "async-trait",
 "hyper",
 "hyper-rustls",
 "jsonrpsee-core",
 "jsonrpsee-types",
 "rustc-hash",
 "serde",
 "serde_json",
 "thiserror",
 "tokio",
 "tracing",
]

[[package]]
name = "jsonrpsee-proc-macros"
version = "0.16.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "44e8ab85614a08792b9bff6c8feee23be78c98d0182d4c622c05256ab553892a"
dependencies = [
 "heck",
 "proc-macro-crate 1.1.3",
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "jsonrpsee-server"
version = "0.16.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cf4d945a6008c9b03db3354fb3c83ee02d2faa9f2e755ec1dfb69c3551b8f4ba"
dependencies = [
 "futures-channel",
 "futures-util",
 "http",
 "hyper",
 "jsonrpsee-core",
 "jsonrpsee-types",
 "serde",
 "serde_json",
 "soketto",
 "tokio",
 "tokio-stream",
 "tokio-util",
 "tower",
 "tracing",
]

[[package]]
name = "jsonrpsee-types"
version = "0.16.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "245ba8e5aa633dd1c1e4fae72bce06e71f42d34c14a2767c6b4d173b57bee5e5"
dependencies = [
 "anyhow",
 "beef",
 "serde",
 "serde_json",
 "thiserror",
 "tracing",
]

[[package]]
name = "jsonrpsee-ws-client"
version = "0.16.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4e1b3975ed5d73f456478681a417128597acd6a2487855fdb7b4a3d4d195bf5e"
dependencies = [
 "http",
 "jsonrpsee-client-transport",
 "jsonrpsee-core",
 "jsonrpsee-types",
]

[[package]]
name = "k256"
version = "0.13.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "956ff9b67e26e1a6a866cb758f12c6f8746208489e3e4a4b5580802f2f0a587b"
dependencies = [
 "cfg-if",
 "ecdsa",
 "elliptic-curve",
 "once_cell",
 "sha2 0.10.8",
]

[[package]]
name = "keccak"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ecc2af9a1119c51f12a14607e783cb977bde58bc069ff0c3da1095e635d70654"
dependencies = [
 "cpufeatures",
]

[[package]]
name = "kvdb"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e7d770dcb02bf6835887c3a979b5107a04ff4bbde97a5f0928d27404a155add9"
dependencies = [
 "smallvec",
]

[[package]]
name = "kvdb-memorydb"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bf7a85fe66f9ff9cd74e169fdd2c94c6e1e74c412c99a73b4df3200b5d3760b2"
dependencies = [
 "kvdb",
 "parking_lot 0.12.1",
]

[[package]]
name = "kvdb-rocksdb"
version = "0.19.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b644c70b92285f66bfc2032922a79000ea30af7bc2ab31902992a5dcb9b434f6"
dependencies = [
 "kvdb",
 "num_cpus",
 "parking_lot 0.12.1",
 "regex",
 "rocksdb",
 "smallvec",
]

[[package]]
name = "lazy_static"
version = "1.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e2abad23fbc42b3700f2f279844dc832adb2b2eb069b2df918f455c4e18cc646"
dependencies = [
 "spin 0.5.2",
]

[[package]]
name = "lazycell"
version = "1.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "830d08ce1d1d941e6b30645f1a0eb5643013d835ce3779a5fc208261dbe10f55"

[[package]]
name = "libc"
version = "0.2.152"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "13e3bf6590cbc649f4d1a3eefc9d5d6eb746f5200ffb04e5e142700b8faa56e7"

[[package]]
name = "libloading"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c571b676ddfc9a8c12f1f3d3085a7b163966a8fd8098a90640953ce5f6170161"
dependencies = [
 "cfg-if",
 "windows-sys 0.48.0",
]

[[package]]
name = "libp2p"
version = "0.51.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f35eae38201a993ece6bdc823292d6abd1bffed1c4d0f4a3517d2bd8e1d917fe"
dependencies = [
 "bytes",
 "futures",
 "futures-timer",
 "getrandom 0.2.12",
 "instant",
 "libp2p-allow-block-list",
 "libp2p-connection-limits",
 "libp2p-core",
 "libp2p-dns",
 "libp2p-identify",
 "libp2p-identity",
 "libp2p-kad",
 "libp2p-mdns",
 "libp2p-metrics",
 "libp2p-noise",
 "libp2p-ping",
 "libp2p-quic",
 "libp2p-request-response",
 "libp2p-swarm",
 "libp2p-tcp",
 "libp2p-wasm-ext",
 "libp2p-websocket",
 "libp2p-yamux",
 "multiaddr",
 "pin-project",
]

[[package]]
name = "libp2p-allow-block-list"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "510daa05efbc25184458db837f6f9a5143888f1caa742426d92e1833ddd38a50"
dependencies = [
 "libp2p-core",
 "libp2p-identity",
 "libp2p-swarm",
 "void",
]

[[package]]
name = "libp2p-connection-limits"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4caa33f1d26ed664c4fe2cca81a08c8e07d4c1c04f2f4ac7655c2dd85467fda0"
dependencies = [
 "libp2p-core",
 "libp2p-identity",
 "libp2p-swarm",
 "void",
]

[[package]]
name = "libp2p-core"
version = "0.39.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3c1df63c0b582aa434fb09b2d86897fa2b419ffeccf934b36f87fcedc8e835c2"
dependencies = [
 "either",
 "fnv",
 "futures",
 "futures-timer",
 "instant",
 "libp2p-identity",
 "log",
 "multiaddr",
 "multihash",
 "multistream-select",
 "once_cell",
 "parking_lot 0.12.1",
 "pin-project",
 "quick-protobuf",
 "rand 0.8.5",
 "rw-stream-sink",
 "smallvec",
 "thiserror",
 "unsigned-varint",
 "void",
]

[[package]]
name = "libp2p-dns"
version = "0.39.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "146ff7034daae62077c415c2376b8057368042df6ab95f5432ad5e88568b1554"
dependencies = [
 "futures",
 "libp2p-core",
 "log",
 "parking_lot 0.12.1",
 "smallvec",
 "trust-dns-resolver",
]

[[package]]
name = "libp2p-identify"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5455f472243e63b9c497ff320ded0314254a9eb751799a39c283c6f20b793f3c"
dependencies = [
 "asynchronous-codec",
 "either",
 "futures",
 "futures-timer",
 "libp2p-core",
 "libp2p-identity",
 "libp2p-swarm",
 "log",
 "lru",
 "quick-protobuf",
 "quick-protobuf-codec",
 "smallvec",
 "thiserror",
 "void",
]

[[package]]
name = "libp2p-identity"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "276bb57e7af15d8f100d3c11cbdd32c6752b7eef4ba7a18ecf464972c07abcce"
dependencies = [
 "bs58 0.4.0",
 "ed25519-dalek",
 "log",
 "multiaddr",
 "multihash",
 "quick-protobuf",
 "rand 0.8.5",
 "sha2 0.10.8",
 "thiserror",
 "zeroize",
]

[[package]]
name = "libp2p-kad"
version = "0.43.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "39d5ef876a2b2323d63c258e63c2f8e36f205fe5a11f0b3095d59635650790ff"
dependencies = [
 "arrayvec 0.7.4",
 "asynchronous-codec",
 "bytes",
 "either",
 "fnv",
 "futures",
 "futures-timer",
 "instant",
 "libp2p-core",
 "libp2p-identity",
 "libp2p-swarm",
 "log",
 "quick-protobuf",
 "rand 0.8.5",
 "sha2 0.10.8",
 "smallvec",
 "thiserror",
 "uint",
 "unsigned-varint",
 "void",
]

[[package]]
name = "libp2p-mdns"
version = "0.43.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "19983e1f949f979a928f2c603de1cf180cc0dc23e4ac93a62651ccb18341460b"
dependencies = [
 "data-encoding",
 "futures",
 "if-watch",
 "libp2p-core",
 "libp2p-identity",
 "libp2p-swarm",
 "log",
 "rand 0.8.5",
 "smallvec",
 "socket2 0.4.10",
 "tokio",
 "trust-dns-proto",
 "void",
]

[[package]]
name = "libp2p-metrics"
version = "0.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a42ec91e227d7d0dafa4ce88b333cdf5f277253873ab087555c92798db2ddd46"
dependencies = [
 "libp2p-core",
 "libp2p-identify",
 "libp2p-kad",
 "libp2p-ping",
 "libp2p-swarm",
 "prometheus-client",
]

[[package]]
name = "libp2p-noise"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9c3673da89d29936bc6435bafc638e2f184180d554ce844db65915113f86ec5e"
dependencies = [
 "bytes",
 "curve25519-dalek 3.2.0",
 "futures",
 "libp2p-core",
 "libp2p-identity",
 "log",
 "once_cell",
 "quick-protobuf",
 "rand 0.8.5",
 "sha2 0.10.8",
 "snow",
 "static_assertions",
 "thiserror",
 "x25519-dalek 1.1.1",
 "zeroize",
]

[[package]]
name = "libp2p-ping"
version = "0.42.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3e57759c19c28a73ef1eb3585ca410cefb72c1a709fcf6de1612a378e4219202"
dependencies = [
 "either",
 "futures",
 "futures-timer",
 "instant",
 "libp2p-core",
 "libp2p-swarm",
 "log",
 "rand 0.8.5",
 "void",
]

[[package]]
name = "libp2p-quic"
version = "0.7.0-alpha.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c6b26abd81cd2398382a1edfe739b539775be8a90fa6914f39b2ab49571ec735"
dependencies = [
 "bytes",
 "futures",
 "futures-timer",
 "if-watch",
 "libp2p-core",
 "libp2p-identity",
 "libp2p-tls",
 "log",
 "parking_lot 0.12.1",
 "quinn-proto",
 "rand 0.8.5",
 "rustls 0.20.9",
 "thiserror",
 "tokio",
]

[[package]]
name = "libp2p-request-response"
version = "0.24.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7ffdb374267d42dc5ed5bc53f6e601d4a64ac5964779c6e40bb9e4f14c1e30d5"
dependencies = [
 "async-trait",
 "futures",
 "instant",
 "libp2p-core",
 "libp2p-identity",
 "libp2p-swarm",
 "rand 0.8.5",
 "smallvec",
]

[[package]]
name = "libp2p-swarm"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "903b3d592d7694e56204d211f29d31bc004be99386644ba8731fc3e3ef27b296"
dependencies = [
 "either",
 "fnv",
 "futures",
 "futures-timer",
 "instant",
 "libp2p-core",
 "libp2p-identity",
 "libp2p-swarm-derive",
 "log",
 "rand 0.8.5",
 "smallvec",
 "tokio",
 "void",
]

[[package]]
name = "libp2p-swarm-derive"
version = "0.32.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0fba456131824ab6acd4c7bf61e9c0f0a3014b5fc9868ccb8e10d344594cdc4f"
dependencies = [
 "heck",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "libp2p-tcp"
version = "0.39.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "33d33698596d7722d85d3ab0c86c2c322254fce1241e91208e3679b4eb3026cf"
dependencies = [
 "futures",
 "futures-timer",
 "if-watch",
 "libc",
 "libp2p-core",
 "log",
 "socket2 0.4.10",
 "tokio",
]

[[package]]
name = "libp2p-tls"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ff08d13d0dc66e5e9ba6279c1de417b84fa0d0adc3b03e5732928c180ec02781"
dependencies = [
 "futures",
 "futures-rustls",
 "libp2p-core",
 "libp2p-identity",
 "rcgen",
 "ring 0.16.20",
 "rustls 0.20.9",
 "thiserror",
 "webpki",
 "x509-parser",
 "yasna",
]

[[package]]
name = "libp2p-wasm-ext"
version = "0.39.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "77dff9d32353a5887adb86c8afc1de1a94d9e8c3bc6df8b2201d7cdf5c848f43"
dependencies = [
 "futures",
 "js-sys",
 "libp2p-core",
 "parity-send-wrapper",
 "wasm-bindgen",
 "wasm-bindgen-futures",
]

[[package]]
name = "libp2p-websocket"
version = "0.41.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "111273f7b3d3510524c752e8b7a5314b7f7a1fee7e68161c01a7d72cbb06db9f"
dependencies = [
 "either",
 "futures",
 "futures-rustls",
 "libp2p-core",
 "log",
 "parking_lot 0.12.1",
 "quicksink",
 "rw-stream-sink",
 "soketto",
 "url",
 "webpki-roots 0.22.6",
]

[[package]]
name = "libp2p-yamux"
version = "0.43.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4dcd21d950662700a385d4c6d68e2f5f54d778e97068cdd718522222ef513bda"
dependencies = [
 "futures",
 "libp2p-core",
 "log",
 "thiserror",
 "yamux",
]

[[package]]
name = "libredox"
version = "0.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "85c833ca1e66078851dba29046874e38f08b2c883700aa29a03ddd3b23814ee8"
dependencies = [
 "bitflags 2.4.2",
 "libc",
 "redox_syscall 0.4.1",
]

[[package]]
name = "librocksdb-sys"
version = "0.11.0+8.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d3386f101bcb4bd252d8e9d2fb41ec3b0862a15a62b478c355b2982efa469e3e"
dependencies = [
 "bindgen",
 "bzip2-sys",
 "cc",
 "glob",
 "libc",
 "libz-sys",
 "tikv-jemalloc-sys",
]

[[package]]
name = "libsecp256k1"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "95b09eff1b35ed3b33b877ced3a691fc7a481919c7e29c53c906226fcf55e2a1"
dependencies = [
 "arrayref",
 "base64 0.13.1",
 "digest 0.9.0",
 "hmac-drbg",
 "libsecp256k1-core",
 "libsecp256k1-gen-ecmult",
 "libsecp256k1-gen-genmult",
 "rand 0.8.5",
 "serde",
 "sha2 0.9.9",
 "typenum",
]

[[package]]
name = "libsecp256k1-core"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5be9b9bb642d8522a44d533eab56c16c738301965504753b03ad1de3425d5451"
dependencies = [
 "crunchy",
 "digest 0.9.0",
 "subtle",
]

[[package]]
name = "libsecp256k1-gen-ecmult"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3038c808c55c87e8a172643a7d87187fc6c4174468159cb3090659d55bcb4809"
dependencies = [
 "libsecp256k1-core",
]

[[package]]
name = "libsecp256k1-gen-genmult"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3db8d6ba2cec9eacc40e6e8ccc98931840301f1006e95647ceb2dd5c3aa06f7c"
dependencies = [
 "libsecp256k1-core",
]

[[package]]
name = "libz-sys"
version = "1.1.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "295c17e837573c8c821dbaeb3cceb3d745ad082f7572191409e69cbc1b3fd050"
dependencies = [
 "cc",
 "pkg-config",
 "vcpkg",
]

[[package]]
name = "link-cplusplus"
version = "1.0.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9d240c6f7e1ba3a28b0249f774e6a9dd0175054b52dfbb61b16eb8505c3785c9"
dependencies = [
 "cc",
]

[[package]]
name = "linked-hash-map"
version = "0.5.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0717cef1bc8b636c6e1c1bbdefc09e6322da8a9321966e8928ef80d20f7f770f"

[[package]]
name = "linked_hash_set"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "47186c6da4d81ca383c7c47c1bfc80f4b95f4720514d860a5407aaf4233f9588"
dependencies = [
 "linked-hash-map",
]

[[package]]
name = "linregress"
version = "0.5.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4de04dcecc58d366391f9920245b85ffa684558a5ef6e7736e754347c3aea9c2"
dependencies = [
 "nalgebra",
]

[[package]]
name = "linux-raw-sys"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f051f77a7c8e6957c0696eac88f26b0117e54f52d3fc682ab19397a8812846a4"

[[package]]
name = "linux-raw-sys"
version = "0.4.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "01cda141df6706de531b6c46c3a33ecca755538219bd484262fa09410c13539c"

[[package]]
name = "lite-json"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0460d985423a026b4d9b828a7c6eed1bcf606f476322f3f9b507529686a61715"
dependencies = [
 "lite-parser",
]

[[package]]
name = "lite-parser"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0c50092e40e0ccd1bf2015a10333fde0502ff95b832b0895dc1ca0d7ac6c52f6"
dependencies = [
 "paste 0.1.18",
]

[[package]]
name = "lock_api"
version = "0.4.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3c168f8615b12bc01f9c17e2eb0cc07dcae1940121185446edc3744920e8ef45"
dependencies = [
 "autocfg",
 "scopeguard",
]

[[package]]
name = "log"
version = "0.4.20"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b5e6163cb8c49088c2c36f57875e58ccd8c87c7427f7fbd50ea6710b2f3f2e8f"

[[package]]
name = "lru"
version = "0.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "718e8fae447df0c7e1ba7f5189829e63fd536945c8988d61444c19039f16b670"
dependencies = [
 "hashbrown 0.13.2",
]

[[package]]
name = "lru-cache"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "31e24f1ad8321ca0e8a1e0ac13f23cb668e6f5466c2c57319f6a5cf1cc8e3b1c"
dependencies = [
 "linked-hash-map",
]

[[package]]
name = "lz4"
version = "1.24.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7e9e2dd86df36ce760a60f6ff6ad526f7ba1f14ba0356f8254fb6905e6494df1"
dependencies = [
 "libc",
 "lz4-sys",
]

[[package]]
name = "lz4-sys"
version = "1.9.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "57d27b317e207b10f69f5e75494119e391a96f48861ae870d1da6edac98ca900"
dependencies = [
 "cc",
 "libc",
]

[[package]]
name = "mach"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b823e83b2affd8f40a9ee8c29dbc56404c1e34cd2710921f2801e2cf29527afa"
dependencies = [
 "libc",
]

[[package]]
name = "macro_magic"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "aee866bfee30d2d7e83835a4574aad5b45adba4cc807f2a3bbba974e5d4383c9"
dependencies = [
 "macro_magic_core",
 "macro_magic_macros",
 "quote",
 "syn 2.0.48",
]

[[package]]
name = "macro_magic_core"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7e766a20fd9c72bab3e1e64ed63f36bd08410e75803813df210d1ce297d7ad00"
dependencies = [
 "const-random",
 "derive-syn-parse",
 "macro_magic_core_macros",
 "proc-macro2",
 "quote",
 "syn 2.0.48",
]

[[package]]
name = "macro_magic_core_macros"
version = "0.4.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d710e1214dffbab3b5dacb21475dde7d6ed84c69ff722b3a47a782668d44fbac"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.48",
]

[[package]]
name = "macro_magic_macros"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b8fb85ec1620619edf2984a7693497d4ec88a9665d8b87e942856884c92dbf2a"
dependencies = [
 "macro_magic_core",
 "quote",
 "syn 2.0.48",
]

[[package]]
name = "maplit"
version = "1.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3e2e65a1a2e43cfcb47a895c4c8b10d1f4a61097f9f254f183aee60cad9c651d"

[[package]]
name = "match_cfg"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ffbee8634e0d45d258acb448e7eaab3fce7a0a467395d4d9f228e3c1f01fb2e4"

[[package]]
name = "matchers"
version = "0.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f099785f7595cc4b4553a174ce30dd7589ef93391ff414dbb67f62392b9e0ce1"
dependencies = [
 "regex-automata 0.1.10",
]

[[package]]
name = "matches"
version = "0.1.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2532096657941c2fea9c289d370a250971c689d4f143798ff67113ec042024a5"

[[package]]
name = "matrixmultiply"
version = "0.3.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7574c1cf36da4798ab73da5b215bbf444f50718207754cb522201d78d1cd0ff2"
dependencies = [
 "autocfg",
 "rawpointer",
]

[[package]]
name = "memchr"
version = "2.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "523dc4f511e55ab87b694dc30d0f820d60906ef06413f93d4d7a1385599cc149"

[[package]]
name = "memfd"
version = "0.6.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b2cffa4ad52c6f791f4f8b15f0c05f9824b2ced1160e88cc393d64fff9a8ac64"
dependencies = [
 "rustix 0.38.30",
]

[[package]]
name = "memmap2"
version = "0.5.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "83faa42c0a078c393f6b29d5db232d8be22776a891f8f56e5284faee4a20b327"
dependencies = [
 "libc",
]

[[package]]
name = "memoffset"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d61c719bcfbcf5d62b3a09efa6088de8c54bc0bfcd3ea7ae39fcc186108b8de1"
dependencies = [
 "autocfg",
]

[[package]]
name = "memory-db"
version = "0.32.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "808b50db46293432a45e63bc15ea51e0ab4c0a1647b8eb114e31a3e698dd6fbe"
dependencies = [
 "hash-db",
]

[[package]]
name = "merlin"
version = "2.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4e261cf0f8b3c42ded9f7d2bb59dea03aa52bc8a1cbc7482f9fc3fd1229d3b42"
dependencies = [
 "byteorder",
 "keccak",
 "rand_core 0.5.1",
 "zeroize",
]

[[package]]
name = "merlin"
version = "3.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "58c38e2799fc0978b65dfff8023ec7843e2330bb462f19198840b34b6582397d"
dependencies = [
 "byteorder",
 "keccak",
 "rand_core 0.6.4",
 "zeroize",
]

[[package]]
name = "minimal-lexical"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "68354c5c6bd36d73ff3feceb05efa59b6acb7626617f4962be322a825e61f79a"

[[package]]
name = "miniz_oxide"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e7810e0be55b428ada41041c41f32c9f1a42817901b4ccf45fa3d4b6561e74c7"
dependencies = [
 "adler",
]

[[package]]
name = "mio"
version = "0.8.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8f3d0b296e374a4e6f3c7b0a1f5a51d748a0d34c85e7dc48fc3fa9a87657fe09"
dependencies = [
 "libc",
 "wasi 0.11.0+wasi-snapshot-preview1",
 "windows-sys 0.48.0",
]

[[package]]
name = "mockall"
version = "0.11.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4c84490118f2ee2d74570d114f3d0493cbf02790df303d2707606c3e14e07c96"
dependencies = [
 "cfg-if",
 "downcast",
 "fragile",
 "lazy_static",
 "mockall_derive",
 "predicates",
 "predicates-tree",
]

[[package]]
name = "mockall_derive"
version = "0.11.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "22ce75669015c4f47b289fd4d4f56e894e4c96003ffdf3ac51313126f94c6cbb"
dependencies = [
 "cfg-if",
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "multiaddr"
version = "0.17.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2b36f567c7099511fa8612bbbb52dda2419ce0bdbacf31714e3a5ffdb766d3bd"
dependencies = [
 "arrayref",
 "byteorder",
 "data-encoding",
 "log",
 "multibase",
 "multihash",
 "percent-encoding",
 "serde",
 "static_assertions",
 "unsigned-varint",
 "url",
]

[[package]]
name = "multibase"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9b3539ec3c1f04ac9748a260728e855f261b4977f5c3406612c884564f329404"
dependencies = [
 "base-x",
 "data-encoding",
 "data-encoding-macro",
]

[[package]]
name = "multihash"
version = "0.17.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "835d6ff01d610179fbce3de1694d007e500bf33a7f29689838941d6bf783ae40"
dependencies = [
 "blake2b_simd",
 "blake2s_simd",
 "blake3",
 "core2",
 "digest 0.10.7",
 "multihash-derive",
 "sha2 0.10.8",
 "sha3",
 "unsigned-varint",
]

[[package]]
name = "multihash-derive"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d6d4752e6230d8ef7adf7bd5d8c4b1f6561c1014c5ba9a37445ccefe18aa1db"
dependencies = [
 "proc-macro-crate 1.1.3",
 "proc-macro-error",
 "proc-macro2",
 "quote",
 "syn 1.0.109",
 "synstructure",
]

[[package]]
name = "multimap"
version = "0.8.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e5ce46fe64a9d73be07dcbe690a38ce1b293be448fd8ce1e6c1b8062c9f72c6a"

[[package]]
name = "multistream-select"
version = "0.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c8552ab875c1313b97b8d20cb857b9fd63e2d1d6a0a1b53ce9821e575405f27a"
dependencies = [
 "bytes",
 "futures",
 "log",
 "pin-project",
 "smallvec",
 "unsigned-varint",
]

[[package]]
name = "nalgebra"
version = "0.32.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "307ed9b18cc2423f29e83f84fd23a8e73628727990181f18641a8b5dc2ab1caa"
dependencies = [
 "approx",
 "matrixmultiply",
 "nalgebra-macros",
 "num-complex",
 "num-rational",
 "num-traits",
 "simba",
 "typenum",
]

[[package]]
name = "nalgebra-macros"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "91761aed67d03ad966ef783ae962ef9bbaca728d2dd7ceb7939ec110fffad998"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "names"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e7d66043b25d4a6cccb23619d10c19c25304b355a7dccd4a8e11423dd2382146"
dependencies = [
 "rand 0.8.5",
]

[[package]]
name = "netlink-packet-core"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "345b8ab5bd4e71a2986663e88c56856699d060e78e152e6e9d7966fcd5491297"
dependencies = [
 "anyhow",
 "byteorder",
 "libc",
 "netlink-packet-utils",
]

[[package]]
name = "netlink-packet-route"
version = "0.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d9ea4302b9759a7a88242299225ea3688e63c85ea136371bb6cf94fd674efaab"
dependencies = [
 "anyhow",
 "bitflags 1.3.2",
 "byteorder",
 "libc",
 "netlink-packet-core",
 "netlink-packet-utils",
]

[[package]]
name = "netlink-packet-utils"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0ede8a08c71ad5a95cdd0e4e52facd37190977039a4704eb82a283f713747d34"
dependencies = [
 "anyhow",
 "byteorder",
 "paste 1.0.14",
 "thiserror",
]

[[package]]
name = "netlink-proto"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "65b4b14489ab424703c092062176d52ba55485a89c076b4f9db05092b7223aa6"
dependencies = [
 "bytes",
 "futures",
 "log",
 "netlink-packet-core",
 "netlink-sys",
 "thiserror",
 "tokio",
]

[[package]]
name = "netlink-sys"
version = "0.8.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6471bf08e7ac0135876a9581bf3217ef0333c191c128d34878079f42ee150411"
dependencies = [
 "bytes",
 "futures",
 "libc",
 "log",
 "tokio",
]

[[package]]
name = "nix"
version = "0.24.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fa52e972a9a719cecb6864fb88568781eb706bac2cd1d4f04a648542dbf78069"
dependencies = [
 "bitflags 1.3.2",
 "cfg-if",
 "libc",
]

[[package]]
name = "nohash-hasher"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2bf50223579dc7cdcfb3bfcacf7069ff68243f8c363f62ffa99cf000a6b9c451"

[[package]]
name = "nom"
version = "7.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d273983c5a657a70a3e8f2a01329822f3b8c8172b73826411a55751e404a0a4a"
dependencies = [
 "memchr",
 "minimal-lexical",
]

[[package]]
name = "normalize-line-endings"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "61807f77802ff30975e01f4f071c8ba10c022052f98b3294119f3e615d13e5be"

[[package]]
name = "num-bigint"
version = "0.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "608e7659b5c3d7cba262d894801b9ec9d00de989e8a82bd4bef91d08da45cdc0"
dependencies = [
 "autocfg",
 "num-integer",
 "num-traits",
]

[[package]]
name = "num-complex"
version = "0.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1ba157ca0885411de85d6ca030ba7e2a83a28636056c7c699b07c8b6f7383214"
dependencies = [
 "num-traits",
]

[[package]]
name = "num-format"
version = "0.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a652d9771a63711fd3c3deb670acfbe5c30a4072e664d7a3bf5a9e1056ac72c3"
dependencies = [
 "arrayvec 0.7.4",
 "itoa",
]

[[package]]
name = "num-integer"
version = "0.1.45"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "225d3389fb3509a24c93f5c29eb6bde2586b98d9f016636dff58d7c6f7569cd9"
dependencies = [
 "autocfg",
 "num-traits",
]

[[package]]
name = "num-rational"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0638a1c9d0a3c0914158145bc76cff373a75a627e6ecbfb71cbe6f453a5a19b0"
dependencies = [
 "autocfg",
 "num-integer",
 "num-traits",
]

[[package]]
name = "num-traits"
version = "0.2.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "39e3200413f237f41ab11ad6d161bc7239c84dcb631773ccd7de3dfe4b5c267c"
dependencies = [
 "autocfg",
]

[[package]]
name = "num_cpus"
version = "1.16.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4161fcb6d602d4d2081af7c3a45852d875a03dd337a6bfdd6e06407b61342a43"
dependencies = [
 "hermit-abi 0.3.4",
 "libc",
]

[[package]]
name = "number_prefix"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "830b246a0e5f20af87141b25c173cd1b609bd7779a4617d6ec582abaf90870f3"

[[package]]
name = "object"
version = "0.30.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "03b4680b86d9cfafba8fc491dc9b6df26b68cf40e9e6cd73909194759a63c385"
dependencies = [
 "crc32fast",
 "hashbrown 0.13.2",
 "indexmap 1.9.3",
 "memchr",
]

[[package]]
name = "object"
version = "0.32.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a6a622008b6e321afc04970976f62ee297fdbaa6f95318ca343e3eebb9648441"
dependencies = [
 "memchr",
]

[[package]]
name = "oid-registry"
version = "0.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9bedf36ffb6ba96c2eb7144ef6270557b52e54b20c0a8e1eb2ff99a6c6959bff"
dependencies = [
 "asn1-rs",
]

[[package]]
name = "once_cell"
version = "1.19.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3fdb12b2476b595f9358c5161aa467c2438859caa136dec86c26fdd2efe17b92"

[[package]]
name = "opaque-debug"
version = "0.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2839e79665f131bdb5782e51f2c6c9599c133c6098982a54c794358bf432529c"

[[package]]
name = "opaque-debug"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "624a8340c38c1b80fd549087862da4ba43e08858af025b236e509b6649fc13d5"

[[package]]
name = "openssl-probe"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ff011a302c396a5197692431fc1948019154afc178baf7d8e37367442a4601cf"

[[package]]
name = "pallet-aura"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "frame-support",
 "frame-system",
 "log",
 "pallet-timestamp",
 "parity-scale-codec 3.6.9",
 "scale-info",
 "sp-application-crypto",
 "sp-consensus-aura",
 "sp-runtime",
 "sp-std 8.0.0",
]

[[package]]
name = "pallet-authorship"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "frame-support",
 "frame-system",
 "impl-trait-for-tuples",
 "parity-scale-codec 3.6.9",
 "scale-info",
 "sp-runtime",
 "sp-std 8.0.0",
]

[[package]]
name = "pallet-balances"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "frame-benchmarking",
 "frame-support",
 "frame-system",
 "log",
 "parity-scale-codec 3.6.9",
 "scale-info",
 "sp-runtime",
 "sp-std 8.0.0",
]

[[package]]
name = "pallet-burning"
version = "2.9.3"
dependencies = [
 "frame-benchmarking",
 "frame-support",
 "frame-system",
 "pallet-balances",
 "pallet-timestamp",
 "parity-scale-codec 3.6.9",
 "scale-info",
 "sp-core",
 "sp-io",
 "sp-runtime",
 "sp-std 8.0.0",
 "sp-storage",
]

[[package]]
name = "pallet-collective"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "frame-benchmarking",
 "frame-support",
 "frame-system",
 "log",
 "parity-scale-codec 3.6.9",
 "scale-info",
 "sp-core",
 "sp-io",
 "sp-runtime",
 "sp-std 8.0.0",
]

[[package]]
name = "pallet-dao"
version = "2.9.3"
dependencies = [
 "env_logger",
 "frame-benchmarking",
 "frame-support",
 "frame-system",
 "log",
 "pallet-collective",
 "pallet-membership",
 "pallet-tfgrid",
 "pallet-timestamp",
 "parity-scale-codec 3.6.9",
 "scale-info",
 "serde",
 "sp-core",
 "sp-io",
 "sp-runtime",
 "sp-std 8.0.0",
 "tfchain-support",
]

[[package]]
name = "pallet-grandpa"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "frame-benchmarking",
 "frame-support",
 "frame-system",
 "log",
 "pallet-authorship",
 "pallet-session",
 "parity-scale-codec 3.6.9",
 "scale-info",
 "sp-application-crypto",
 "sp-consensus-grandpa",
 "sp-core",
 "sp-io",
 "sp-runtime",
 "sp-session",
 "sp-staking",
 "sp-std 8.0.0",
]

[[package]]
name = "pallet-kvstore"
version = "2.9.3"
dependencies = [
 "frame-benchmarking",
 "frame-support",
 "frame-system",
 "parity-scale-codec 3.6.9",
 "scale-info",
 "sp-core",
 "sp-io",
 "sp-runtime",
 "sp-std 8.0.0",
 "sp-storage",
]

[[package]]
name = "pallet-membership"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "frame-benchmarking",
 "frame-support",
 "frame-system",
 "log",
 "parity-scale-codec 3.6.9",
 "scale-info",
 "sp-core",
 "sp-io",
 "sp-runtime",
 "sp-std 8.0.0",
]

[[package]]
name = "pallet-runtime-upgrade"
version = "2.9.3"
dependencies = [
 "frame-support",
 "frame-system",
 "parity-scale-codec 3.6.9",
 "scale-info",
 "sp-io",
 "sp-std 8.0.0",
]

[[package]]
name = "pallet-scheduler"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "docify",
 "frame-benchmarking",
 "frame-support",
 "frame-system",
 "log",
 "parity-scale-codec 3.6.9",
 "scale-info",
 "sp-io",
 "sp-runtime",
 "sp-std 8.0.0",
 "sp-weights",
]

[[package]]
name = "pallet-session"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "frame-support",
 "frame-system",
 "impl-trait-for-tuples",
 "log",
 "pallet-timestamp",
 "parity-scale-codec 3.6.9",
 "scale-info",
 "sp-core",
 "sp-io",
 "sp-runtime",
 "sp-session",
 "sp-staking",
 "sp-state-machine",
 "sp-std 8.0.0",
 "sp-trie",
]

[[package]]
name = "pallet-session-benchmarking"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "frame-benchmarking",
 "frame-support",
 "frame-system",
 "pallet-session",
 "pallet-staking",
 "parity-scale-codec 3.6.9",
 "rand 0.8.5",
 "sp-runtime",
 "sp-session",
 "sp-std 8.0.0",
]

[[package]]
name = "pallet-smart-contract"
version = "2.9.3"
dependencies = [
 "env_logger",
 "frame-benchmarking",
 "frame-support",
 "frame-system",
 "frame-try-runtime",
 "log",
 "pallet-authorship",
 "pallet-balances",
 "pallet-collective",
 "pallet-session",
 "pallet-tfgrid",
 "pallet-tft-price",
 "pallet-timestamp",
 "parity-scale-codec 3.6.9",
 "parking_lot 0.11.2",
 "parking_lot 0.12.1",
 "scale-info",
 "sp-core",
 "sp-io",
 "sp-keystore",
 "sp-runtime",
 "sp-std 8.0.0",
 "substrate-fixed",
 "substrate-validator-set",
 "tfchain-support",
]

[[package]]
name = "pallet-staking"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "frame-benchmarking",
 "frame-election-provider-support",
 "frame-support",
 "frame-system",
 "log",
 "pallet-authorship",
 "pallet-session",
 "parity-scale-codec 3.6.9",
 "scale-info",
 "serde",
 "sp-application-crypto",
 "sp-io",
 "sp-runtime",
 "sp-staking",
 "sp-std 8.0.0",
]

[[package]]
name = "pallet-tfgrid"
version = "2.9.3"
dependencies = [
 "env_logger",
 "frame-benchmarking",
 "frame-support",
 "frame-system",
 "frame-try-runtime",
 "hex",
 "hex-literal",
 "log",
 "pallet-balances",
 "pallet-collective",
 "pallet-membership",
 "pallet-timestamp",
 "parity-scale-codec 3.6.9",
 "scale-info",
 "serde_json",
 "sp-core",
 "sp-io",
 "sp-runtime",
 "sp-std 8.0.0",
 "sp-storage",
 "sp-weights",
 "tfchain-support",
 "valip",
]

[[package]]
name = "pallet-tft-bridge"
version = "2.9.3"
dependencies = [
 "frame-benchmarking",
 "frame-support",
 "frame-system",
 "log",
 "pallet-balances",
 "parity-scale-codec 3.6.9",
 "scale-info",
 "sp-core",
 "sp-io",
 "sp-runtime",
 "sp-std 8.0.0",
 "sp-storage",
 "substrate-stellar-sdk",
]

[[package]]
name = "pallet-tft-price"
version = "2.9.3"
dependencies = [
 "frame-benchmarking",
 "frame-support",
 "frame-system",
 "lite-json",
 "log",
 "pallet-authorship",
 "pallet-session",
 "parity-scale-codec 3.6.9",
 "parking_lot 0.11.2",
 "scale-info",
 "serde",
 "serde_json",
 "sp-core",
 "sp-io",
 "sp-keystore",
 "sp-runtime",
 "sp-std 8.0.0",
 "substrate-fixed",
 "substrate-validator-set",
 "tfchain-support",
]

[[package]]
name = "pallet-timestamp"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "frame-benchmarking",
 "frame-support",
 "frame-system",
 "log",
 "parity-scale-codec 3.6.9",
 "scale-info",
 "sp-inherents",
 "sp-io",
 "sp-runtime",
 "sp-std 8.0.0",
 "sp-storage",
 "sp-timestamp",
]

[[package]]
name = "pallet-transaction-payment"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "frame-support",
 "frame-system",
 "parity-scale-codec 3.6.9",
 "scale-info",
 "serde",
 "sp-core",
 "sp-io",
 "sp-runtime",
 "sp-std 8.0.0",
]

[[package]]
name = "pallet-transaction-payment-rpc"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "jsonrpsee",
 "pallet-transaction-payment-rpc-runtime-api",
 "parity-scale-codec 3.6.9",
 "sp-api",
 "sp-blockchain",
 "sp-core",
 "sp-rpc",
 "sp-runtime",
 "sp-weights",
]

[[package]]
name = "pallet-transaction-payment-rpc-runtime-api"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "pallet-transaction-payment",
 "parity-scale-codec 3.6.9",
 "sp-api",
 "sp-runtime",
 "sp-weights",
]

[[package]]
name = "pallet-utility"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "frame-benchmarking",
 "frame-support",
 "frame-system",
 "parity-scale-codec 3.6.9",
 "scale-info",
 "sp-core",
 "sp-io",
 "sp-runtime",
 "sp-std 8.0.0",
]

[[package]]
name = "pallet-validator"
version = "2.9.3"
dependencies = [
 "frame-benchmarking",
 "frame-support",
 "frame-system",
 "pallet-collective",
 "pallet-membership",
 "pallet-session",
 "parity-scale-codec 3.6.9",
 "scale-info",
 "sp-core",
 "sp-io",
 "sp-runtime",
 "sp-std 8.0.0",
 "substrate-validator-set",
]

[[package]]
name = "parity-db"
version = "0.4.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "592a28a24b09c9dc20ac8afaa6839abc417c720afe42c12e1e4a9d6aa2508d2e"
dependencies = [
 "blake2",
 "crc32fast",
 "fs2",
 "hex",
 "libc",
 "log",
 "lz4",
 "memmap2",
 "parking_lot 0.12.1",
 "rand 0.8.5",
 "siphasher",
 "snap",
 "winapi",
]

[[package]]
name = "parity-scale-codec"
version = "2.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "373b1a4c1338d9cd3d1fa53b3a11bdab5ab6bd80a20f7f7becd76953ae2be909"
dependencies = [
 "arrayvec 0.7.4",
 "byte-slice-cast",
 "impl-trait-for-tuples",
 "parity-scale-codec-derive 2.3.1",
]

[[package]]
name = "parity-scale-codec"
version = "3.6.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "881331e34fa842a2fb61cc2db9643a8fedc615e47cfcc52597d1af0db9a7e8fe"
dependencies = [
 "arrayvec 0.7.4",
 "bitvec",
 "byte-slice-cast",
 "bytes",
 "impl-trait-for-tuples",
 "parity-scale-codec-derive 3.6.9",
 "serde",
]

[[package]]
name = "parity-scale-codec-derive"
version = "2.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1557010476e0595c9b568d16dcfb81b93cdeb157612726f5170d31aa707bed27"
dependencies = [
 "proc-macro-crate 1.1.3",
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "parity-scale-codec-derive"
version = "3.6.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "be30eaf4b0a9fba5336683b38de57bb86d179a35862ba6bfcf57625d006bde5b"
dependencies = [
 "proc-macro-crate 2.0.1",
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "parity-send-wrapper"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "aa9777aa91b8ad9dd5aaa04a9b6bcb02c7f1deb952fca5a66034d5e63afc5c6f"

[[package]]
name = "parity-wasm"
version = "0.45.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e1ad0aff30c1da14b1254fcb2af73e1fa9a28670e584a626f53a369d0e157304"

[[package]]
name = "parking"
version = "2.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bb813b8af86854136c6922af0598d719255ecb2179515e6e7730d468f05c9cae"

[[package]]
name = "parking_lot"
version = "0.11.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7d17b78036a60663b797adeaee46f5c9dfebb86948d1255007a1d6be0271ff99"
dependencies = [
 "instant",
 "lock_api",
 "parking_lot_core 0.8.6",
]

[[package]]
name = "parking_lot"
version = "0.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3742b2c103b9f06bc9fff0a37ff4912935851bee6d36f3c02bcc755bcfec228f"
dependencies = [
 "lock_api",
 "parking_lot_core 0.9.9",
]

[[package]]
name = "parking_lot_core"
version = "0.8.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "60a2cfe6f0ad2bfc16aefa463b497d5c7a5ecd44a23efa72aa342d90177356dc"
dependencies = [
 "cfg-if",
 "instant",
 "libc",
 "redox_syscall 0.2.16",
 "smallvec",
 "winapi",
]

[[package]]
name = "parking_lot_core"
version = "0.9.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4c42a9226546d68acdd9c0a280d17ce19bfe27a46bf68784e4066115788d008e"
dependencies = [
 "cfg-if",
 "libc",
 "redox_syscall 0.4.1",
 "smallvec",
 "windows-targets 0.48.5",
]

[[package]]
name = "partial_sort"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7924d1d0ad836f665c9065e26d016c673ece3993f30d340068b16f282afc1156"

[[package]]
name = "paste"
version = "0.1.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "45ca20c77d80be666aef2b45486da86238fabe33e38306bd3118fe4af33fa880"
dependencies = [
 "paste-impl",
 "proc-macro-hack",
]

[[package]]
name = "paste"
version = "1.0.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "de3145af08024dea9fa9914f381a17b8fc6034dfb00f3a84013f7ff43f29ed4c"

[[package]]
name = "paste-impl"
version = "0.1.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d95a7db200b97ef370c8e6de0088252f7e0dfff7d047a28528e47456c0fc98b6"
dependencies = [
 "proc-macro-hack",
]

[[package]]
name = "pbkdf2"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d95f5254224e617595d2cc3cc73ff0a5eaf2637519e25f03388154e9378b6ffa"
dependencies = [
 "crypto-mac 0.11.1",
]

[[package]]
name = "pbkdf2"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "83a0692ec44e4cf1ef28ca317f14f8f07da2d95ec3fa01f86e4467b725e60917"
dependencies = [
 "digest 0.10.7",
]

[[package]]
name = "peeking_take_while"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "19b17cddbe7ec3f8bc800887bab5e717348c95ea2ca0b1bf0837fb964dc67099"

[[package]]
name = "pem"
version = "1.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a8835c273a76a90455d7344889b0964598e3316e2a79ede8e36f16bdcf2228b8"
dependencies = [
 "base64 0.13.1",
]

[[package]]
name = "percent-encoding"
version = "2.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e3148f5046208a5d56bcfc03053e3ca6334e51da8dfb19b6cdc8b306fae3283e"

[[package]]
name = "pest"
version = "2.7.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1f200d8d83c44a45b21764d1916299752ca035d15ecd46faca3e9a2a2bf6ad06"
dependencies = [
 "memchr",
 "thiserror",
 "ucd-trie",
]

[[package]]
name = "pest_derive"
version = "2.7.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bcd6ab1236bbdb3a49027e920e693192ebfe8913f6d60e294de57463a493cfde"
dependencies = [
 "pest",
 "pest_generator",
]

[[package]]
name = "pest_generator"
version = "2.7.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2a31940305ffc96863a735bef7c7994a00b325a7138fdbc5bda0f1a0476d3275"
dependencies = [
 "pest",
 "pest_meta",
 "proc-macro2",
 "quote",
 "syn 2.0.48",
]

[[package]]
name = "pest_meta"
version = "2.7.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a7ff62f5259e53b78d1af898941cdcdccfae7385cf7d793a6e55de5d05bb4b7d"
dependencies = [
 "once_cell",
 "pest",
 "sha2 0.10.8",
]

[[package]]
name = "petgraph"
version = "0.6.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e1d3afd2628e69da2be385eb6f2fd57c8ac7977ceeff6dc166ff1657b0e386a9"
dependencies = [
 "fixedbitset",
 "indexmap 2.1.0",
]

[[package]]
name = "pin-project"
version = "1.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fda4ed1c6c173e3fc7a83629421152e01d7b1f9b7f65fb301e490e8cfc656422"
dependencies = [
 "pin-project-internal",
]

[[package]]
name = "pin-project-internal"
version = "1.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4359fd9c9171ec6e8c62926d6faaf553a8dc3f64e1507e76da7911b4f6a04405"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.48",
]

[[package]]
name = "pin-project-lite"
version = "0.1.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "257b64915a082f7811703966789728173279bdebb956b143dbcd23f6f970a777"

[[package]]
name = "pin-project-lite"
version = "0.2.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8afb450f006bf6385ca15ef45d71d2288452bc3683ce2e2cacc0d18e4be60b58"

[[package]]
name = "pin-utils"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b870d8c151b6f2fb93e84a13146138f05d02ed11c7e7c54f8826aaaf7c9f184"

[[package]]
name = "pkcs8"
version = "0.10.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f950b2377845cebe5cf8b5165cb3cc1a5e0fa5cfa3e1f7f55707d8fd82e0a7b7"
dependencies = [
 "der",
 "spki",
]

[[package]]
name = "pkg-config"
version = "0.3.29"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2900ede94e305130c13ddd391e0ab7cbaeb783945ae07a279c268cb05109c6cb"

[[package]]
name = "platforms"
version = "3.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "626dec3cac7cc0e1577a2ec3fc496277ec2baa084bebad95bb6fdbfae235f84c"

[[package]]
name = "polling"
version = "3.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "545c980a3880efd47b2e262f6a4bb6daad6555cf3367aa9c4e52895f69537a41"
dependencies = [
 "cfg-if",
 "concurrent-queue",
 "pin-project-lite 0.2.13",
 "rustix 0.38.30",
 "tracing",
 "windows-sys 0.52.0",
]

[[package]]
name = "poly1305"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8159bd90725d2df49889a078b54f4f79e87f1f8a8444194cdca81d38f5393abf"
dependencies = [
 "cpufeatures",
 "opaque-debug 0.3.0",
 "universal-hash",
]

[[package]]
name = "polyval"
version = "0.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d52cff9d1d4dee5fe6d03729099f4a310a41179e0a10dbf542039873f2e826fb"
dependencies = [
 "cfg-if",
 "cpufeatures",
 "opaque-debug 0.3.0",
 "universal-hash",
]

[[package]]
name = "portable-atomic"
version = "1.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7170ef9988bc169ba16dd36a7fa041e5c4cbeb6a35b76d4c03daded371eae7c0"

[[package]]
name = "powerfmt"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "439ee305def115ba05938db6eb1644ff94165c5ab5e9420d1c1bcedbba909391"

[[package]]
name = "ppv-lite86"
version = "0.2.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5b40af805b3121feab8a3c29f04d8ad262fa8e0561883e7653e024ae4479e6de"

[[package]]
name = "predicates"
version = "2.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "59230a63c37f3e18569bdb90e4a89cbf5bf8b06fea0b84e65ea10cc4df47addd"
dependencies = [
 "difflib",
 "float-cmp",
 "itertools",
 "normalize-line-endings",
 "predicates-core",
 "regex",
]

[[package]]
name = "predicates-core"
version = "1.0.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b794032607612e7abeb4db69adb4e33590fa6cf1149e95fd7cb00e634b92f174"

[[package]]
name = "predicates-tree"
version = "1.0.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "368ba315fb8c5052ab692e68a0eefec6ec57b23a36959c14496f0b0df2c0cecf"
dependencies = [
 "predicates-core",
 "termtree",
]

[[package]]
name = "prettyplease"
version = "0.1.25"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6c8646e95016a7a6c4adea95bafa8a16baab64b583356217f2c85db4a39d9a86"
dependencies = [
 "proc-macro2",
 "syn 1.0.109",
]

[[package]]
name = "prettyplease"
version = "0.2.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a41cf62165e97c7f814d2221421dbb9afcbcdb0a88068e5ea206e19951c2cbb5"
dependencies = [
 "proc-macro2",
 "syn 2.0.48",
]

[[package]]
name = "primitive-types"
version = "0.12.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b34d9fd68ae0b74a41b21c03c2f62847aa0ffea044eee893b4c140b37e244e2"
dependencies = [
 "fixed-hash",
 "impl-codec",
 "impl-serde",
 "scale-info",
 "uint",
]

[[package]]
name = "proc-macro-crate"
version = "1.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e17d47ce914bf4de440332250b0edd23ce48c005f59fab39d3335866b114f11a"
dependencies = [
 "thiserror",
 "toml 0.5.11",
]

[[package]]
name = "proc-macro-crate"
version = "2.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "97dc5fea232fc28d2f597b37c4876b348a40e33f3b02cc975c8d006d78d94b1a"
dependencies = [
 "toml_datetime",
 "toml_edit 0.20.2",
]

[[package]]
name = "proc-macro-error"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "da25490ff9892aab3fcf7c36f08cfb902dd3e71ca0f9f9517bea02a73a5ce38c"
dependencies = [
 "proc-macro-error-attr",
 "proc-macro2",
 "quote",
 "syn 1.0.109",
 "version_check",
]

[[package]]
name = "proc-macro-error-attr"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a1be40180e52ecc98ad80b184934baf3d0d29f979574e439af5a55274b35f869"
dependencies = [
 "proc-macro2",
 "quote",
 "version_check",
]

[[package]]
name = "proc-macro-hack"
version = "0.5.20+deprecated"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dc375e1527247fe1a97d8b7156678dfe7c1af2fc075c9a4db3690ecd2a148068"

[[package]]
name = "proc-macro-warning"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3d1eaa7fa0aa1929ffdf7eeb6eac234dde6268914a14ad44d23521ab6a9b258e"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.48",
]

[[package]]
name = "proc-macro2"
version = "1.0.76"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "95fc56cda0b5c3325f5fbbd7ff9fda9e02bb00bb3dac51252d2f1bfa1cb8cc8c"
dependencies = [
 "unicode-ident",
]

[[package]]
name = "prometheus"
version = "0.13.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "449811d15fbdf5ceb5c1144416066429cf82316e2ec8ce0c1f6f8a02e7bbcf8c"
dependencies = [
 "cfg-if",
 "fnv",
 "lazy_static",
 "memchr",
 "parking_lot 0.12.1",
 "thiserror",
]

[[package]]
name = "prometheus-client"
version = "0.19.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5d6fa99d535dd930d1249e6c79cb3c2915f9172a540fe2b02a4c8f9ca954721e"
dependencies = [
 "dtoa",
 "itoa",
 "parking_lot 0.12.1",
 "prometheus-client-derive-encode",
]

[[package]]
name = "prometheus-client-derive-encode"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "440f724eba9f6996b75d63681b0a92b06947f1457076d503a4d2e2c8f56442b8"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.48",
]

[[package]]
name = "prost"
version = "0.11.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b82eaa1d779e9a4bc1c3217db8ffbeabaae1dca241bf70183242128d48681cd"
dependencies = [
 "bytes",
 "prost-derive",
]

[[package]]
name = "prost-build"
version = "0.11.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "119533552c9a7ffacc21e099c24a0ac8bb19c2a2a3f363de84cd9b844feab270"
dependencies = [
 "bytes",
 "heck",
 "itertools",
 "lazy_static",
 "log",
 "multimap",
 "petgraph",
 "prettyplease 0.1.25",
 "prost",
 "prost-types",
 "regex",
 "syn 1.0.109",
 "tempfile",
 "which",
]

[[package]]
name = "prost-derive"
version = "0.11.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e5d2d8d10f3c6ded6da8b05b5fb3b8a5082514344d56c9f871412d29b4e075b4"
dependencies = [
 "anyhow",
 "itertools",
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "prost-types"
version = "0.11.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "213622a1460818959ac1181aaeb2dc9c7f63df720db7d788b3e24eacd1983e13"
dependencies = [
 "prost",
]

[[package]]
name = "psm"
version = "0.1.21"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5787f7cda34e3033a72192c018bc5883100330f362ef279a8cbccfce8bb4e874"
dependencies = [
 "cc",
]

[[package]]
name = "quick-error"
version = "1.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a1d01941d82fa2ab50be1e79e6714289dd7cde78eba4c074bc5a4374f650dfe0"

[[package]]
name = "quick-protobuf"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9d6da84cc204722a989e01ba2f6e1e276e190f22263d0cb6ce8526fcdb0d2e1f"
dependencies = [
 "byteorder",
]

[[package]]
name = "quick-protobuf-codec"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1693116345026436eb2f10b677806169c1a1260c1c60eaaffe3fb5a29ae23d8b"
dependencies = [
 "asynchronous-codec",
 "bytes",
 "quick-protobuf",
 "thiserror",
 "unsigned-varint",
]

[[package]]
name = "quicksink"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "77de3c815e5a160b1539c6592796801df2043ae35e123b46d73380cfa57af858"
dependencies = [
 "futures-core",
 "futures-sink",
 "pin-project-lite 0.1.12",
]

[[package]]
name = "quinn-proto"
version = "0.9.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "94b0b33c13a79f669c85defaf4c275dc86a0c0372807d0ca3d78e0bb87274863"
dependencies = [
 "bytes",
 "rand 0.8.5",
 "ring 0.16.20",
 "rustc-hash",
 "rustls 0.20.9",
 "slab",
 "thiserror",
 "tinyvec",
 "tracing",
 "webpki",
]

[[package]]
name = "quote"
version = "1.0.35"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "291ec9ab5efd934aaf503a6466c5d5251535d108ee747472c3977cc5acc868ef"
dependencies = [
 "proc-macro2",
]

[[package]]
name = "radium"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dc33ff2d4973d518d823d61aa239014831e521c75da58e3df4840d3f47749d09"

[[package]]
name = "rand"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6a6b1679d49b24bbfe0c803429aa1874472f50d9b363131f0e89fc356b544d03"
dependencies = [
 "getrandom 0.1.16",
 "libc",
 "rand_chacha 0.2.2",
 "rand_core 0.5.1",
 "rand_hc",
]

[[package]]
name = "rand"
version = "0.8.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "34af8d1a0e25924bc5b7c43c079c942339d8f0a8b57c39049bef581b46327404"
dependencies = [
 "libc",
 "rand_chacha 0.3.1",
 "rand_core 0.6.4",
]

[[package]]
name = "rand_chacha"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f4c8ed856279c9737206bf725bf36935d8666ead7aa69b52be55af369d193402"
dependencies = [
 "ppv-lite86",
 "rand_core 0.5.1",
]

[[package]]
name = "rand_chacha"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e6c10a63a0fa32252be49d21e7709d4d4baf8d231c2dbce1eaa8141b9b127d88"
dependencies = [
 "ppv-lite86",
 "rand_core 0.6.4",
]

[[package]]
name = "rand_core"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "90bde5296fc891b0cef12a6d03ddccc162ce7b2aff54160af9338f8d40df6d19"
dependencies = [
 "getrandom 0.1.16",
]

[[package]]
name = "rand_core"
version = "0.6.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ec0be4795e2f6a28069bec0b5ff3e2ac9bafc99e6a9a7dc3547996c5c816922c"
dependencies = [
 "getrandom 0.2.12",
]

[[package]]
name = "rand_hc"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ca3129af7b92a17112d59ad498c6f81eaf463253766b90396d39ea7a39d6613c"
dependencies = [
 "rand_core 0.5.1",
]

[[package]]
name = "rand_pcg"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "59cad018caf63deb318e5a4586d99a24424a364f40f1e5778c29aca23f4fc73e"
dependencies = [
 "rand_core 0.6.4",
]

[[package]]
name = "rawpointer"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "60a357793950651c4ed0f3f52338f53b2f809f32d83a07f72909fa13e4c6c1e3"

[[package]]
name = "rayon"
version = "1.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fa7237101a77a10773db45d62004a272517633fbcc3df19d96455ede1122e051"
dependencies = [
 "either",
 "rayon-core",
]

[[package]]
name = "rayon-core"
version = "1.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1465873a3dfdaa8ae7cb14b4383657caab0b3e8a0aa9ae8e04b044854c8dfce2"
dependencies = [
 "crossbeam-deque",
 "crossbeam-utils",
]

[[package]]
name = "rcgen"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ffbe84efe2f38dea12e9bfc1f65377fdf03e53a18cb3b995faedf7934c7e785b"
dependencies = [
 "pem",
 "ring 0.16.20",
 "time",
 "yasna",
]

[[package]]
name = "redox_syscall"
version = "0.2.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fb5a58c1855b4b6819d59012155603f0b22ad30cad752600aadfcb695265519a"
dependencies = [
 "bitflags 1.3.2",
]

[[package]]
name = "redox_syscall"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4722d768eff46b75989dd134e5c353f0d6296e5aaa3132e776cbdb56be7731aa"
dependencies = [
 "bitflags 1.3.2",
]

[[package]]
name = "redox_users"
version = "0.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a18479200779601e498ada4e8c1e1f50e3ee19deb0259c25825a98b5603b2cb4"
dependencies = [
 "getrandom 0.2.12",
 "libredox",
 "thiserror",
]

[[package]]
name = "ref-cast"
version = "1.0.22"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c4846d4c50d1721b1a3bef8af76924eef20d5e723647333798c1b519b3a9473f"
dependencies = [
 "ref-cast-impl",
]

[[package]]
name = "ref-cast-impl"
version = "1.0.22"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5fddb4f8d99b0a2ebafc65a87a69a7b9875e4b1ae1f00db265d300ef7f28bccc"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.48",
]

[[package]]
name = "regalloc2"
version = "0.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "80535183cae11b149d618fbd3c37e38d7cda589d82d7769e196ca9a9042d7621"
dependencies = [
 "fxhash",
 "log",
 "slice-group-by",
 "smallvec",
]

[[package]]
name = "regex"
version = "1.10.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "380b951a9c5e80ddfd6136919eef32310721aa4aacd4889a8d39124b026ab343"
dependencies = [
 "aho-corasick",
 "memchr",
 "regex-automata 0.4.3",
 "regex-syntax 0.8.2",
]

[[package]]
name = "regex-automata"
version = "0.1.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6c230d73fb8d8c1b9c0b3135c5142a8acee3a0558fb8db5cf1cb65f8d7862132"
dependencies = [
 "regex-syntax 0.6.29",
]

[[package]]
name = "regex-automata"
version = "0.4.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5f804c7828047e88b2d32e2d7fe5a105da8ee3264f01902f796c8e067dc2483f"
dependencies = [
 "aho-corasick",
 "memchr",
 "regex-syntax 0.8.2",
]

[[package]]
name = "regex-syntax"
version = "0.6.29"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f162c6dd7b008981e4d40210aca20b4bd0f9b60ca9271061b07f78537722f2e1"

[[package]]
name = "regex-syntax"
version = "0.8.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c08c74e62047bb2de4ff487b251e4a92e24f48745648451635cec7d591162d9f"

[[package]]
name = "resolv-conf"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "52e44394d2086d010551b14b53b1f24e31647570cd1deb0379e2c21b329aba00"
dependencies = [
 "hostname",
 "quick-error",
]

[[package]]
name = "rfc6979"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f8dd2a808d456c4a54e300a23e9f5a67e122c3024119acbfd73e3bf664491cb2"
dependencies = [
 "hmac 0.12.1",
 "subtle",
]

[[package]]
name = "ring"
version = "0.1.0"
source = "git+https://github.com/w3f/ring-proof?rev=0e948f3#0e948f3c28cbacecdd3020403c4841c0eb339213"
dependencies = [
 "ark-ec",
 "ark-ff",
 "ark-poly",
 "ark-serialize",
 "ark-std",
 "common",
 "fflonk",
 "merlin 3.0.0",
]

[[package]]
name = "ring"
version = "0.16.20"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3053cf52e236a3ed746dfc745aa9cacf1b791d846bdaf412f60a8d7d6e17c8fc"
dependencies = [
 "cc",
 "libc",
 "once_cell",
 "spin 0.5.2",
 "untrusted 0.7.1",
 "web-sys",
 "winapi",
]

[[package]]
name = "ring"
version = "0.17.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "688c63d65483050968b2a8937f7995f443e27041a0f7700aa59b0822aedebb74"
dependencies = [
 "cc",
 "getrandom 0.2.12",
 "libc",
 "spin 0.9.8",
 "untrusted 0.9.0",
 "windows-sys 0.48.0",
]

[[package]]
name = "rocksdb"
version = "0.21.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bb6f170a4041d50a0ce04b0d2e14916d6ca863ea2e422689a5b694395d299ffe"
dependencies = [
 "libc",
 "librocksdb-sys",
]

[[package]]
name = "rpassword"
version = "7.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "80472be3c897911d0137b2d2b9055faf6eeac5b14e324073d83bc17b191d7e3f"
dependencies = [
 "libc",
 "rtoolbox",
 "windows-sys 0.48.0",
]

[[package]]
name = "rtnetlink"
version = "0.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "322c53fd76a18698f1c27381d58091de3a043d356aa5bd0d510608b565f469a0"
dependencies = [
 "futures",
 "log",
 "netlink-packet-route",
 "netlink-proto",
 "nix",
 "thiserror",
 "tokio",
]

[[package]]
name = "rtoolbox"
version = "0.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c247d24e63230cdb56463ae328478bd5eac8b8faa8c69461a77e8e323afac90e"
dependencies = [
 "libc",
 "windows-sys 0.48.0",
]

[[package]]
name = "rustc-demangle"
version = "0.1.23"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d626bb9dae77e28219937af045c257c28bfd3f69333c512553507f5f9798cb76"

[[package]]
name = "rustc-hash"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "08d43f7aa6b08d49f382cde6a7982047c3426db949b1424bc4b7ec9ae12c6ce2"

[[package]]
name = "rustc-hex"
version = "2.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3e75f6a532d0fd9f7f13144f392b6ad56a32696bfcd9c78f797f16bbb6f072d6"

[[package]]
name = "rustc_version"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bfa0f585226d2e68097d4f95d113b15b83a82e819ab25717ec0590d9584ef366"
dependencies = [
 "semver 1.0.21",
]

[[package]]
name = "rusticata-macros"
version = "4.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "faf0c4a6ece9950b9abdb62b1cfcf2a68b3b67a10ba445b3bb85be2a293d0632"
dependencies = [
 "nom",
]

[[package]]
name = "rustix"
version = "0.36.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "305efbd14fde4139eb501df5f136994bb520b033fa9fbdce287507dc23b8c7ed"
dependencies = [
 "bitflags 1.3.2",
 "errno",
 "io-lifetimes",
 "libc",
 "linux-raw-sys 0.1.4",
 "windows-sys 0.45.0",
]

[[package]]
name = "rustix"
version = "0.38.30"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "322394588aaf33c24007e8bb3238ee3e4c5c09c084ab32bc73890b99ff326bca"
dependencies = [
 "bitflags 2.4.2",
 "errno",
 "libc",
 "linux-raw-sys 0.4.13",
 "windows-sys 0.52.0",
]

[[package]]
name = "rustls"
version = "0.20.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1b80e3dec595989ea8510028f30c408a4630db12c9cbb8de34203b89d6577e99"
dependencies = [
 "log",
 "ring 0.16.20",
 "sct",
 "webpki",
]

[[package]]
name = "rustls"
version = "0.21.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f9d5a6813c0759e4609cd494e8e725babae6a2ca7b62a5536a13daaec6fcb7ba"
dependencies = [
 "log",
 "ring 0.17.7",
 "rustls-webpki",
 "sct",
]

[[package]]
name = "rustls-native-certs"
version = "0.6.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a9aace74cb666635c918e9c12bc0d348266037aa8eb599b5cba565709a8dff00"
dependencies = [
 "openssl-probe",
 "rustls-pemfile",
 "schannel",
 "security-framework",
]

[[package]]
name = "rustls-pemfile"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1c74cae0a4cf6ccbbf5f359f08efdf8ee7e1dc532573bf0db71968cb56b1448c"
dependencies = [
 "base64 0.21.7",
]

[[package]]
name = "rustls-webpki"
version = "0.101.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b6275d1ee7a1cd780b64aca7726599a1dbc893b1e64144529e55c3c2f745765"
dependencies = [
 "ring 0.17.7",
 "untrusted 0.9.0",
]

[[package]]
name = "rustversion"
version = "1.0.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7ffc183a10b4478d04cbbbfc96d0873219d962dd5accaff2ffbd4ceb7df837f4"

[[package]]
name = "rw-stream-sink"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "26338f5e09bb721b85b135ea05af7767c90b52f6de4f087d4f4a3a9d64e7dc04"
dependencies = [
 "futures",
 "pin-project",
 "static_assertions",
]

[[package]]
name = "ryu"
version = "1.0.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f98d2aa92eebf49b69786be48e4477826b256916e84a57ff2a4f21923b48eb4c"

[[package]]
name = "safe_arch"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f398075ce1e6a179b46f51bd88d0598b92b00d3551f1a2d4ac49e771b56ac354"
dependencies = [
 "bytemuck",
]

[[package]]
name = "same-file"
version = "1.0.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "93fc1dc3aaa9bfed95e02e6eadabb4baf7e3078b0bd1b4d7b6b0b68378900502"
dependencies = [
 "winapi-util",
]

[[package]]
name = "sc-allocator"
version = "4.1.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "log",
 "sp-core",
 "sp-wasm-interface",
 "thiserror",
]

[[package]]
name = "sc-basic-authorship"
version = "0.10.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "futures",
 "futures-timer",
 "log",
 "parity-scale-codec 3.6.9",
 "sc-block-builder",
 "sc-client-api",
 "sc-proposer-metrics",
 "sc-telemetry",
 "sc-transaction-pool-api",
 "sp-api",
 "sp-blockchain",
 "sp-consensus",
 "sp-core",
 "sp-inherents",
 "sp-runtime",
 "substrate-prometheus-endpoint",
]

[[package]]
name = "sc-block-builder"
version = "0.10.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "parity-scale-codec 3.6.9",
 "sc-client-api",
 "sp-api",
 "sp-block-builder",
 "sp-blockchain",
 "sp-core",
 "sp-inherents",
 "sp-runtime",
]

[[package]]
name = "sc-chain-spec"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "memmap2",
 "sc-chain-spec-derive",
 "sc-client-api",
 "sc-executor",
 "sc-network",
 "sc-telemetry",
 "serde",
 "serde_json",
 "sp-blockchain",
 "sp-core",
 "sp-runtime",
 "sp-state-machine",
]

[[package]]
name = "sc-chain-spec-derive"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "proc-macro-crate 1.1.3",
 "proc-macro2",
 "quote",
 "syn 2.0.48",
]

[[package]]
name = "sc-cli"
version = "0.10.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "array-bytes",
 "chrono",
 "clap",
 "fdlimit",
 "futures",
 "libp2p-identity",
 "log",
 "names",
 "parity-scale-codec 3.6.9",
 "rand 0.8.5",
 "regex",
 "rpassword",
 "sc-client-api",
 "sc-client-db",
 "sc-keystore",
 "sc-network",
 "sc-service",
 "sc-telemetry",
 "sc-tracing",
 "sc-utils",
 "serde",
 "serde_json",
 "sp-blockchain",
 "sp-core",
 "sp-keyring",
 "sp-keystore",
 "sp-panic-handler",
 "sp-runtime",
 "sp-version",
 "thiserror",
 "tiny-bip39",
 "tokio",
]

[[package]]
name = "sc-client-api"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "fnv",
 "futures",
 "log",
 "parity-scale-codec 3.6.9",
 "parking_lot 0.12.1",
 "sc-executor",
 "sc-transaction-pool-api",
 "sc-utils",
 "sp-api",
 "sp-blockchain",
 "sp-consensus",
 "sp-core",
 "sp-database",
 "sp-externalities",
 "sp-runtime",
 "sp-state-machine",
 "sp-statement-store",
 "sp-storage",
 "substrate-prometheus-endpoint",
]

[[package]]
name = "sc-client-db"
version = "0.10.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "hash-db",
 "kvdb",
 "kvdb-memorydb",
 "kvdb-rocksdb",
 "linked-hash-map",
 "log",
 "parity-db",
 "parity-scale-codec 3.6.9",
 "parking_lot 0.12.1",
 "sc-client-api",
 "sc-state-db",
 "schnellru",
 "sp-arithmetic",
 "sp-blockchain",
 "sp-core",
 "sp-database",
 "sp-runtime",
 "sp-state-machine",
 "sp-trie",
]

[[package]]
name = "sc-consensus"
version = "0.10.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "async-trait",
 "futures",
 "futures-timer",
 "libp2p-identity",
 "log",
 "mockall",
 "parking_lot 0.12.1",
 "sc-client-api",
 "sc-utils",
 "serde",
 "sp-api",
 "sp-blockchain",
 "sp-consensus",
 "sp-core",
 "sp-runtime",
 "sp-state-machine",
 "substrate-prometheus-endpoint",
 "thiserror",
]

[[package]]
name = "sc-consensus-aura"
version = "0.10.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "async-trait",
 "futures",
 "log",
 "parity-scale-codec 3.6.9",
 "sc-block-builder",
 "sc-client-api",
 "sc-consensus",
 "sc-consensus-slots",
 "sc-telemetry",
 "sp-api",
 "sp-application-crypto",
 "sp-block-builder",
 "sp-blockchain",
 "sp-consensus",
 "sp-consensus-aura",
 "sp-consensus-slots",
 "sp-core",
 "sp-inherents",
 "sp-keystore",
 "sp-runtime",
 "substrate-prometheus-endpoint",
 "thiserror",
]

[[package]]
name = "sc-consensus-grandpa"
version = "0.10.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "ahash 0.8.7",
 "array-bytes",
 "async-trait",
 "dyn-clone",
 "finality-grandpa",
 "fork-tree",
 "futures",
 "futures-timer",
 "log",
 "parity-scale-codec 3.6.9",
 "parking_lot 0.12.1",
 "rand 0.8.5",
 "sc-block-builder",
 "sc-chain-spec",
 "sc-client-api",
 "sc-consensus",
 "sc-network",
 "sc-network-common",
 "sc-network-gossip",
 "sc-telemetry",
 "sc-transaction-pool-api",
 "sc-utils",
 "serde_json",
 "sp-api",
 "sp-application-crypto",
 "sp-arithmetic",
 "sp-blockchain",
 "sp-consensus",
 "sp-consensus-grandpa",
 "sp-core",
 "sp-keystore",
 "sp-runtime",
 "substrate-prometheus-endpoint",
 "thiserror",
]

[[package]]
name = "sc-consensus-slots"
version = "0.10.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "async-trait",
 "futures",
 "futures-timer",
 "log",
 "parity-scale-codec 3.6.9",
 "sc-client-api",
 "sc-consensus",
 "sc-telemetry",
 "sp-arithmetic",
 "sp-blockchain",
 "sp-consensus",
 "sp-consensus-slots",
 "sp-core",
 "sp-inherents",
 "sp-runtime",
 "sp-state-machine",
]

[[package]]
name = "sc-executor"
version = "0.10.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "parity-scale-codec 3.6.9",
 "parking_lot 0.12.1",
 "sc-executor-common",
 "sc-executor-wasmtime",
 "schnellru",
 "sp-api",
 "sp-core",
 "sp-externalities",
 "sp-io",
 "sp-panic-handler",
 "sp-runtime-interface",
 "sp-trie",
 "sp-version",
 "sp-wasm-interface",
 "tracing",
]

[[package]]
name = "sc-executor-common"
version = "0.10.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "sc-allocator",
 "sp-maybe-compressed-blob",
 "sp-wasm-interface",
 "thiserror",
 "wasm-instrument",
]

[[package]]
name = "sc-executor-wasmtime"
version = "0.10.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "anyhow",
 "cfg-if",
 "libc",
 "log",
 "rustix 0.36.17",
 "sc-allocator",
 "sc-executor-common",
 "sp-runtime-interface",
 "sp-wasm-interface",
 "wasmtime",
]

[[package]]
name = "sc-informant"
version = "0.10.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "ansi_term",
 "futures",
 "futures-timer",
 "log",
 "sc-client-api",
 "sc-network",
 "sc-network-common",
 "sp-blockchain",
 "sp-runtime",
]

[[package]]
name = "sc-keystore"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "array-bytes",
 "parking_lot 0.12.1",
 "serde_json",
 "sp-application-crypto",
 "sp-core",
 "sp-keystore",
 "thiserror",
]

[[package]]
name = "sc-network"
version = "0.10.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "array-bytes",
 "async-channel",
 "async-trait",
 "asynchronous-codec",
 "bytes",
 "either",
 "fnv",
 "futures",
 "futures-timer",
 "ip_network",
 "libp2p",
 "linked_hash_set",
 "log",
 "mockall",
 "parity-scale-codec 3.6.9",
 "parking_lot 0.12.1",
 "partial_sort",
 "pin-project",
 "rand 0.8.5",
 "sc-client-api",
 "sc-network-common",
 "sc-utils",
 "serde",
 "serde_json",
 "smallvec",
 "sp-arithmetic",
 "sp-blockchain",
 "sp-core",
 "sp-runtime",
 "substrate-prometheus-endpoint",
 "thiserror",
 "unsigned-varint",
 "wasm-timer",
 "zeroize",
]

[[package]]
name = "sc-network-bitswap"
version = "0.10.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "async-channel",
 "cid",
 "futures",
 "libp2p-identity",
 "log",
 "prost",
 "prost-build",
 "sc-client-api",
 "sc-network",
 "sp-blockchain",
 "sp-runtime",
 "thiserror",
 "unsigned-varint",
]

[[package]]
name = "sc-network-common"
version = "0.10.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "async-trait",
 "bitflags 1.3.2",
 "futures",
 "libp2p-identity",
 "parity-scale-codec 3.6.9",
 "prost-build",
 "sc-consensus",
 "sp-consensus",
 "sp-consensus-grandpa",
 "sp-runtime",
]

[[package]]
name = "sc-network-gossip"
version = "0.10.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "ahash 0.8.7",
 "futures",
 "futures-timer",
 "libp2p",
 "log",
 "sc-network",
 "sc-network-common",
 "schnellru",
 "sp-runtime",
 "substrate-prometheus-endpoint",
 "tracing",
]

[[package]]
name = "sc-network-light"
version = "0.10.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "array-bytes",
 "async-channel",
 "futures",
 "libp2p-identity",
 "log",
 "parity-scale-codec 3.6.9",
 "prost",
 "prost-build",
 "sc-client-api",
 "sc-network",
 "sp-blockchain",
 "sp-core",
 "sp-runtime",
 "thiserror",
]

[[package]]
name = "sc-network-sync"
version = "0.10.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "array-bytes",
 "async-channel",
 "async-trait",
 "fork-tree",
 "futures",
 "futures-timer",
 "libp2p",
 "log",
 "mockall",
 "parity-scale-codec 3.6.9",
 "prost",
 "prost-build",
 "sc-client-api",
 "sc-consensus",
 "sc-network",
 "sc-network-common",
 "sc-utils",
 "schnellru",
 "smallvec",
 "sp-arithmetic",
 "sp-blockchain",
 "sp-consensus",
 "sp-consensus-grandpa",
 "sp-core",
 "sp-runtime",
 "substrate-prometheus-endpoint",
 "thiserror",
]

[[package]]
name = "sc-network-transactions"
version = "0.10.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "array-bytes",
 "futures",
 "libp2p",
 "log",
 "parity-scale-codec 3.6.9",
 "sc-network",
 "sc-network-common",
 "sc-utils",
 "sp-consensus",
 "sp-runtime",
 "substrate-prometheus-endpoint",
]

[[package]]
name = "sc-offchain"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "array-bytes",
 "bytes",
 "fnv",
 "futures",
 "futures-timer",
 "hyper",
 "hyper-rustls",
 "libp2p",
 "log",
 "num_cpus",
 "once_cell",
 "parity-scale-codec 3.6.9",
 "parking_lot 0.12.1",
 "rand 0.8.5",
 "sc-client-api",
 "sc-network",
 "sc-network-common",
 "sc-transaction-pool-api",
 "sc-utils",
 "sp-api",
 "sp-core",
 "sp-externalities",
 "sp-keystore",
 "sp-offchain",
 "sp-runtime",
 "threadpool",
 "tracing",
]

[[package]]
name = "sc-proposer-metrics"
version = "0.10.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "log",
 "substrate-prometheus-endpoint",
]

[[package]]
name = "sc-rpc"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "futures",
 "jsonrpsee",
 "log",
 "parity-scale-codec 3.6.9",
 "parking_lot 0.12.1",
 "sc-block-builder",
 "sc-chain-spec",
 "sc-client-api",
 "sc-rpc-api",
 "sc-tracing",
 "sc-transaction-pool-api",
 "sc-utils",
 "serde_json",
 "sp-api",
 "sp-blockchain",
 "sp-core",
 "sp-keystore",
 "sp-offchain",
 "sp-rpc",
 "sp-runtime",
 "sp-session",
 "sp-statement-store",
 "sp-version",
 "tokio",
]

[[package]]
name = "sc-rpc-api"
version = "0.10.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "jsonrpsee",
 "parity-scale-codec 3.6.9",
 "sc-chain-spec",
 "sc-transaction-pool-api",
 "scale-info",
 "serde",
 "serde_json",
 "sp-core",
 "sp-rpc",
 "sp-runtime",
 "sp-version",
 "thiserror",
]

[[package]]
name = "sc-rpc-server"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "http",
 "jsonrpsee",
 "log",
 "serde_json",
 "substrate-prometheus-endpoint",
 "tokio",
 "tower",
 "tower-http",
]

[[package]]
name = "sc-rpc-spec-v2"
version = "0.10.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "array-bytes",
 "futures",
 "futures-util",
 "hex",
 "jsonrpsee",
 "log",
 "parity-scale-codec 3.6.9",
 "parking_lot 0.12.1",
 "sc-chain-spec",
 "sc-client-api",
 "sc-transaction-pool-api",
 "sc-utils",
 "serde",
 "sp-api",
 "sp-blockchain",
 "sp-core",
 "sp-runtime",
 "sp-version",
 "thiserror",
 "tokio",
 "tokio-stream",
]

[[package]]
name = "sc-service"
version = "0.10.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "async-trait",
 "directories",
 "exit-future",
 "futures",
 "futures-timer",
 "jsonrpsee",
 "log",
 "parity-scale-codec 3.6.9",
 "parking_lot 0.12.1",
 "pin-project",
 "rand 0.8.5",
 "sc-block-builder",
 "sc-chain-spec",
 "sc-client-api",
 "sc-client-db",
 "sc-consensus",
 "sc-executor",
 "sc-informant",
 "sc-keystore",
 "sc-network",
 "sc-network-bitswap",
 "sc-network-common",
 "sc-network-light",
 "sc-network-sync",
 "sc-network-transactions",
 "sc-rpc",
 "sc-rpc-server",
 "sc-rpc-spec-v2",
 "sc-sysinfo",
 "sc-telemetry",
 "sc-tracing",
 "sc-transaction-pool",
 "sc-transaction-pool-api",
 "sc-utils",
 "serde",
 "serde_json",
 "sp-api",
 "sp-blockchain",
 "sp-consensus",
 "sp-core",
 "sp-externalities",
 "sp-keystore",
 "sp-runtime",
 "sp-session",
 "sp-state-machine",
 "sp-storage",
 "sp-transaction-pool",
 "sp-transaction-storage-proof",
 "sp-trie",
 "sp-version",
 "static_init",
 "substrate-prometheus-endpoint",
 "tempfile",
 "thiserror",
 "tokio",
 "tracing",
 "tracing-futures",
]

[[package]]
name = "sc-state-db"
version = "0.10.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "log",
 "parity-scale-codec 3.6.9",
 "parking_lot 0.12.1",
 "sp-core",
]

[[package]]
name = "sc-sysinfo"
version = "6.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "futures",
 "libc",
 "log",
 "rand 0.8.5",
 "rand_pcg",
 "regex",
 "sc-telemetry",
 "serde",
 "serde_json",
 "sp-core",
 "sp-io",
 "sp-std 8.0.0",
]

[[package]]
name = "sc-telemetry"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "chrono",
 "futures",
 "libp2p",
 "log",
 "parking_lot 0.12.1",
 "pin-project",
 "rand 0.8.5",
 "sc-utils",
 "serde",
 "serde_json",
 "thiserror",
 "wasm-timer",
]

[[package]]
name = "sc-tracing"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "ansi_term",
 "atty",
 "chrono",
 "lazy_static",
 "libc",
 "log",
 "parking_lot 0.12.1",
 "regex",
 "rustc-hash",
 "sc-client-api",
 "sc-tracing-proc-macro",
 "serde",
 "sp-api",
 "sp-blockchain",
 "sp-core",
 "sp-rpc",
 "sp-runtime",
 "sp-tracing",
 "thiserror",
 "tracing",
 "tracing-log",
 "tracing-subscriber",
]

[[package]]
name = "sc-tracing-proc-macro"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "proc-macro-crate 1.1.3",
 "proc-macro2",
 "quote",
 "syn 2.0.48",
]

[[package]]
name = "sc-transaction-pool"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "async-trait",
 "futures",
 "futures-timer",
 "linked-hash-map",
 "log",
 "parity-scale-codec 3.6.9",
 "parking_lot 0.12.1",
 "sc-client-api",
 "sc-transaction-pool-api",
 "sc-utils",
 "serde",
 "sp-api",
 "sp-blockchain",
 "sp-core",
 "sp-runtime",
 "sp-tracing",
 "sp-transaction-pool",
 "substrate-prometheus-endpoint",
 "thiserror",
]

[[package]]
name = "sc-transaction-pool-api"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "async-trait",
 "futures",
 "log",
 "parity-scale-codec 3.6.9",
 "serde",
 "sp-blockchain",
 "sp-core",
 "sp-runtime",
 "thiserror",
]

[[package]]
name = "sc-utils"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "async-channel",
 "futures",
 "futures-timer",
 "lazy_static",
 "log",
 "parking_lot 0.12.1",
 "prometheus",
 "sp-arithmetic",
]

[[package]]
name = "scale-info"
version = "2.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7f7d66a1128282b7ef025a8ead62a4a9fcf017382ec53b8ffbf4d7bf77bd3c60"
dependencies = [
 "bitvec",
 "cfg-if",
 "derive_more",
 "parity-scale-codec 3.6.9",
 "scale-info-derive",
 "serde",
]

[[package]]
name = "scale-info-derive"
version = "2.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "abf2c68b89cafb3b8d918dd07b42be0da66ff202cf1155c5739a4e0c1ea0dc19"
dependencies = [
 "proc-macro-crate 1.1.3",
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "schannel"
version = "0.1.23"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fbc91545643bcf3a0bbb6569265615222618bdf33ce4ffbbd13c4bbd4c093534"
dependencies = [
 "windows-sys 0.52.0",
]

[[package]]
name = "schnellru"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "772575a524feeb803e5b0fcbc6dd9f367e579488197c94c6e4023aad2305774d"
dependencies = [
 "ahash 0.8.7",
 "cfg-if",
 "hashbrown 0.13.2",
]

[[package]]
name = "schnorrkel"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "021b403afe70d81eea68f6ea12f6b3c9588e5d536a94c3bf80f15e7faa267862"
dependencies = [
 "arrayref",
 "arrayvec 0.5.2",
 "curve25519-dalek 2.1.3",
 "getrandom 0.1.16",
 "merlin 2.0.1",
 "rand 0.7.3",
 "rand_core 0.5.1",
 "sha2 0.8.2",
 "subtle",
 "zeroize",
]

[[package]]
name = "scopeguard"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "94143f37725109f92c262ed2cf5e59bce7498c01bcc1502d7b9afe439a4e9f49"

[[package]]
name = "scratch"
version = "1.0.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a3cf7c11c38cb994f3d40e8a8cde3bbd1f72a435e4c49e85d6553d8312306152"

[[package]]
name = "sct"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "da046153aa2352493d6cb7da4b6e5c0c057d8a1d0a9aa8560baffdd945acd414"
dependencies = [
 "ring 0.17.7",
 "untrusted 0.9.0",
]

[[package]]
name = "sec1"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d3e97a565f76233a6003f9f5c54be1d9c5bdfa3eccfb189469f11ec4901c47dc"
dependencies = [
 "base16ct",
 "der",
 "generic-array 0.14.7",
 "pkcs8",
 "subtle",
 "zeroize",
]

[[package]]
name = "secp256k1"
version = "0.24.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6b1629c9c557ef9b293568b338dddfc8208c98a18c59d722a9d53f859d9c9b62"
dependencies = [
 "secp256k1-sys",
]

[[package]]
name = "secp256k1-sys"
version = "0.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "83080e2c2fc1006e625be82e5d1eb6a43b7fd9578b617fcc55814daf286bba4b"
dependencies = [
 "cc",
]

[[package]]
name = "secrecy"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9bd1c54ea06cfd2f6b63219704de0b9b4f72dcc2b8fdef820be6cd799780e91e"
dependencies = [
 "zeroize",
]

[[package]]
name = "security-framework"
version = "2.9.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "05b64fb303737d99b81884b2c63433e9ae28abebe5eb5045dcdd175dc2ecf4de"
dependencies = [
 "bitflags 1.3.2",
 "core-foundation",
 "core-foundation-sys",
 "libc",
 "security-framework-sys",
]

[[package]]
name = "security-framework-sys"
version = "2.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e932934257d3b408ed8f30db49d85ea163bfe74961f017f405b025af298f0c7a"
dependencies = [
 "core-foundation-sys",
 "libc",
]

[[package]]
name = "semver"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7a3186ec9e65071a2095434b1f5bb24838d4e8e130f584c790f6033c79943537"
dependencies = [
 "semver-parser",
]

[[package]]
name = "semver"
version = "1.0.21"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b97ed7a9823b74f99c7742f5336af7be5ecd3eeafcb1507d1fa93347b1d589b0"
dependencies = [
 "serde",
]

[[package]]
name = "semver-parser"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "388a1df253eca08550bef6c72392cfe7c30914bf41df5269b68cbd6ff8f570a3"

[[package]]
name = "serde"
version = "1.0.195"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "63261df402c67811e9ac6def069e4786148c4563f4b50fd4bf30aa370d626b02"
dependencies = [
 "serde_derive",
]

[[package]]
name = "serde_derive"
version = "1.0.195"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "46fe8f8603d81ba86327b23a2e9cdf49e1255fb94a4c5f297f6ee0547178ea2c"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.48",
]

[[package]]
name = "serde_json"
version = "1.0.111"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "176e46fa42316f18edd598015a5166857fc835ec732f5215eac6b7bdbf0a84f4"
dependencies = [
 "itoa",
 "ryu",
 "serde",
]

[[package]]
name = "serde_spanned"
version = "0.6.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eb3622f419d1296904700073ea6cc23ad690adbd66f13ea683df73298736f0c1"
dependencies = [
 "serde",
]

[[package]]
name = "sha-1"
version = "0.9.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "99cd6713db3cf16b6c84e06321e049a9b9f699826e16096d23bbcc44d15d51a6"
dependencies = [
 "block-buffer 0.9.0",
 "cfg-if",
 "cpufeatures",
 "digest 0.9.0",
 "opaque-debug 0.3.0",
]

[[package]]
name = "sha2"
version = "0.8.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a256f46ea78a0c0d9ff00077504903ac881a1dafdc20da66545699e7776b3e69"
dependencies = [
 "block-buffer 0.7.3",
 "digest 0.8.1",
 "fake-simd",
 "opaque-debug 0.2.3",
]

[[package]]
name = "sha2"
version = "0.9.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4d58a1e1bf39749807d89cf2d98ac2dfa0ff1cb3faa38fbb64dd88ac8013d800"
dependencies = [
 "block-buffer 0.9.0",
 "cfg-if",
 "cpufeatures",
 "digest 0.9.0",
 "opaque-debug 0.3.0",
]

[[package]]
name = "sha2"
version = "0.10.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "793db75ad2bcafc3ffa7c68b215fee268f537982cd901d132f89c6343f3a3dc8"
dependencies = [
 "cfg-if",
 "cpufeatures",
 "digest 0.10.7",
]

[[package]]
name = "sha3"
version = "0.10.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "75872d278a8f37ef87fa0ddbda7802605cb18344497949862c0d4dcb291eba60"
dependencies = [
 "digest 0.10.7",
 "keccak",
]

[[package]]
name = "sharded-slab"
version = "0.1.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f40ca3c46823713e0d4209592e8d6e826aa57e928f09752619fc696c499637f6"
dependencies = [
 "lazy_static",
]

[[package]]
name = "shlex"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a7cee0529a6d40f580e7a5e6c495c8fbfe21b7b52795ed4bb5e62cdf92bc6380"

[[package]]
name = "signal-hook-registry"
version = "1.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d8229b473baa5980ac72ef434c4415e70c4b5e71b423043adb4ba059f89c99a1"
dependencies = [
 "libc",
]

[[package]]
name = "signature"
version = "2.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "77549399552de45a898a580c1b41d445bf730df867cc44e6c0233bbc4b8329de"
dependencies = [
 "digest 0.10.7",
 "rand_core 0.6.4",
]

[[package]]
name = "simba"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "061507c94fc6ab4ba1c9a0305018408e312e17c041eb63bef8aa726fa33aceae"
dependencies = [
 "approx",
 "num-complex",
 "num-traits",
 "paste 1.0.14",
 "wide",
]

[[package]]
name = "siphasher"
version = "0.3.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "38b58827f4464d87d377d175e90bf58eb00fd8716ff0a62f80356b5e61555d0d"

[[package]]
name = "slab"
version = "0.4.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8f92a496fb766b417c996b9c5e57daf2f7ad3b0bebe1ccfca4856390e3d3bb67"
dependencies = [
 "autocfg",
]

[[package]]
name = "slice-group-by"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "826167069c09b99d56f31e9ae5c99049e932a98c9dc2dac47645b08dbbf76ba7"

[[package]]
name = "smallvec"
version = "1.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3b187f0231d56fe41bfb12034819dd2bf336422a5866de41bc3fec4b2e3883e8"

[[package]]
name = "snap"
version = "1.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1b6b67fb9a61334225b5b790716f609cd58395f895b3fe8b328786812a40bc3b"

[[package]]
name = "snow"
version = "0.9.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "58021967fd0a5eeeb23b08df6cc244a4d4a5b4aec1d27c9e02fad1a58b4cd74e"
dependencies = [
 "aes-gcm",
 "blake2",
 "chacha20poly1305",
 "curve25519-dalek 4.1.1",
 "rand_core 0.6.4",
 "ring 0.17.7",
 "rustc_version",
 "sha2 0.10.8",
 "subtle",
]

[[package]]
name = "socket2"
version = "0.4.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9f7916fc008ca5542385b89a3d3ce689953c143e9304a9bf8beec1de48994c0d"
dependencies = [
 "libc",
 "winapi",
]

[[package]]
name = "socket2"
version = "0.5.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7b5fac59a5cb5dd637972e5fca70daf0523c9067fcdc4842f053dae04a18f8e9"
dependencies = [
 "libc",
 "windows-sys 0.48.0",
]

[[package]]
name = "sodalite"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "41784a359d15c58bba298cccb7f30a847a1a42d0620c9bdaa0aa42fdb3c280e0"
dependencies = [
 "index-fixed",
]

[[package]]
name = "soketto"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "41d1c5305e39e09653383c2c7244f2f78b3bcae37cf50c64cb4789c9f5096ec2"
dependencies = [
 "base64 0.13.1",
 "bytes",
 "flate2",
 "futures",
 "http",
 "httparse",
 "log",
 "rand 0.8.5",
 "sha-1",
]

[[package]]
name = "sp-api"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "hash-db",
 "log",
 "parity-scale-codec 3.6.9",
 "scale-info",
 "sp-api-proc-macro",
 "sp-core",
 "sp-externalities",
 "sp-metadata-ir",
 "sp-runtime",
 "sp-state-machine",
 "sp-std 8.0.0",
 "sp-trie",
 "sp-version",
 "thiserror",
]

[[package]]
name = "sp-api-proc-macro"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "Inflector",
 "blake2",
 "expander",
 "proc-macro-crate 1.1.3",
 "proc-macro2",
 "quote",
 "syn 2.0.48",
]

[[package]]
name = "sp-application-crypto"
version = "23.0.0"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "parity-scale-codec 3.6.9",
 "scale-info",
 "serde",
 "sp-core",
 "sp-io",
 "sp-std 8.0.0",
]

[[package]]
name = "sp-arithmetic"
version = "16.0.0"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "integer-sqrt",
 "num-traits",
 "parity-scale-codec 3.6.9",
 "scale-info",
 "serde",
 "sp-std 8.0.0",
 "static_assertions",
]

[[package]]
name = "sp-block-builder"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "sp-api",
 "sp-inherents",
 "sp-runtime",
 "sp-std 8.0.0",
]

[[package]]
name = "sp-blockchain"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "futures",
 "log",
 "parity-scale-codec 3.6.9",
 "parking_lot 0.12.1",
 "schnellru",
 "sp-api",
 "sp-consensus",
 "sp-database",
 "sp-runtime",
 "sp-state-machine",
 "thiserror",
]

[[package]]
name = "sp-consensus"
version = "0.10.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "async-trait",
 "futures",
 "log",
 "sp-core",
 "sp-inherents",
 "sp-runtime",
 "sp-state-machine",
 "thiserror",
]

[[package]]
name = "sp-consensus-aura"
version = "0.10.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "async-trait",
 "parity-scale-codec 3.6.9",
 "scale-info",
 "sp-api",
 "sp-application-crypto",
 "sp-consensus-slots",
 "sp-inherents",
 "sp-runtime",
 "sp-std 8.0.0",
 "sp-timestamp",
]

[[package]]
name = "sp-consensus-babe"
version = "0.10.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "async-trait",
 "parity-scale-codec 3.6.9",
 "scale-info",
 "serde",
 "sp-api",
 "sp-application-crypto",
 "sp-consensus-slots",
 "sp-core",
 "sp-inherents",
 "sp-runtime",
 "sp-std 8.0.0",
 "sp-timestamp",
]

[[package]]
name = "sp-consensus-grandpa"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "finality-grandpa",
 "log",
 "parity-scale-codec 3.6.9",
 "scale-info",
 "serde",
 "sp-api",
 "sp-application-crypto",
 "sp-core",
 "sp-keystore",
 "sp-runtime",
 "sp-std 8.0.0",
]

[[package]]
name = "sp-consensus-slots"
version = "0.10.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "parity-scale-codec 3.6.9",
 "scale-info",
 "serde",
 "sp-std 8.0.0",
 "sp-timestamp",
]

[[package]]
name = "sp-core"
version = "21.0.0"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "array-bytes",
 "arrayvec 0.7.4",
 "bandersnatch_vrfs",
 "bitflags 1.3.2",
 "blake2",
 "bounded-collections",
 "bs58 0.5.0",
 "dyn-clonable",
 "ed25519-zebra",
 "futures",
 "hash-db",
 "hash256-std-hasher",
 "impl-serde",
 "lazy_static",
 "libsecp256k1",
 "log",
 "merlin 2.0.1",
 "parity-scale-codec 3.6.9",
 "parking_lot 0.12.1",
 "paste 1.0.14",
 "primitive-types",
 "rand 0.8.5",
 "regex",
 "scale-info",
 "schnorrkel",
 "secp256k1",
 "secrecy",
 "serde",
 "sp-core-hashing",
 "sp-debug-derive",
 "sp-externalities",
 "sp-runtime-interface",
 "sp-std 8.0.0",
 "sp-storage",
 "ss58-registry",
 "substrate-bip39",
 "thiserror",
 "tiny-bip39",
 "tracing",
 "zeroize",
]

[[package]]
name = "sp-core-hashing"
version = "9.0.0"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "blake2b_simd",
 "byteorder",
 "digest 0.10.7",
 "sha2 0.10.8",
 "sha3",
 "twox-hash",
]

[[package]]
name = "sp-core-hashing-proc-macro"
version = "9.0.0"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "quote",
 "sp-core-hashing",
 "syn 2.0.48",
]

[[package]]
name = "sp-database"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "kvdb",
 "parking_lot 0.12.1",
]

[[package]]
name = "sp-debug-derive"
version = "8.0.0"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.48",
]

[[package]]
name = "sp-externalities"
version = "0.19.0"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "environmental",
 "parity-scale-codec 3.6.9",
 "sp-std 8.0.0",
 "sp-storage",
]

[[package]]
name = "sp-genesis-builder"
version = "0.1.0"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "serde_json",
 "sp-api",
 "sp-runtime",
 "sp-std 8.0.0",
]

[[package]]
name = "sp-inherents"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "async-trait",
 "impl-trait-for-tuples",
 "parity-scale-codec 3.6.9",
 "scale-info",
 "sp-runtime",
 "sp-std 8.0.0",
 "thiserror",
]

[[package]]
name = "sp-io"
version = "23.0.0"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "bytes",
 "ed25519-dalek",
 "libsecp256k1",
 "log",
 "parity-scale-codec 3.6.9",
 "rustversion",
 "secp256k1",
 "sp-core",
 "sp-externalities",
 "sp-keystore",
 "sp-runtime-interface",
 "sp-state-machine",
 "sp-std 8.0.0",
 "sp-tracing",
 "sp-trie",
 "tracing",
 "tracing-core",
]

[[package]]
name = "sp-keyring"
version = "24.0.0"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "lazy_static",
 "sp-core",
 "sp-runtime",
 "strum 0.24.1",
]

[[package]]
name = "sp-keystore"
version = "0.27.0"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "parity-scale-codec 3.6.9",
 "parking_lot 0.12.1",
 "sp-core",
 "sp-externalities",
 "thiserror",
]

[[package]]
name = "sp-maybe-compressed-blob"
version = "4.1.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "thiserror",
 "zstd 0.12.4",
]

[[package]]
name = "sp-metadata-ir"
version = "0.1.0"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "frame-metadata",
 "parity-scale-codec 3.6.9",
 "scale-info",
 "sp-std 8.0.0",
]

[[package]]
name = "sp-npos-elections"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "parity-scale-codec 3.6.9",
 "scale-info",
 "serde",
 "sp-arithmetic",
 "sp-core",
 "sp-runtime",
 "sp-std 8.0.0",
]

[[package]]
name = "sp-offchain"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "sp-api",
 "sp-core",
 "sp-runtime",
]

[[package]]
name = "sp-panic-handler"
version = "8.0.0"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "backtrace",
 "lazy_static",
 "regex",
]

[[package]]
name = "sp-rpc"
version = "6.0.0"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "rustc-hash",
 "serde",
 "sp-core",
]

[[package]]
name = "sp-runtime"
version = "24.0.0"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "either",
 "hash256-std-hasher",
 "impl-trait-for-tuples",
 "log",
 "parity-scale-codec 3.6.9",
 "paste 1.0.14",
 "rand 0.8.5",
 "scale-info",
 "serde",
 "sp-application-crypto",
 "sp-arithmetic",
 "sp-core",
 "sp-io",
 "sp-std 8.0.0",
 "sp-weights",
]

[[package]]
name = "sp-runtime-interface"
version = "17.0.0"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "bytes",
 "impl-trait-for-tuples",
 "parity-scale-codec 3.6.9",
 "primitive-types",
 "sp-externalities",
 "sp-runtime-interface-proc-macro",
 "sp-std 8.0.0",
 "sp-storage",
 "sp-tracing",
 "sp-wasm-interface",
 "static_assertions",
]

[[package]]
name = "sp-runtime-interface-proc-macro"
version = "11.0.0"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "Inflector",
 "proc-macro-crate 1.1.3",
 "proc-macro2",
 "quote",
 "syn 2.0.48",
]

[[package]]
name = "sp-session"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "parity-scale-codec 3.6.9",
 "scale-info",
 "sp-api",
 "sp-core",
 "sp-keystore",
 "sp-runtime",
 "sp-staking",
 "sp-std 8.0.0",
]

[[package]]
name = "sp-staking"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "impl-trait-for-tuples",
 "parity-scale-codec 3.6.9",
 "scale-info",
 "serde",
 "sp-core",
 "sp-runtime",
 "sp-std 8.0.0",
]

[[package]]
name = "sp-state-machine"
version = "0.28.0"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "hash-db",
 "log",
 "parity-scale-codec 3.6.9",
 "parking_lot 0.12.1",
 "rand 0.8.5",
 "smallvec",
 "sp-core",
 "sp-externalities",
 "sp-panic-handler",
 "sp-std 8.0.0",
 "sp-trie",
 "thiserror",
 "tracing",
 "trie-db",
]

[[package]]
name = "sp-statement-store"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "aes-gcm",
 "curve25519-dalek 4.1.1",
 "ed25519-dalek",
 "hkdf",
 "parity-scale-codec 3.6.9",
 "rand 0.8.5",
 "scale-info",
 "sha2 0.10.8",
 "sp-api",
 "sp-application-crypto",
 "sp-core",
 "sp-externalities",
 "sp-runtime",
 "sp-runtime-interface",
 "sp-std 8.0.0",
 "thiserror",
 "x25519-dalek 2.0.1",
]

[[package]]
name = "sp-std"
version = "5.0.0"
source = "git+https://github.com/paritytech/substrate?branch=polkadot-v0.9.37#6fa7fe1326ecaab9921c2c3888530ad679cfbb87"

[[package]]
name = "sp-std"
version = "8.0.0"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"

[[package]]
name = "sp-storage"
version = "13.0.0"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "impl-serde",
 "parity-scale-codec 3.6.9",
 "ref-cast",
 "serde",
 "sp-debug-derive",
 "sp-std 8.0.0",
]

[[package]]
name = "sp-timestamp"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "async-trait",
 "parity-scale-codec 3.6.9",
 "sp-inherents",
 "sp-runtime",
 "sp-std 8.0.0",
 "thiserror",
]

[[package]]
name = "sp-tracing"
version = "10.0.0"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "parity-scale-codec 3.6.9",
 "sp-std 8.0.0",
 "tracing",
 "tracing-core",
 "tracing-subscriber",
]

[[package]]
name = "sp-transaction-pool"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "sp-api",
 "sp-runtime",
]

[[package]]
name = "sp-transaction-storage-proof"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "async-trait",
 "parity-scale-codec 3.6.9",
 "scale-info",
 "sp-core",
 "sp-inherents",
 "sp-runtime",
 "sp-std 8.0.0",
 "sp-trie",
]

[[package]]
name = "sp-trie"
version = "22.0.0"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "ahash 0.8.7",
 "hash-db",
 "hashbrown 0.13.2",
 "lazy_static",
 "memory-db",
 "nohash-hasher",
 "parity-scale-codec 3.6.9",
 "parking_lot 0.12.1",
 "scale-info",
 "schnellru",
 "sp-core",
 "sp-std 8.0.0",
 "thiserror",
 "tracing",
 "trie-db",
 "trie-root",
]

[[package]]
name = "sp-version"
version = "22.0.0"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "impl-serde",
 "parity-scale-codec 3.6.9",
 "parity-wasm",
 "scale-info",
 "serde",
 "sp-core-hashing-proc-macro",
 "sp-runtime",
 "sp-std 8.0.0",
 "sp-version-proc-macro",
 "thiserror",
]

[[package]]
name = "sp-version-proc-macro"
version = "8.0.0"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "parity-scale-codec 3.6.9",
 "proc-macro2",
 "quote",
 "syn 2.0.48",
]

[[package]]
name = "sp-wasm-interface"
version = "14.0.0"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "anyhow",
 "impl-trait-for-tuples",
 "log",
 "parity-scale-codec 3.6.9",
 "sp-std 8.0.0",
 "wasmtime",
]

[[package]]
name = "sp-weights"
version = "20.0.0"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "parity-scale-codec 3.6.9",
 "scale-info",
 "serde",
 "smallvec",
 "sp-arithmetic",
 "sp-core",
 "sp-debug-derive",
 "sp-std 8.0.0",
]

[[package]]
name = "spin"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6e63cff320ae2c57904679ba7cb63280a3dc4613885beafb148ee7bf9aa9042d"

[[package]]
name = "spin"
version = "0.9.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6980e8d7511241f8acf4aebddbb1ff938df5eebe98691418c4468d0b72a96a67"

[[package]]
name = "spinners"
version = "4.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a0ef947f358b9c238923f764c72a4a9d42f2d637c46e059dbd319d6e7cfb4f82"
dependencies = [
 "lazy_static",
 "maplit",
 "strum 0.24.1",
]

[[package]]
name = "spki"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d91ed6c858b01f942cd56b37a94b3e0a1798290327d1236e4d9cf4eaca44d29d"
dependencies = [
 "base64ct",
 "der",
]

[[package]]
name = "ss58-registry"
version = "1.45.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3c0c74081753a8ce1c8eb10b9f262ab6f7017e5ad3317c17a54c7ab65fcb3c6e"
dependencies = [
 "Inflector",
 "num-format",
 "proc-macro2",
 "quote",
 "serde",
 "serde_json",
 "unicode-xid",
]

[[package]]
name = "stable_deref_trait"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a8f112729512f8e442d81f95a8a7ddf2b7c6b8a1a6f509a95864142b30cab2d3"

[[package]]
name = "static_assertions"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a2eb9349b6444b326872e140eb1cf5e7c522154d69e7a0ffb0fb81c06b37543f"

[[package]]
name = "static_init"
version = "1.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8a2a1c578e98c1c16fc3b8ec1328f7659a500737d7a0c6d625e73e830ff9c1f6"
dependencies = [
 "bitflags 1.3.2",
 "cfg_aliases",
 "libc",
 "parking_lot 0.11.2",
 "parking_lot_core 0.8.6",
 "static_init_macro",
 "winapi",
]

[[package]]
name = "static_init_macro"
version = "1.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "70a2595fc3aa78f2d0e45dd425b22282dd863273761cc77780914b2cf3003acf"
dependencies = [
 "cfg_aliases",
 "memchr",
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "strsim"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "73473c0e59e6d5812c5dfe2a064a6444949f089e20eec9a2e5506596494e4623"

[[package]]
name = "strum"
version = "0.24.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "063e6045c0e62079840579a7e47a355ae92f60eb74daaf156fb1e84ba164e63f"
dependencies = [
 "strum_macros 0.24.3",
]

[[package]]
name = "strum"
version = "0.25.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "290d54ea6f91c969195bdbcd7442c8c2a2ba87da8bf60a7ee86a235d4bc1e125"

[[package]]
name = "strum_macros"
version = "0.24.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e385be0d24f186b4ce2f9982191e7101bb737312ad61c1f2f984f34bcf85d59"
dependencies = [
 "heck",
 "proc-macro2",
 "quote",
 "rustversion",
 "syn 1.0.109",
]

[[package]]
name = "strum_macros"
version = "0.25.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "23dc1fa9ac9c169a78ba62f0b841814b7abae11bdd047b9c58f893439e309ea0"
dependencies = [
 "heck",
 "proc-macro2",
 "quote",
 "rustversion",
 "syn 2.0.48",
]

[[package]]
name = "substrate-bip39"
version = "0.4.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e620c7098893ba667438b47169c00aacdd9e7c10e042250ce2b60b087ec97328"
dependencies = [
 "hmac 0.11.0",
 "pbkdf2 0.8.0",
 "schnorrkel",
 "sha2 0.9.9",
 "zeroize",
]

[[package]]
name = "substrate-build-script-utils"
version = "3.0.0"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"

[[package]]
name = "substrate-fixed"
version = "0.5.6"
source = "git+https://github.com/encointer/substrate-fixed.git?rev=b33d186888c60f38adafcfc0ec3a21aab263aef1#b33d186888c60f38adafcfc0ec3a21aab263aef1"
dependencies = [
 "parity-scale-codec 2.3.1",
 "typenum",
]

[[package]]
name = "substrate-frame-rpc-system"
version = "4.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "frame-system-rpc-runtime-api",
 "futures",
 "jsonrpsee",
 "log",
 "parity-scale-codec 3.6.9",
 "sc-rpc-api",
 "sc-transaction-pool-api",
 "sp-api",
 "sp-block-builder",
 "sp-blockchain",
 "sp-core",
 "sp-runtime",
]

[[package]]
name = "substrate-prometheus-endpoint"
version = "0.10.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "hyper",
 "log",
 "prometheus",
 "thiserror",
 "tokio",
]

[[package]]
name = "substrate-rpc-client"
version = "0.10.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "async-trait",
 "jsonrpsee",
 "log",
 "sc-rpc-api",
 "serde",
 "sp-runtime",
]

[[package]]
name = "substrate-stellar-sdk"
version = "0.2.4"
source = "git+https://github.com/pendulum-chain/substrate-stellar-sdk#740389c7ec47a8725c32d8a68061ffb1bdfd013b"
dependencies = [
 "base64 0.13.1",
 "hex",
 "lazy_static",
 "num-rational",
 "sha2 0.9.9",
 "sodalite",
 "sp-std 5.0.0",
]

[[package]]
name = "substrate-validator-set"
version = "2.9.3"
dependencies = [
 "frame-benchmarking",
 "frame-support",
 "frame-system",
 "log",
 "pallet-session",
 "parity-scale-codec 3.6.9",
 "scale-info",
 "serde",
 "sp-core",
 "sp-io",
 "sp-runtime",
 "sp-staking",
 "sp-state-machine",
 "sp-std 8.0.0",
 "sp-weights",
]

[[package]]
name = "substrate-wasm-builder"
version = "5.0.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "ansi_term",
 "build-helper",
 "cargo_metadata",
 "filetime",
 "parity-wasm",
 "sp-maybe-compressed-blob",
 "strum 0.24.1",
 "tempfile",
 "toml 0.7.8",
 "walkdir",
 "wasm-opt",
]

[[package]]
name = "subtle"
version = "2.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6bdef32e8150c2a081110b42772ffe7d7c9032b606bc226c8260fd97e0976601"

[[package]]
name = "syn"
version = "1.0.109"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "72b64191b275b66ffe2469e8af2c1cfe3bafa67b529ead792a6d0160888b4237"
dependencies = [
 "proc-macro2",
 "quote",
 "unicode-ident",
]

[[package]]
name = "syn"
version = "2.0.48"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0f3531638e407dfc0814761abb7c00a5b54992b849452a0646b7f65c9f770f3f"
dependencies = [
 "proc-macro2",
 "quote",
 "unicode-ident",
]

[[package]]
name = "synstructure"
version = "0.12.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f36bdaa60a83aca3921b5259d5400cbf5e90fc51931376a9bd4a0eb79aa7210f"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.109",
 "unicode-xid",
]

[[package]]
name = "system-configuration"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ba3a3adc5c275d719af8cb4272ea1c4a6d668a777f37e115f6d11ddbc1c8e0e7"
dependencies = [
 "bitflags 1.3.2",
 "core-foundation",
 "system-configuration-sys",
]

[[package]]
name = "system-configuration-sys"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a75fb188eb626b924683e3b95e3a48e63551fcfb51949de2f06a9d91dbee93c9"
dependencies = [
 "core-foundation-sys",
 "libc",
]

[[package]]
name = "tap"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "55937e1799185b12863d447f42597ed69d9928686b8d88a1df17376a097d8369"

[[package]]
name = "target-lexicon"
version = "0.12.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "69758bda2e78f098e4ccb393021a0963bb3442eac05f135c30f61b7370bbafae"

[[package]]
name = "tempfile"
version = "3.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "01ce4141aa927a6d1bd34a041795abd0db1cccba5d5f24b009f694bdf3a1f3fa"
dependencies = [
 "cfg-if",
 "fastrand",
 "redox_syscall 0.4.1",
 "rustix 0.38.30",
 "windows-sys 0.52.0",
]

[[package]]
name = "termcolor"
version = "1.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "06794f8f6c5c898b3275aebefa6b8a1cb24cd2c6c79397ab15774837a0bc5755"
dependencies = [
 "winapi-util",
]

[[package]]
name = "termtree"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3369f5ac52d5eb6ab48c6b4ffdc8efbcad6b89c765749064ba298f2c68a16a76"

[[package]]
name = "tfchain"
version = "2.9.3"
dependencies = [
 "clap",
 "frame-benchmarking",
 "frame-benchmarking-cli",
 "frame-system",
 "futures",
 "jsonrpsee",
 "pallet-transaction-payment",
 "pallet-transaction-payment-rpc",
 "sc-basic-authorship",
 "sc-cli",
 "sc-client-api",
 "sc-consensus",
 "sc-consensus-aura",
 "sc-consensus-grandpa",
 "sc-executor",
 "sc-keystore",
 "sc-network",
 "sc-offchain",
 "sc-rpc",
 "sc-rpc-api",
 "sc-service",
 "sc-telemetry",
 "sc-transaction-pool",
 "sc-transaction-pool-api",
 "serde_json",
 "sp-api",
 "sp-block-builder",
 "sp-blockchain",
 "sp-consensus",
 "sp-consensus-aura",
 "sp-core",
 "sp-inherents",
 "sp-io",
 "sp-keyring",
 "sp-runtime",
 "sp-timestamp",
 "substrate-build-script-utils",
 "substrate-frame-rpc-system",
 "tfchain-runtime",
 "try-runtime-cli",
]

[[package]]
name = "tfchain-runtime"
version = "2.9.3"
dependencies = [
 "frame-benchmarking",
 "frame-executive",
 "frame-support",
 "frame-system",
 "frame-system-benchmarking",
 "frame-system-rpc-runtime-api",
 "frame-try-runtime",
 "hex-literal",
 "log",
 "pallet-aura",
 "pallet-authorship",
 "pallet-balances",
 "pallet-burning",
 "pallet-collective",
 "pallet-dao",
 "pallet-grandpa",
 "pallet-kvstore",
 "pallet-membership",
 "pallet-runtime-upgrade",
 "pallet-scheduler",
 "pallet-session",
 "pallet-session-benchmarking",
 "pallet-smart-contract",
 "pallet-tfgrid",
 "pallet-tft-bridge",
 "pallet-tft-price",
 "pallet-timestamp",
 "pallet-transaction-payment",
 "pallet-transaction-payment-rpc-runtime-api",
 "pallet-utility",
 "pallet-validator",
 "parity-scale-codec 3.6.9",
 "scale-info",
 "smallvec",
 "sp-api",
 "sp-block-builder",
 "sp-consensus-aura",
 "sp-core",
 "sp-inherents",
 "sp-offchain",
 "sp-runtime",
 "sp-session",
 "sp-std 8.0.0",
 "sp-transaction-pool",
 "sp-version",
 "substrate-validator-set",
 "substrate-wasm-builder",
 "tfchain-support",
]

[[package]]
name = "tfchain-support"
version = "2.9.3"
dependencies = [
 "frame-support",
 "frame-system",
 "parity-scale-codec 3.6.9",
 "scale-info",
 "sp-runtime",
 "sp-std 8.0.0",
 "valip",
]

[[package]]
name = "thiserror"
version = "1.0.56"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d54378c645627613241d077a3a79db965db602882668f9136ac42af9ecb730ad"
dependencies = [
 "thiserror-impl",
]

[[package]]
name = "thiserror-impl"
version = "1.0.56"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fa0faa943b50f3db30a20aa7e265dbc66076993efed8463e8de414e5d06d3471"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.48",
]

[[package]]
name = "thousands"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3bf63baf9f5039dadc247375c29eb13706706cfde997d0330d05aa63a77d8820"

[[package]]
name = "thread_local"
version = "1.1.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3fdd6f064ccff2d6567adcb3873ca630700f00b5ad3f060c25b5dcfd9a4ce152"
dependencies = [
 "cfg-if",
 "once_cell",
]

[[package]]
name = "threadpool"
version = "1.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d050e60b33d41c19108b32cea32164033a9013fe3b46cbd4457559bfbf77afaa"
dependencies = [
 "num_cpus",
]

[[package]]
name = "tikv-jemalloc-sys"
version = "0.5.4****.0-patched"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9402443cb8fd499b6f327e40565234ff34dbda27460c5b47db0db77443dd85d1"
dependencies = [
 "cc",
 "libc",
]

[[package]]
name = "time"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f657ba42c3f86e7680e53c8cd3af8abbe56b5491790b46e22e19c0d57463583e"
dependencies = [
 "deranged",
 "itoa",
 "powerfmt",
 "serde",
 "time-core",
 "time-macros",
]

[[package]]
name = "time-core"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ef927ca75afb808a4d64dd374f00a2adf8d0fcff8e7b184af886c3c87ec4a3f3"

[[package]]
name = "time-macros"
version = "0.2.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "26197e33420244aeb70c3e8c78376ca46571bc4e701e4791c2cd9f57dcb3a43f"
dependencies = [
 "time-core",
]

[[package]]
name = "tiny-bip39"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "62cc94d358b5a1e84a5cb9109f559aa3c4d634d2b1b4de3d0fa4adc7c78e2861"
dependencies = [
 "anyhow",
 "hmac 0.12.1",
 "once_cell",
 "pbkdf2 0.11.0",
 "rand 0.8.5",
 "rustc-hash",
 "sha2 0.10.8",
 "thiserror",
 "unicode-normalization",
 "wasm-bindgen",
 "zeroize",
]

[[package]]
name = "tiny-keccak"
version = "2.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2c9d3793400a45f954c52e73d068316d76b6f4e36977e3fcebb13a2721e80237"
dependencies = [
 "crunchy",
]

[[package]]
name = "tinyvec"
version = "1.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "87cc5ceb3875bb20c2890005a4e226a4651264a5c75edb2421b52861a0a0cb50"
dependencies = [
 "tinyvec_macros",
]

[[package]]
name = "tinyvec_macros"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1f3ccbac311fea05f86f61904b462b55fb3df8837a366dfc601a0161d0532f20"

[[package]]
name = "tokio"
version = "1.35.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c89b4efa943be685f629b149f53829423f8f5531ea21249408e8e2f8671ec104"
dependencies = [
 "backtrace",
 "bytes",
 "libc",
 "mio",
 "num_cpus",
 "parking_lot 0.12.1",
 "pin-project-lite 0.2.13",
 "signal-hook-registry",
 "socket2 0.5.5",
 "tokio-macros",
 "windows-sys 0.48.0",
]

[[package]]
name = "tokio-macros"
version = "2.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5b8a1e28f2deaa14e508979454cb3a223b10b938b45af148bc0986de36f1923b"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.48",
]

[[package]]
name = "tokio-retry"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7f57eb36ecbe0fc510036adff84824dd3c24bb781e21bfa67b69d556aa85214f"
dependencies = [
 "pin-project",
 "rand 0.8.5",
 "tokio",
]

[[package]]
name = "tokio-rustls"
version = "0.24.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c28327cf380ac148141087fbfb9de9d7bd4e84ab5d2c28fbc911d753de8a7081"
dependencies = [
 "rustls 0.21.10",
 "tokio",
]

[[package]]
name = "tokio-stream"
version = "0.1.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "397c988d37662c7dda6d2208364a706264bf3d6138b11d436cbac0ad38832842"
dependencies = [
 "futures-core",
 "pin-project-lite 0.2.13",
 "tokio",
 "tokio-util",
]

[[package]]
name = "tokio-util"
version = "0.7.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5419f34732d9eb6ee4c3578b7989078579b7f039cbbb9ca2c4da015749371e15"
dependencies = [
 "bytes",
 "futures-core",
 "futures-io",
 "futures-sink",
 "pin-project-lite 0.2.13",
 "tokio",
 "tracing",
]

[[package]]
name = "toml"
version = "0.5.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f4f7f0dd8d50a853a531c426359045b1998f04219d88799810762cd4ad314234"
dependencies = [
 "serde",
]

[[package]]
name = "toml"
version = "0.7.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dd79e69d3b627db300ff956027cc6c3798cef26d22526befdfcd12feeb6d2257"
dependencies = [
 "serde",
 "serde_spanned",
 "toml_datetime",
 "toml_edit 0.19.15",
]

[[package]]
name = "toml"
version = "0.8.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "185d8ab0dfbb35cf1399a6344d8484209c088f75f8f68230da55d48d95d43e3d"
dependencies = [
 "serde",
 "serde_spanned",
 "toml_datetime",
 "toml_edit 0.20.2",
]

[[package]]
name = "toml_datetime"
version = "0.6.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7cda73e2f1397b1262d6dfdcef8aafae14d1de7748d66822d3bfeeb6d03e5e4b"
dependencies = [
 "serde",
]

[[package]]
name = "toml_edit"
version = "0.19.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1b5bb770da30e5cbfde35a2d7b9b8a2c4b8ef89548a7a6aeab5c9a576e3e7421"
dependencies = [
 "indexmap 2.1.0",
 "serde",
 "serde_spanned",
 "toml_datetime",
 "winnow",
]

[[package]]
name = "toml_edit"
version = "0.20.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "396e4d48bbb2b7554c944bde63101b5ae446cff6ec4a24227428f15eb72ef338"
dependencies = [
 "indexmap 2.1.0",
 "serde",
 "serde_spanned",
 "toml_datetime",
 "winnow",
]

[[package]]
name = "tower"
version = "0.4.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b8fa9be0de6cf49e536ce1851f987bd21a43b771b09473c3549a6c853db37c1c"
dependencies = [
 "tower-layer",
 "tower-service",
 "tracing",
]

[[package]]
name = "tower-http"
version = "0.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "61c5bb1d698276a2443e5ecfabc1008bf15a36c12e6a7176e7bf089ea9131140"
dependencies = [
 "bitflags 2.4.2",
 "bytes",
 "futures-core",
 "futures-util",
 "http",
 "http-body",
 "http-range-header",
 "pin-project-lite 0.2.13",
 "tower-layer",
 "tower-service",
]

[[package]]
name = "tower-layer"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c20c8dbed6283a09604c3e69b4b7eeb54e298b8a600d4d5ecb5ad39de609f1d0"

[[package]]
name = "tower-service"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b6bc1c9ce2b5135ac7f93c72918fc37feb872bdc6a5533a8b85eb4b86bfdae52"

[[package]]
name = "tracing"
version = "0.1.40"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c3523ab5a71916ccf420eebdf5521fcef02141234bbc0b8a49f2fdc4544364ef"
dependencies = [
 "log",
 "pin-project-lite 0.2.13",
 "tracing-attributes",
 "tracing-core",
]

[[package]]
name = "tracing-attributes"
version = "0.1.27"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "34704c8d6ebcbc939824180af020566b01a7c01f80641264eba0999f6c2b6be7"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.48",
]

[[package]]
name = "tracing-core"
version = "0.1.32"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c06d3da6113f116aaee68e4d601191614c9053067f9ab7f6edbcb161237daa54"
dependencies = [
 "once_cell",
 "valuable",
]

[[package]]
name = "tracing-futures"
version = "0.2.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "97d095ae15e245a057c8e8451bab9b3ee1e1f68e9ba2b4fbc18d0ac5237835f2"
dependencies = [
 "pin-project",
 "tracing",
]

[[package]]
name = "tracing-log"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f751112709b4e791d8ce53e32c4ed2d353565a795ce84da2285393f41557bdf2"
dependencies = [
 "log",
 "once_cell",
 "tracing-core",
]

[[package]]
name = "tracing-serde"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bc6b213177105856957181934e4920de57730fc69bf42c37ee5bb664d406d9e1"
dependencies = [
 "serde",
 "tracing-core",
]

[[package]]
name = "tracing-subscriber"
version = "0.2.25"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0e0d2eaa99c3c2e41547cfa109e910a68ea03823cccad4a0525dcbc9b01e8c71"
dependencies = [
 "ansi_term",
 "chrono",
 "lazy_static",
 "matchers",
 "parking_lot 0.11.2",
 "regex",
 "serde",
 "serde_json",
 "sharded-slab",
 "smallvec",
 "thread_local",
 "tracing",
 "tracing-core",
 "tracing-log",
 "tracing-serde",
]

[[package]]
name = "trie-db"
version = "0.27.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "767abe6ffed88a1889671a102c2861ae742726f52e0a5a425b92c9fbfa7e9c85"
dependencies = [
 "hash-db",
 "hashbrown 0.13.2",
 "log",
 "rustc-hex",
 "smallvec",
]

[[package]]
name = "trie-root"
version = "0.18.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d4ed310ef5ab98f5fa467900ed906cb9232dd5376597e00fd4cba2a449d06c0b"
dependencies = [
 "hash-db",
]

[[package]]
name = "trust-dns-proto"
version = "0.22.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4f7f83d1e4a0e4358ac54c5c3681e5d7da5efc5a7a632c90bb6d6669ddd9bc26"
dependencies = [
 "async-trait",
 "cfg-if",
 "data-encoding",
 "enum-as-inner",
 "futures-channel",
 "futures-io",
 "futures-util",
 "idna 0.2.3",
 "ipnet",
 "lazy_static",
 "rand 0.8.5",
 "smallvec",
 "socket2 0.4.10",
 "thiserror",
 "tinyvec",
 "tokio",
 "tracing",
 "url",
]

[[package]]
name = "trust-dns-resolver"
version = "0.22.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "aff21aa4dcefb0a1afbfac26deb0adc93888c7d295fb63ab273ef276ba2b7cfe"
dependencies = [
 "cfg-if",
 "futures-util",
 "ipconfig",
 "lazy_static",
 "lru-cache",
 "parking_lot 0.12.1",
 "resolv-conf",
 "smallvec",
 "thiserror",
 "tokio",
 "tracing",
 "trust-dns-proto",
]

[[package]]
name = "try-lock"
version = "0.2.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e421abadd41a4225275504ea4d6566923418b7f05506fbc9c0fe86ba7396114b"

[[package]]
name = "try-runtime-cli"
version = "0.10.0-dev"
source = "git+https://github.com/paritytech/polkadot-sdk?tag=polkadot-v1.1.0#f60318f68687e601c47de5ad5ca88e2c3f8139a7"
dependencies = [
 "async-trait",
 "clap",
 "frame-remote-externalities",
 "frame-try-runtime",
 "hex",
 "log",
 "parity-scale-codec 3.6.9",
 "sc-cli",
 "sc-executor",
 "serde",
 "serde_json",
 "sp-api",
 "sp-consensus-aura",
 "sp-consensus-babe",
 "sp-core",
 "sp-debug-derive",
 "sp-externalities",
 "sp-inherents",
 "sp-io",
 "sp-keystore",
 "sp-rpc",
 "sp-runtime",
 "sp-state-machine",
 "sp-timestamp",
 "sp-transaction-storage-proof",
 "sp-version",
 "sp-weights",
 "substrate-rpc-client",
 "zstd 0.12.4",
]

[[package]]
name = "tt-call"
version = "1.0.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f4f195fd851901624eee5a58c4bb2b4f06399148fcd0ed336e6f1cb60a9881df"

[[package]]
name = "twox-hash"
version = "1.6.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "97fee6b57c6a41524a810daee9286c02d7752c4253064d0b05472833a438f675"
dependencies = [
 "cfg-if",
 "digest 0.10.7",
 "rand 0.8.5",
 "static_assertions",
]

[[package]]
name = "typenum"
version = "1.17.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "42ff0bf0c66b8238c6f3b578df37d0b7848e55df8577b3f74f92a69acceeb825"

[[package]]
name = "ucd-trie"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ed646292ffc8188ef8ea4d1e0e0150fb15a5c2e12ad9b8fc191ae7a8a7f3c4b9"

[[package]]
name = "uint"
version = "0.9.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "76f64bba2c53b04fcab63c01a7d7427eadc821e3bc48c34dc9ba29c501164b52"
dependencies = [
 "byteorder",
 "crunchy",
 "hex",
 "static_assertions",
]

[[package]]
name = "unicode-bidi"
version = "0.3.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "08f95100a766bf4f8f28f90d77e0a5461bbdb219042e7679bebe79004fed8d75"

[[package]]
name = "unicode-ident"
version = "1.0.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3354b9ac3fae1ff6755cb6db53683adb661634f67557942dea4facebec0fee4b"

[[package]]
name = "unicode-normalization"
version = "0.1.22"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5c5713f0fc4b5db668a2ac63cdb7bb4469d8c9fed047b1d0292cc7b0ce2ba921"
dependencies = [
 "tinyvec",
]

[[package]]
name = "unicode-width"
version = "0.1.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e51733f11c9c4f72aa0c160008246859e340b00807569a0da0e7a1079b27ba85"

[[package]]
name = "unicode-xid"
version = "0.2.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f962df74c8c05a667b5ee8bcf162993134c104e96440b663c8daa176dc772d8c"

[[package]]
name = "universal-hash"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fc1de2c688dc15305988b563c3854064043356019f97a4b46276fe734c4f07ea"
dependencies = [
 "crypto-common",
 "subtle",
]

[[package]]
name = "unsigned-varint"
version = "0.7.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6889a77d49f1f013504cec6bf97a2c730394adedaeb1deb5ea08949a50541105"
dependencies = [
 "asynchronous-codec",
 "bytes",
 "futures-io",
 "futures-util",
]

[[package]]
name = "untrusted"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a156c684c91ea7d62626509bce3cb4e1d9ed5c4d978f7b4352658f96a4c26b4a"

[[package]]
name = "untrusted"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8ecb6da28b8a351d773b68d5825ac39017e680750f980f3a1a85cd8dd28a47c1"

[[package]]
name = "url"
version = "2.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "31e6302e3bb753d46e83516cae55ae196fc0c309407cf11ab35cc51a4c2a4633"
dependencies = [
 "form_urlencoded",
 "idna 0.5.0",
 "percent-encoding",
]

[[package]]
name = "utf8parse"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "711b9620af191e0cdc7468a8d14e709c3dcdb115b36f838e601583af800a370a"

[[package]]
name = "valip"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dfbab1f81fcf258f4e5c7822b4b38425520b8ec5065863df813ac76ba040be81"

[[package]]
name = "valuable"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "830b7e5d4d90034032940e4ace0d9a9a057e7a45cd94e6c007832e39edb82f6d"

[[package]]
name = "vcpkg"
version = "0.2.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "accd4ea62f7bb7a82fe23066fb0957d48ef677f6eeb8215f372f52e48bb32426"

[[package]]
name = "version_check"
version = "0.9.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "49874b5167b65d7193b8aba1567f5c7d93d001cafc34600cee003eda787e483f"

[[package]]
name = "void"
version = "1.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6a02e4885ed3bc0f2de90ea6dd45ebcbb66dacffe03547fadbb0eeae2770887d"

[[package]]
name = "walkdir"
version = "2.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d71d857dc86794ca4c280d616f7da00d2dbfd8cd788846559a6813e6aa4b54ee"
dependencies = [
 "same-file",
 "winapi-util",
]

[[package]]
name = "want"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bfa7760aed19e106de2c7c0b581b509f2f25d3dacaf737cb82ac61bc6d760b0e"
dependencies = [
 "try-lock",
]

[[package]]
name = "wasi"
version = "0.9.0+wasi-snapshot-preview1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cccddf32554fecc6acb585f82a32a72e28b48f8c4c1883ddfeeeaa96f7d8e519"

[[package]]
name = "wasi"
version = "0.11.0+wasi-snapshot-preview1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9c8d87e72b64a3b4db28d11ce29237c246188f4f51057d65a7eab63b7987e423"

[[package]]
name = "wasm-bindgen"
version = "0.2.90"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b1223296a201415c7fad14792dbefaace9bd52b62d33453ade1c5b5f07555406"
dependencies = [
 "cfg-if",
 "wasm-bindgen-macro",
]

[[package]]
name = "wasm-bindgen-backend"
version = "0.2.90"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fcdc935b63408d58a32f8cc9738a0bffd8f05cc7c002086c6ef20b7312ad9dcd"
dependencies = [
 "bumpalo",
 "log",
 "once_cell",
 "proc-macro2",
 "quote",
 "syn 2.0.48",
 "wasm-bindgen-shared",
]

[[package]]
name = "wasm-bindgen-futures"
version = "0.4.40"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bde2032aeb86bdfaecc8b261eef3cba735cc426c1f3a3416d1e0791be95fc461"
dependencies = [
 "cfg-if",
 "js-sys",
 "wasm-bindgen",
 "web-sys",
]

[[package]]
name = "wasm-bindgen-macro"
version = "0.2.90"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3e4c238561b2d428924c49815533a8b9121c664599558a5d9ec51f8a1740a999"
dependencies = [
 "quote",
 "wasm-bindgen-macro-support",
]

[[package]]
name = "wasm-bindgen-macro-support"
version = "0.2.90"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bae1abb6806dc1ad9e560ed242107c0f6c84335f1749dd4e8ddb012ebd5e25a7"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.48",
 "wasm-bindgen-backend",
 "wasm-bindgen-shared",
]

[[package]]
name = "wasm-bindgen-shared"
version = "0.2.90"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4d91413b1c31d7539ba5ef2451af3f0b833a005eb27a631cec32bc0635a8602b"

[[package]]
name = "wasm-instrument"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "aa1dafb3e60065305741e83db35c6c2584bb3725b692b5b66148a38d72ace6cd"
dependencies = [
 "parity-wasm",
]

[[package]]
name = "wasm-opt"
version = "0.114.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "effbef3bd1dde18acb401f73e740a6f3d4a1bc651e9773bddc512fe4d8d68f67"
dependencies = [
 "anyhow",
 "libc",
 "strum 0.24.1",
 "strum_macros 0.24.3",
 "tempfile",
 "thiserror",
 "wasm-opt-cxx-sys",
 "wasm-opt-sys",
]

[[package]]
name = "wasm-opt-cxx-sys"
version = "0.114.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c09e24eb283919ace2ed5733bda4842a59ce4c8de110ef5c6d98859513d17047"
dependencies = [
 "anyhow",
 "cxx",
 "cxx-build",
 "wasm-opt-sys",
]

[[package]]
name = "wasm-opt-sys"
version = "0.114.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "36f2f817bed2e8d65eb779fa37317e74de15585751f903c9118342d1970703a4"
dependencies = [
 "anyhow",
 "cc",
 "cxx",
 "cxx-build",
]

[[package]]
name = "wasm-timer"
version = "0.2.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "be0ecb0db480561e9a7642b5d3e4187c128914e58aa84330b9493e3eb68c5e7f"
dependencies = [
 "futures",
 "js-sys",
 "parking_lot 0.11.2",
 "pin-utils",
 "wasm-bindgen",
 "wasm-bindgen-futures",
 "web-sys",
]

[[package]]
name = "wasmparser"
version = "0.102.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "48134de3d7598219ab9eaf6b91b15d8e50d31da76b8519fe4ecfcec2cf35104b"
dependencies = [
 "indexmap 1.9.3",
 "url",
]

[[package]]
name = "wasmtime"
version = "8.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f907fdead3153cb9bfb7a93bbd5b62629472dc06dee83605358c64c52ed3dda9"
dependencies = [
 "anyhow",
 "bincode",
 "cfg-if",
 "indexmap 1.9.3",
 "libc",
 "log",
 "object 0.30.4",
 "once_cell",
 "paste 1.0.14",
 "psm",
 "rayon",
 "serde",
 "target-lexicon",
 "wasmparser",
 "wasmtime-cache",
 "wasmtime-cranelift",
 "wasmtime-environ",
 "wasmtime-jit",
 "wasmtime-runtime",
 "windows-sys 0.45.0",
]

[[package]]
name = "wasmtime-asm-macros"
version = "8.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d3b9daa7c14cd4fa3edbf69de994408d5f4b7b0959ac13fa69d465f6597f810d"
dependencies = [
 "cfg-if",
]

[[package]]
name = "wasmtime-cache"
version = "8.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c86437fa68626fe896e5afc69234bb2b5894949083586535f200385adfd71213"
dependencies = [
 "anyhow",
 "base64 0.21.7",
 "bincode",
 "directories-next",
 "file-per-thread-logger",
 "log",
 "rustix 0.36.17",
 "serde",
 "sha2 0.10.8",
 "toml 0.5.11",
 "windows-sys 0.45.0",
 "zstd 0.11.2+zstd.1.5.2",
]

[[package]]
name = "wasmtime-cranelift"
version = "8.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b1cefde0cce8cb700b1b21b6298a3837dba46521affd7b8c38a9ee2c869eee04"
dependencies = [
 "anyhow",
 "cranelift-codegen",
 "cranelift-entity",
 "cranelift-frontend",
 "cranelift-native",
 "cranelift-wasm",
 "gimli 0.27.3",
 "log",
 "object 0.30.4",
 "target-lexicon",
 "thiserror",
 "wasmparser",
 "wasmtime-cranelift-shared",
 "wasmtime-environ",
]

[[package]]
name = "wasmtime-cranelift-shared"
version = "8.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cd041e382ef5aea1b9fc78442394f1a4f6d676ce457e7076ca4cb3f397882f8b"
dependencies = [
 "anyhow",
 "cranelift-codegen",
 "cranelift-native",
 "gimli 0.27.3",
 "object 0.30.4",
 "target-lexicon",
 "wasmtime-environ",
]

[[package]]
name = "wasmtime-environ"
version = "8.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a990198cee4197423045235bf89d3359e69bd2ea031005f4c2d901125955c949"
dependencies = [
 "anyhow",
 "cranelift-entity",
 "gimli 0.27.3",
 "indexmap 1.9.3",
 "log",
 "object 0.30.4",
 "serde",
 "target-lexicon",
 "thiserror",
 "wasmparser",
 "wasmtime-types",
]

[[package]]
name = "wasmtime-jit"
version = "8.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0de48df552cfca1c9b750002d3e07b45772dd033b0b206d5c0968496abf31244"
dependencies = [
 "addr2line 0.19.0",
 "anyhow",
 "bincode",
 "cfg-if",
 "cpp_demangle",
 "gimli 0.27.3",
 "log",
 "object 0.30.4",
 "rustc-demangle",
 "serde",
 "target-lexicon",
 "wasmtime-environ",
 "wasmtime-jit-debug",
 "wasmtime-jit-icache-coherence",
 "wasmtime-runtime",
 "windows-sys 0.45.0",
]

[[package]]
name = "wasmtime-jit-debug"
version = "8.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6e0554b84c15a27d76281d06838aed94e13a77d7bf604bbbaf548aa20eb93846"
dependencies = [
 "object 0.30.4",
 "once_cell",
 "rustix 0.36.17",
]

[[package]]
name = "wasmtime-jit-icache-coherence"
version = "8.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "aecae978b13f7f67efb23bd827373ace4578f2137ec110bbf6a4a7cde4121bbd"
dependencies = [
 "cfg-if",
 "libc",
 "windows-sys 0.45.0",
]

[[package]]
name = "wasmtime-runtime"
version = "8.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "658cf6f325232b6760e202e5255d823da5e348fdea827eff0a2a22319000b441"
dependencies = [
 "anyhow",
 "cc",
 "cfg-if",
 "indexmap 1.9.3",
 "libc",
 "log",
 "mach",
 "memfd",
 "memoffset",
 "paste 1.0.14",
 "rand 0.8.5",
 "rustix 0.36.17",
 "wasmtime-asm-macros",
 "wasmtime-environ",
 "wasmtime-jit-debug",
 "windows-sys 0.45.0",
]

[[package]]
name = "wasmtime-types"
version = "8.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a4f6fffd2a1011887d57f07654dd112791e872e3ff4a2e626aee8059ee17f06f"
dependencies = [
 "cranelift-entity",
 "serde",
 "thiserror",
 "wasmparser",
]

[[package]]
name = "web-sys"
version = "0.3.67"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "58cd2333b6e0be7a39605f0e255892fd7418a682d8da8fe042fe25128794d2ed"
dependencies = [
 "js-sys",
 "wasm-bindgen",
]

[[package]]
name = "webpki"
version = "0.22.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ed63aea5ce73d0ff405984102c42de94fc55a6b75765d621c65262469b3c9b53"
dependencies = [
 "ring 0.17.7",
 "untrusted 0.9.0",
]

[[package]]
name = "webpki-roots"
version = "0.22.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b6c71e40d7d2c34a5106301fb632274ca37242cd0c9d3e64dbece371a40a2d87"
dependencies = [
 "webpki",
]

[[package]]
name = "webpki-roots"
version = "0.25.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1778a42e8b3b90bff8d0f5032bf22250792889a5cdc752aa0020c84abe3aaf10"

[[package]]
name = "which"
version = "4.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "87ba24419a2078cd2b0f2ede2691b6c66d8e47836da3b6db8265ebad47afbfc7"
dependencies = [
 "either",
 "home",
 "once_cell",
 "rustix 0.38.30",
]

[[package]]
name = "wide"
version = "0.7.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c68938b57b33da363195412cfc5fc37c9ed49aa9cfe2156fde64b8d2c9498242"
dependencies = [
 "bytemuck",
 "safe_arch",
]

[[package]]
name = "widestring"
version = "1.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "653f141f39ec16bba3c5abe400a0c60da7468261cc2cbf36805022876bc721a8"

[[package]]
name = "winapi"
version = "0.3.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5c839a674fcd7a98952e593242ea400abe93992746761e38641405d28b00f419"
dependencies = [
 "winapi-i686-pc-windows-gnu",
 "winapi-x86_64-pc-windows-gnu",
]

[[package]]
name = "winapi-i686-pc-windows-gnu"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ac3b87c63620426dd9b991e5ce0329eff545bccbbb34f3be09ff6fb6ab51b7b6"

[[package]]
name = "winapi-util"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f29e6f9198ba0d26b4c9f07dbe6f9ed633e1f3d5b8b414090084349e46a52596"
dependencies = [
 "winapi",
]

[[package]]
name = "winapi-x86_64-pc-windows-gnu"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "712e227841d057c1ee1cd2fb22fa7e5a5461ae8e48fa2ca79ec42cfc1931183f"

[[package]]
name = "windows"
version = "0.51.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ca229916c5ee38c2f2bc1e9d8f04df975b4bd93f9955dc69fabb5d91270045c9"
dependencies = [
 "windows-core 0.51.1",
 "windows-targets 0.48.5",
]

[[package]]
name = "windows-core"
version = "0.51.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f1f8cf84f35d2db49a46868f947758c7a1138116f7fac3bc844f43ade1292e64"
dependencies = [
 "windows-targets 0.48.5",
]

[[package]]
name = "windows-core"
version = "0.52.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "33ab640c8d7e35bf8ba19b884ba838ceb4fba93a4e8c65a9059d08afcfc683d9"
dependencies = [
 "windows-targets 0.52.0",
]

[[package]]
name = "windows-sys"
version = "0.45.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "75283be5efb2831d37ea142365f009c02ec203cd29a3ebecbc093d52315b66d0"
dependencies = [
 "windows-targets 0.42.2",
]

[[package]]
name = "windows-sys"
version = "0.48.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "677d2418bec65e3338edb076e806bc1ec15693c5d0104683f2efe857f61056a9"
dependencies = [
 "windows-targets 0.48.5",
]

[[package]]
name = "windows-sys"
version = "0.52.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "282be5f36a8ce781fad8c8ae18fa3f9beff57ec1b52cb3de0789201425d9a33d"
dependencies = [
 "windows-targets 0.52.0",
]

[[package]]
name = "windows-targets"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8e5180c00cd44c9b1c88adb3693291f1cd93605ded80c250a75d472756b4d071"
dependencies = [
 "windows_aarch64_gnullvm 0.42.2",
 "windows_aarch64_msvc 0.42.2",
 "windows_i686_gnu 0.42.2",
 "windows_i686_msvc 0.42.2",
 "windows_x86_64_gnu 0.42.2",
 "windows_x86_64_gnullvm 0.42.2",
 "windows_x86_64_msvc 0.42.2",
]

[[package]]
name = "windows-targets"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9a2fa6e2155d7247be68c096456083145c183cbbbc2764150dda45a87197940c"
dependencies = [
 "windows_aarch64_gnullvm 0.48.5",
 "windows_aarch64_msvc 0.48.5",
 "windows_i686_gnu 0.48.5",
 "windows_i686_msvc 0.48.5",
 "windows_x86_64_gnu 0.48.5",
 "windows_x86_64_gnullvm 0.48.5",
 "windows_x86_64_msvc 0.48.5",
]

[[package]]
name = "windows-targets"
version = "0.52.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8a18201040b24831fbb9e4eb208f8892e1f50a37feb53cc7ff887feb8f50e7cd"
dependencies = [
 "windows_aarch64_gnullvm 0.52.0",
 "windows_aarch64_msvc 0.52.0",
 "windows_i686_gnu 0.52.0",
 "windows_i686_msvc 0.52.0",
 "windows_x86_64_gnu 0.52.0",
 "windows_x86_64_gnullvm 0.52.0",
 "windows_x86_64_msvc 0.52.0",
]

[[package]]
name = "windows_aarch64_gnullvm"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "597a5118570b68bc08d8d59125332c54f1ba9d9adeedeef5b99b02ba2b0698f8"

[[package]]
name = "windows_aarch64_gnullvm"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2b38e32f0abccf9987a4e3079dfb67dcd799fb61361e53e2882c3cbaf0d905d8"

[[package]]
name = "windows_aarch64_gnullvm"
version = "0.52.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cb7764e35d4db8a7921e09562a0304bf2f93e0a51bfccee0bd0bb0b666b015ea"

[[package]]
name = "windows_aarch64_msvc"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e08e8864a60f06ef0d0ff4ba04124db8b0fb3be5776a5cd47641e942e58c4d43"

[[package]]
name = "windows_aarch64_msvc"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dc35310971f3b2dbbf3f0690a219f40e2d9afcf64f9ab7cc1be722937c26b4bc"

[[package]]
name = "windows_aarch64_msvc"
version = "0.52.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bbaa0368d4f1d2aaefc55b6fcfee13f41544ddf36801e793edbbfd7d7df075ef"

[[package]]
name = "windows_i686_gnu"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c61d927d8da41da96a81f029489353e68739737d3beca43145c8afec9a31a84f"

[[package]]
name = "windows_i686_gnu"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a75915e7def60c94dcef72200b9a8e58e5091744960da64ec734a6c6e9b3743e"

[[package]]
name = "windows_i686_gnu"
version = "0.52.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a28637cb1fa3560a16915793afb20081aba2c92ee8af57b4d5f28e4b3e7df313"

[[package]]
name = "windows_i686_msvc"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "44d840b6ec649f480a41c8d80f9c65108b92d89345dd94027bfe06ac444d1060"

[[package]]
name = "windows_i686_msvc"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8f55c233f70c4b27f66c523580f78f1004e8b5a8b659e05a4eb49d4166cca406"

[[package]]
name = "windows_i686_msvc"
version = "0.52.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ffe5e8e31046ce6230cc7215707b816e339ff4d4d67c65dffa206fd0f7aa7b9a"

[[package]]
name = "windows_x86_64_gnu"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8de912b8b8feb55c064867cf047dda097f92d51efad5b491dfb98f6bbb70cb36"

[[package]]
name = "windows_x86_64_gnu"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "53d40abd2583d23e4718fddf1ebec84dbff8381c07cae67ff7768bbf19c6718e"

[[package]]
name = "windows_x86_64_gnu"
version = "0.52.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3d6fa32db2bc4a2f5abeacf2b69f7992cd09dca97498da74a151a3132c26befd"

[[package]]
name = "windows_x86_64_gnullvm"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "26d41b46a36d453748aedef1486d5c7a85db22e56aff34643984ea85514e94a3"

[[package]]
name = "windows_x86_64_gnullvm"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b7b52767868a23d5bab768e390dc5f5c55825b6d30b86c844ff2dc7414044cc"

[[package]]
name = "windows_x86_64_gnullvm"
version = "0.52.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1a657e1e9d3f514745a572a6846d3c7aa7dbe1658c056ed9c3344c4109a6949e"

[[package]]
name = "windows_x86_64_msvc"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9aec5da331524158c6d1a4ac0ab1541149c0b9505fde06423b02f5ef0106b9f0"

[[package]]
name = "windows_x86_64_msvc"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ed94fce61571a4006852b7389a063ab983c02eb1bb37b47f8272ce92d06d9538"

[[package]]
name = "windows_x86_64_msvc"
version = "0.52.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dff9641d1cd4be8d1a070daf9e3773c5f67e78b4d9d42263020c057706765c04"

[[package]]
name = "winnow"
version = "0.5.34"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b7cf47b659b318dccbd69cc4797a39ae128f533dce7902a1096044d1967b9c16"
dependencies = [
 "memchr",
]

[[package]]
name = "winreg"
version = "0.50.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "524e57b2c537c0f9b1e69f1965311ec12182b4122e45035b1508cd24d2adadb1"
dependencies = [
 "cfg-if",
 "windows-sys 0.48.0",
]

[[package]]
name = "wyz"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "05f360fc0b24296329c78fda852a1e9ae82de9cf7b27dae4b7f62f118f77b9ed"
dependencies = [
 "tap",
]

[[package]]
name = "x25519-dalek"
version = "1.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5a0c105152107e3b96f6a00a65e86ce82d9b125230e1c4302940eca58ff71f4f"
dependencies = [
 "curve25519-dalek 3.2.0",
 "rand_core 0.5.1",
 "zeroize",
]

[[package]]
name = "x25519-dalek"
version = "2.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c7e468321c81fb07fa7f4c636c3972b9100f0346e5b6a9f2bd0603a52f7ed277"
dependencies = [
 "curve25519-dalek 4.1.1",
 "rand_core 0.6.4",
 "serde",
 "zeroize",
]

[[package]]
name = "x509-parser"
version = "0.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e0ecbeb7b67ce215e40e3cc7f2ff902f94a223acf44995934763467e7b1febc8"
dependencies = [
 "asn1-rs",
 "base64 0.13.1",
 "data-encoding",
 "der-parser",
 "lazy_static",
 "nom",
 "oid-registry",
 "rusticata-macros",
 "thiserror",
 "time",
]

[[package]]
name = "yamux"
version = "0.10.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e5d9ba232399af1783a58d8eb26f6b5006fbefe2dc9ef36bd283324792d03ea5"
dependencies = [
 "futures",
 "log",
 "nohash-hasher",
 "parking_lot 0.12.1",
 "rand 0.8.5",
 "static_assertions",
]

[[package]]
name = "yasna"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e17bb3549cc1321ae1296b9cdc2698e2b6cb1992adfa19a8c72e5b7a738f44cd"
dependencies = [
 "time",
]

[[package]]
name = "zerocopy"
version = "0.7.32"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "74d4d3961e53fa4c9a25a8637fc2bfaf2595b3d3ae34875568a5cf64787716be"
dependencies = [
 "zerocopy-derive",
]

[[package]]
name = "zerocopy-derive"
version = "0.7.32"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9ce1b18ccd8e73a9321186f97e46f9f04b778851177567b1975109d26a08d2a6"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.48",
]

[[package]]
name = "zeroize"
version = "1.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "525b4ec142c6b68a2d10f01f7bbf6755599ca3f81ea53b8431b7dd348f5fdb2d"
dependencies = [
 "zeroize_derive",
]

[[package]]
name = "zeroize_derive"
version = "1.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ce36e65b0d2999d2aafac989fb249189a141aee1f53c612c1f37d72631959f69"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.48",
]

[[package]]
name = "zstd"
version = "0.11.2+zstd.1.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "20cc960326ece64f010d2d2107537f26dc589a6573a316bd5b1dba685fa5fde4"
dependencies = [
 "zstd-safe 5.0.2+zstd.1.5.2",
]

[[package]]
name = "zstd"
version = "0.12.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1a27595e173641171fc74a1232b7b1c7a7cb6e18222c11e9dfb9888fa424c53c"
dependencies = [
 "zstd-safe 6.0.6",
]

[[package]]
name = "zstd-safe"
version = "5.0.2+zstd.1.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d2a5585e04f9eea4b2a3d1eca508c4dee9592a89ef6f450c11719da0726f4db"
dependencies = [
 "libc",
 "zstd-sys",
]

[[package]]
name = "zstd-safe"
version = "6.0.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ee98ffd0b48ee95e6c5168188e44a54550b1564d9d530ee21d5f0eaed1069581"
dependencies = [
 "libc",
 "zstd-sys",
]

[[package]]
name = "zstd-sys"
version = "2.0.9+zstd.1.5.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9e16efa8a874a0481a574084d34cc26fdb3b99627480f785888deb6386506656"
dependencies = [
 "cc",
 "pkg-config",
]
