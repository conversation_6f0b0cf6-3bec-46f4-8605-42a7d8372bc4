[workspace.package]
authors = ["<EMAIL>"]
documentation = "https://manual.grid.tf"
edition = "2021"
homepage = "https://threefold.io/"
license-file = "LICENSE"
readme = "README.md"
repository = "https://github.com/threefoldtech/tfchain3"
version = "2.9.3"

[workspace]
members = [
    'node',
    'runtime',
    'support',
    'pallets/*',
]
resolver = "2"

[workspace.dependencies]
# Build deps

# External (without extra features and with default disabled if necessary)
base58 = {version = "0.2.0", default-features = false}
bitflags = {version = "1.3.2", default-features = false}
clap = "4.0.9"
parity-scale-codec = { version = "3.6.1", default-features = false }
env_logger = "0.10.0"
futures = {version = "0.3.21", default-features = false}
hex = {version = "0.4.0", default-features = false}
hex-literal = "0.3.4"
jsonrpsee = "0.16.2"
libsecp256k1 = {version = "0.7", default-features = false}
log = "0.4.17"
scale-info = {version = "2.5.0", default-features = false}
serde = { version = "1.0.144", default-features = false, features = ["derive"] }
serde_json = { version = "1.0", default-features = false, features = ["alloc"] }
sha3 = {version = "0.10.0", default-features = false}
smallvec = "1.8.0"
valip = "0.4.0"
lite-json = { version = "0.1", default-features = false }
parking_lot = '0.11'

# Internal pallets
pallet-burning = { path = "pallets/pallet-burning", default-features = false }
pallet-dao = { path = "pallets/pallet-dao", default-features = false }
pallet-kvstore = { path = "pallets/pallet-kvstore", default-features = false }
pallet-smart-contract = { path = "pallets/pallet-smart-contract", default-features = false }
pallet-tfgrid = { path = "pallets/pallet-tfgrid", default-features = false }
pallet-tft-price = { path = "pallets/pallet-tft-price", default-features = false }
pallet-validator = { path = "pallets/pallet-validator", default-features = false }
substrate-validator-set = { path = "pallets/substrate-validator-set", default-features = false }
pallet-runtime-upgrade = { path = "pallets/pallet-runtime-upgrade", default-features = false }
pallet-tft-bridge = { path = "pallets/pallet-tft-bridge", default-features = false }

# Internal support 
tfchain-support = { path = "support", default-features = false }

# Runtimes
tfchain-runtime = { path = "runtime", default-features = false }

# Benchmarking (with default disabled)
frame-system-benchmarking = { git = "https://github.com/paritytech/polkadot-sdk",  default-features = false, tag = "polkadot-v1.1.0" }

# Substrate (with default disabled)
frame-benchmarking = { git = "https://github.com/paritytech/polkadot-sdk", default-features = false, tag = "polkadot-v1.1.0" }
frame-benchmarking-cli = { git = "https://github.com/paritytech/polkadot-sdk", default-features = false, tag = "polkadot-v1.1.0" }
frame-executive = { git = "https://github.com/paritytech/polkadot-sdk", default-features = false, tag = "polkadot-v1.1.0" }
frame-support = { git = "https://github.com/paritytech/polkadot-sdk", default-features = false, tag = "polkadot-v1.1.0" }
frame-system = { git = "https://github.com/paritytech/polkadot-sdk", default-features = false, tag = "polkadot-v1.1.0" }
frame-system-rpc-runtime-api = { git = "https://github.com/paritytech/polkadot-sdk", default-features = false, tag = "polkadot-v1.1.0" }
frame-try-runtime = { git = "https://github.com/paritytech/polkadot-sdk", default-features = false, tag = "polkadot-v1.1.0" }
pallet-aura = { git = "https://github.com/paritytech/polkadot-sdk", default-features = false, tag = "polkadot-v1.1.0" }
pallet-authorship = { git = "https://github.com/paritytech/polkadot-sdk", default-features = false, tag = "polkadot-v1.1.0" }
pallet-balances = { git = "https://github.com/paritytech/polkadot-sdk", default-features = false, tag = "polkadot-v1.1.0" }
pallet-collective = { git = "https://github.com/paritytech/polkadot-sdk", default-features = false, tag = "polkadot-v1.1.0" }
pallet-grandpa = { git = "https://github.com/paritytech/polkadot-sdk", default-features = false, tag = "polkadot-v1.1.0" }
pallet-membership = { git = "https://github.com/paritytech/polkadot-sdk", default-features = false, tag = "polkadot-v1.1.0" }
pallet-scheduler = { git = "https://github.com/paritytech/polkadot-sdk", default-features = false, tag = "polkadot-v1.1.0" }
pallet-session = { git = "https://github.com/paritytech/polkadot-sdk", default-features = false, tag = "polkadot-v1.1.0" }
pallet-session-benchmarking = { git = "https://github.com/paritytech/polkadot-sdk", default-features = false, tag = "polkadot-v1.1.0" }
pallet-timestamp = { git = "https://github.com/paritytech/polkadot-sdk", default-features = false, tag = "polkadot-v1.1.0" }
pallet-transaction-payment = { git = "https://github.com/paritytech/polkadot-sdk", default-features = false, tag = "polkadot-v1.1.0" }
pallet-transaction-payment-rpc = { git = "https://github.com/paritytech/polkadot-sdk", default-features = false, tag = "polkadot-v1.1.0" }
pallet-transaction-payment-rpc-runtime-api = { git = "https://github.com/paritytech/polkadot-sdk", default-features = false, tag = "polkadot-v1.1.0" }
pallet-utility = { git = "https://github.com/paritytech/polkadot-sdk", default-features = false, tag = "polkadot-v1.1.0" }
sp-api = { git = "https://github.com/paritytech/polkadot-sdk", default-features = false, tag = "polkadot-v1.1.0" }
sp-block-builder = { git = "https://github.com/paritytech/polkadot-sdk", default-features = false, tag = "polkadot-v1.1.0" }
sp-consensus-aura = { git = "https://github.com/paritytech/polkadot-sdk", default-features = false, tag = "polkadot-v1.1.0" }
sp-core = { git = "https://github.com/paritytech/polkadot-sdk", default-features = false, tag = "polkadot-v1.1.0" }
sp-inherents = { git = "https://github.com/paritytech/polkadot-sdk", default-features = false, tag = "polkadot-v1.1.0" }
sp-io = { git = "https://github.com/paritytech/polkadot-sdk", default-features = false, tag = "polkadot-v1.1.0" }
sp-offchain = { git = "https://github.com/paritytech/polkadot-sdk", default-features = false, tag = "polkadot-v1.1.0" }
sp-runtime = { git = "https://github.com/paritytech/polkadot-sdk", default-features = false, tag = "polkadot-v1.1.0" }
sp-session = { git = "https://github.com/paritytech/polkadot-sdk", default-features = false, tag = "polkadot-v1.1.0" }
sp-std = { git = "https://github.com/paritytech/polkadot-sdk", default-features = false, tag = "polkadot-v1.1.0" }
sp-transaction-pool = { git = "https://github.com/paritytech/polkadot-sdk", default-features = false, tag = "polkadot-v1.1.0" }
sp-version = { git = "https://github.com/paritytech/polkadot-sdk", default-features = false, tag = "polkadot-v1.1.0" }
sp-storage = { git = "https://github.com/paritytech/polkadot-sdk", default-features = false, tag = "polkadot-v1.1.0" }
try-runtime-cli = { git = "https://github.com/paritytech/polkadot-sdk", default-features = false, tag = "polkadot-v1.1.0" }
sp-keystore = { git = "https://github.com/paritytech/polkadot-sdk", default-features = false, tag = "polkadot-v1.1.0" }
sp-staking = { git = "https://github.com/paritytech/polkadot-sdk", default-features = false, tag = "polkadot-v1.1.0" }
sp-weights = { git = "https://github.com/paritytech/polkadot-sdk", default-features = false, tag = "polkadot-v1.1.0" }
sp-state-machine = { git = "https://github.com/paritytech/polkadot-sdk", default-features = false, tag = "polkadot-v1.1.0" }

# Client-only (with default enabled)
polkadot-cli = { git = "https://github.com/paritytech/polkadot-sdk",  tag = "polkadot-v1.1.0" }
polkadot-primitives = { git = "https://github.com/paritytech/polkadot-sdk",  tag = "polkadot-v1.1.0" }
polkadot-service = { git = "https://github.com/paritytech/polkadot-sdk",  tag = "polkadot-v1.1.0" }
sc-basic-authorship = { git = "https://github.com/paritytech/polkadot-sdk",  tag = "polkadot-v1.1.0" }
sc-chain-spec = { git = "https://github.com/paritytech/polkadot-sdk",  tag = "polkadot-v1.1.0" }
sc-cli = { git = "https://github.com/paritytech/polkadot-sdk",  tag = "polkadot-v1.1.0" }
sc-client-api = { git = "https://github.com/paritytech/polkadot-sdk",  tag = "polkadot-v1.1.0" }
sc-consensus = { git = "https://github.com/paritytech/polkadot-sdk",  tag = "polkadot-v1.1.0" }
sc-consensus-aura = { git = "https://github.com/paritytech/polkadot-sdk",  tag = "polkadot-v1.1.0" }
sc-executor = { git = "https://github.com/paritytech/polkadot-sdk",  tag = "polkadot-v1.1.0" }
sc-consensus-grandpa = { git = "https://github.com/paritytech/polkadot-sdk",  tag = "polkadot-v1.1.0" }
sc-keystore = { git = "https://github.com/paritytech/polkadot-sdk",  tag = "polkadot-v1.1.0" }
sc-network = { git = "https://github.com/paritytech/polkadot-sdk",  tag = "polkadot-v1.1.0" }
sc-rpc = { git = "https://github.com/paritytech/polkadot-sdk",  tag = "polkadot-v1.1.0" }
sc-rpc-api = { git = "https://github.com/paritytech/polkadot-sdk",  tag = "polkadot-v1.1.0" }
sc-service = { git = "https://github.com/paritytech/polkadot-sdk",  tag = "polkadot-v1.1.0" }
sc-sysinfo = { git = "https://github.com/paritytech/polkadot-sdk",  tag = "polkadot-v1.1.0" }
sc-telemetry = { git = "https://github.com/paritytech/polkadot-sdk",  tag = "polkadot-v1.1.0" }
sc-tracing = { git = "https://github.com/paritytech/polkadot-sdk",  tag = "polkadot-v1.1.0" }
sc-transaction-pool = { git = "https://github.com/paritytech/polkadot-sdk",  tag = "polkadot-v1.1.0" }
sc-transaction-pool-api = { git = "https://github.com/paritytech/polkadot-sdk",  tag = "polkadot-v1.1.0" }
sc-offchain = { git = "https://github.com/paritytech/polkadot-sdk",  tag = "polkadot-v1.1.0" }
sp-blockchain = { git = "https://github.com/paritytech/polkadot-sdk",  tag = "polkadot-v1.1.0" }
sp-consensus = { git = "https://github.com/paritytech/polkadot-sdk",  tag = "polkadot-v1.1.0" }
sp-keyring = { git = "https://github.com/paritytech/polkadot-sdk",  tag = "polkadot-v1.1.0" }
sp-timestamp = { git = "https://github.com/paritytech/polkadot-sdk",  tag = "polkadot-v1.1.0" }
substrate-build-script-utils = { git = "https://github.com/paritytech/polkadot-sdk",  tag = "polkadot-v1.1.0" }
substrate-frame-rpc-system = { git = "https://github.com/paritytech/polkadot-sdk",  tag = "polkadot-v1.1.0" }
substrate-prometheus-endpoint = { git = "https://github.com/paritytech/polkadot-sdk",  tag = "polkadot-v1.1.0" }

[profile.production]
inherits = "release"

# Sacrifice compile speed for execution speed by using optimization flags:

# https://doc.rust-lang.org/rustc/linker-plugin-lto.html
lto = "fat"
# https://doc.rust-lang.org/rustc/codegen-options/index.html#codegen-units
codegen-units = 1