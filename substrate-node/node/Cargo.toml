[package]
authors.workspace = true
documentation.workspace = true
edition.workspace = true
homepage.workspace = true
license-file.workspace = true
readme.workspace = true
repository.workspace = true
version.workspace = true
name = "tfchain"
description = "Client for tfchain node"
build = "build.rs"

[package.metadata.docs.rs]
targets = ["x86_64-unknown-linux-gnu"]

[[bin]]
name = "tfchain"

[build-dependencies]
substrate-build-script-utils.workspace = true

[dependencies]
clap.workspace = true
futures = { workspace = true, features = ["thread-pool"]}
serde_json.workspace = true

sc-cli.workspace = true
sp-core.workspace = true
sc-executor.workspace = true
sc-network.workspace = true
sc-service.workspace = true
sc-telemetry.workspace = true
sc-keystore.workspace = true
sc-transaction-pool.workspace = true
sc-transaction-pool-api.workspace = true
sc-offchain.workspace = true
sc-consensus-aura.workspace = true
sp-consensus-aura.workspace = true
sp-consensus.workspace = true
sc-consensus.workspace = true
sc-consensus-grandpa.workspace = true
sc-client-api.workspace = true
sp-runtime.workspace = true
sp-io.workspace = true
sp-timestamp.workspace = true
sp-inherents.workspace = true
sp-keyring.workspace = true
frame-system.workspace = true
pallet-transaction-payment.workspace = true

# These dependencies are used for the node template's RPCs
jsonrpsee = {workspace = true, features = ["server"]}
sc-rpc.workspace = true
sp-api.workspace = true
sc-rpc-api.workspace = true
sp-blockchain.workspace = true
sp-block-builder.workspace = true
sc-basic-authorship.workspace = true
substrate-frame-rpc-system.workspace = true
pallet-transaction-payment-rpc.workspace = true

# These dependencies are used for runtime benchmarking
frame-benchmarking.workspace = true
frame-benchmarking-cli.workspace = true

# Local Dependencies
tfchain-runtime.workspace = true

# CLI-specific dependencies
try-runtime-cli = {workspace = true, optional = true}

[features]
default = []
# Dependencies that are only required if runtime benchmarking should be build.
runtime-benchmarks = [
	"tfchain-runtime/runtime-benchmarks",
	"frame-benchmarking/runtime-benchmarks",
	"frame-benchmarking-cli/runtime-benchmarks",
]
# Enable features that allow the runtime to be tried and debugged. Name might be subject to change
# in the near future.
try-runtime = ["tfchain-runtime/try-runtime", "try-runtime-cli/try-runtime"]