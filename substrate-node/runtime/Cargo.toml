[package]
authors.workspace = true
description = "Tfchain runtime"
documentation.workspace = true
edition.workspace = true
homepage.workspace = true
license-file.workspace = true
name = "tfchain-runtime"
readme.workspace = true
repository.workspace = true
version.workspace = true

[package.metadata.docs.rs]
targets = ["x86_64-unknown-linux-gnu"]

[build-dependencies]
substrate-wasm-builder = { git = "https://github.com/paritytech/polkadot-sdk",  tag = "polkadot-v1.1.0" }

[dependencies]
smallvec.workspace = true
parity-scale-codec = {workspace = true, features = ["derive"]}
log.workspace = true
scale-info = {workspace = true, features = ["derive"]}

frame-support.workspace = true
frame-system.workspace = true
frame-try-runtime = {workspace = true, optional = true}
frame-executive.workspace = true

# custom pallets
pallet-burning.workspace = true
pallet-dao.workspace = true
pallet-kvstore.workspace = true
pallet-smart-contract.workspace = true
pallet-tfgrid.workspace = true
pallet-tft-price.workspace = true
pallet-validator.workspace = true
substrate-validator-set.workspace = true
pallet-runtime-upgrade.workspace = true
pallet-tft-bridge.workspace = true

# support
tfchain-support.workspace = true

pallet-aura.workspace = true
pallet-balances.workspace = true
pallet-grandpa.workspace = true
pallet-timestamp.workspace = true
pallet-transaction-payment.workspace = true
pallet-session = {workspace = true, features = ["historical"]}
pallet-session-benchmarking.workspace = true
pallet-utility.workspace = true
pallet-collective.workspace = true
pallet-membership.workspace = true
pallet-authorship.workspace = true
pallet-scheduler.workspace = true

sp-api.workspace = true
sp-block-builder.workspace = true
sp-consensus-aura.workspace = true
sp-core.workspace = true
sp-inherents.workspace = true
sp-offchain.workspace = true
sp-runtime.workspace = true
sp-session.workspace = true
sp-std.workspace = true
sp-transaction-pool.workspace = true
sp-version.workspace = true

# Used for the node template's RPCs
frame-system-rpc-runtime-api.workspace = true
pallet-transaction-payment-rpc-runtime-api.workspace = true

# Used for runtime benchmarking
frame-benchmarking = {workspace = true, optional = true}
frame-system-benchmarking = {workspace = true, optional = true}
hex-literal = { workspace = true, optional = true }

[features]
default = ["std"]
std = [
	"frame-try-runtime?/std",
	"frame-system-benchmarking?/std",
	"frame-benchmarking?/std",
	"parity-scale-codec/std",
	"scale-info/std",
	"frame-executive/std",
	"frame-support/std",
	"frame-system-rpc-runtime-api/std",
	"frame-system/std",
	"frame-try-runtime/std",
	"pallet-aura/std",
	"pallet-balances/std",
	"pallet-grandpa/std",
	"pallet-timestamp/std",
	"pallet-transaction-payment-rpc-runtime-api/std",
	"pallet-transaction-payment/std",
	"sp-api/std",
	"sp-block-builder/std",
	"sp-consensus-aura/std",
	"sp-core/std",
	"sp-inherents/std",
	"sp-offchain/std",
	"sp-runtime/std",
	"sp-session/std",
	"sp-std/std",
	"sp-transaction-pool/std",
	"sp-version/std",
	"substrate-validator-set/std",
	"pallet-tft-price/std",
    "pallet-smart-contract/std",
	"pallet-tft-bridge/std",
	"pallet-dao/std",
	"pallet-tfgrid/std",
	"pallet-kvstore/std",
	"pallet-session/std",
	"pallet-utility/std",
	"pallet-collective/std",
	"pallet-membership/std",
	"pallet-authorship/std",
	"log/std",
	"tfchain-support/std",
	"pallet-session-benchmarking/std",
	"pallet-scheduler/std",
]
runtime-benchmarks = [
    'hex-literal',  
    'frame-benchmarking/runtime-benchmarks',
    'frame-system-benchmarking/runtime-benchmarks',
    'frame-support/runtime-benchmarks',
    'frame-system/runtime-benchmarks',
    'pallet-timestamp/runtime-benchmarks',
    'sp-runtime/runtime-benchmarks',
    'pallet-balances/runtime-benchmarks',
    'pallet-collective/runtime-benchmarks',
    'pallet-tfgrid/runtime-benchmarks',
    'pallet-smart-contract/runtime-benchmarks',
    'pallet-tft-price/runtime-benchmarks',
    'pallet-burning/runtime-benchmarks',
    'pallet-dao/runtime-benchmarks',
    'pallet-kvstore/runtime-benchmarks',
    'substrate-validator-set/runtime-benchmarks',
    'pallet-validator/runtime-benchmarks',
    'pallet-tft-bridge/runtime-benchmarks',
]
try-runtime = [
	"frame-try-runtime/try-runtime",
	"frame-executive/try-runtime",
	"frame-system/try-runtime",
	"frame-support/try-runtime",
	"pallet-aura/try-runtime",
	"pallet-balances/try-runtime",
	"pallet-grandpa/try-runtime",
	"pallet-timestamp/try-runtime",
	"pallet-transaction-payment/try-runtime",
	"pallet-utility/try-runtime",
	"pallet-authorship/try-runtime",
	"pallet-session/try-runtime",
	"pallet-scheduler/try-runtime",
	"pallet-collective/try-runtime",
	"pallet-membership/try-runtime",
	"pallet-burning/try-runtime",
	"pallet-dao/try-runtime",
	"pallet-kvstore/try-runtime",
	"pallet-runtime-upgrade/try-runtime",
    "pallet-smart-contract/try-runtime",
	"pallet-tfgrid/try-runtime",
	"pallet-tft-bridge/try-runtime",
	"pallet-tft-price/try-runtime",
	"pallet-validator/try-runtime",
	"substrate-validator-set/try-runtime",
]