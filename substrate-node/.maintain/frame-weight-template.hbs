{{header}}
//! Autogenerated weights for {{pallet}}
//!
//! THIS FILE WAS AUTO-GENERATED USING THE SUBSTRATE BENCHMARK CLI VERSION {{version}}
//! DATE: {{date}}, STEPS: `{{cmd.steps}}`, REPEAT: `{{cmd.repeat}}`, LOW RANGE: `{{cmd.lowest_range_values}}`, HIGH RANGE: `{{cmd.highest_range_values}}`
//! WORST CASE MAP SIZE: `{{cmd.worst_case_map_values}}`
//! HOSTNAME: `{{hostname}}`, CPU: `{{cpuname}}`
//! EXECUTION: {{cmd.execution}}, WASM-EXECUTION: {{cmd.wasm_execution}}, CHAIN: {{cmd.chain}}, DB CACHE: {{cmd.db_cache}}

// Executed Command:
{{#each args as |arg|}}
// {{arg}}
{{/each}}

#![cfg_attr(rustfmt, rustfmt_skip)]
#![allow(unused_parens)]
#![allow(unused_imports)]
#![allow(missing_docs)]

use frame_support::{traits::Get, weights::{Weight, constants::RocksDbWeight}};
use core::marker::PhantomData;

/// Weight functions needed for {{pallet}}.
pub trait WeightInfo {
	{{#each benchmarks as |benchmark|}}
	fn {{benchmark.name~}}
	(
		{{~#each benchmark.components as |c| ~}}
		{{c.name}}: u32, {{/each~}}
	) -> Weight;
	{{/each}}
}

/// Weights for {{pallet}} using the Substrate node and recommended hardware.
pub struct SubstrateWeight<T>(PhantomData<T>);
{{#if (eq pallet "frame_system")}}
impl<T: crate::Config> WeightInfo for SubstrateWeight<T> {
{{else}}
impl<T: frame_system::Config> WeightInfo for SubstrateWeight<T> {
{{/if}}
	{{#each benchmarks as |benchmark|}}
	{{#each benchmark.comments as |comment|}}
	/// {{comment}}
	{{/each}}
	{{#each benchmark.component_ranges as |range|}}
	/// The range of component `{{range.name}}` is `[{{range.min}}, {{range.max}}]`.
	{{/each}}
	fn {{benchmark.name~}}
	(
		{{~#each benchmark.components as |c| ~}}
		{{~#if (not c.is_used)}}_{{/if}}{{c.name}}: u32, {{/each~}}
	) -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `{{benchmark.base_recorded_proof_size}}{{#each benchmark.component_recorded_proof_size as |cp|}} + {{cp.name}} * ({{cp.slope}} ±{{underscore cp.error}}){{/each}}`
		//  Estimated: `{{benchmark.base_calculated_proof_size}}{{#each benchmark.component_calculated_proof_size as |cp|}} + {{cp.name}} * ({{cp.slope}} ±{{underscore cp.error}}){{/each}}`
		// Minimum execution time: {{underscore benchmark.min_execution_time}}_000 picoseconds.
		Weight::from_parts({{underscore benchmark.base_weight}}, {{benchmark.base_calculated_proof_size}})
			{{#each benchmark.component_weight as |cw|}}
			// Standard Error: {{underscore cw.error}}
			.saturating_add(Weight::from_parts({{underscore cw.slope}}, 0).saturating_mul({{cw.name}}.into()))
			{{/each}}
			{{#if (ne benchmark.base_reads "0")}}
			.saturating_add(T::DbWeight::get().reads({{benchmark.base_reads}}_u64))
			{{/if}}
			{{#each benchmark.component_reads as |cr|}}
			.saturating_add(T::DbWeight::get().reads(({{cr.slope}}_u64).saturating_mul({{cr.name}}.into())))
			{{/each}}
			{{#if (ne benchmark.base_writes "0")}}
			.saturating_add(T::DbWeight::get().writes({{benchmark.base_writes}}_u64))
			{{/if}}
			{{#each benchmark.component_writes as |cw|}}
			.saturating_add(T::DbWeight::get().writes(({{cw.slope}}_u64).saturating_mul({{cw.name}}.into())))
			{{/each}}
			{{#each benchmark.component_calculated_proof_size as |cp|}}
			.saturating_add(Weight::from_parts(0, {{cp.slope}}).saturating_mul({{cp.name}}.into()))
			{{/each}}
	}
	{{/each}}
}

// For backwards compatibility and tests
impl WeightInfo for () {
	{{#each benchmarks as |benchmark|}}
	{{#each benchmark.comments as |comment|}}
	/// {{comment}}
	{{/each}}
	{{#each benchmark.component_ranges as |range|}}
	/// The range of component `{{range.name}}` is `[{{range.min}}, {{range.max}}]`.
	{{/each}}
	fn {{benchmark.name~}}
	(
		{{~#each benchmark.components as |c| ~}}
		{{~#if (not c.is_used)}}_{{/if}}{{c.name}}: u32, {{/each~}}
	) -> Weight {
		// Proof Size summary in bytes:
		//  Measured:  `{{benchmark.base_recorded_proof_size}}{{#each benchmark.component_recorded_proof_size as |cp|}} + {{cp.name}} * ({{cp.slope}} ±{{underscore cp.error}}){{/each}}`
		//  Estimated: `{{benchmark.base_calculated_proof_size}}{{#each benchmark.component_calculated_proof_size as |cp|}} + {{cp.name}} * ({{cp.slope}} ±{{underscore cp.error}}){{/each}}`
		// Minimum execution time: {{underscore benchmark.min_execution_time}}_000 picoseconds.
		Weight::from_parts({{underscore benchmark.base_weight}}, {{benchmark.base_calculated_proof_size}})
			{{#each benchmark.component_weight as |cw|}}
			// Standard Error: {{underscore cw.error}}
			.saturating_add(Weight::from_parts({{underscore cw.slope}}, 0).saturating_mul({{cw.name}}.into()))
			{{/each}}
			{{#if (ne benchmark.base_reads "0")}}
			.saturating_add(RocksDbWeight::get().reads({{benchmark.base_reads}}_u64))
			{{/if}}
			{{#each benchmark.component_reads as |cr|}}
			.saturating_add(RocksDbWeight::get().reads(({{cr.slope}}_u64).saturating_mul({{cr.name}}.into())))
			{{/each}}
			{{#if (ne benchmark.base_writes "0")}}
			.saturating_add(RocksDbWeight::get().writes({{benchmark.base_writes}}_u64))
			{{/if}}
			{{#each benchmark.component_writes as |cw|}}
			.saturating_add(RocksDbWeight::get().writes(({{cw.slope}}_u64).saturating_mul({{cw.name}}.into())))
			{{/each}}
			{{#each benchmark.component_calculated_proof_size as |cp|}}
			.saturating_add(Weight::from_parts(0, {{cp.slope}}).saturating_mul({{cp.name}}.into()))
			{{/each}}
	}
	{{/each}}
}
