# Default values for substrate-node.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

image:
  repository: ghcr.io/threefoldtech/tfchain
  pullPolicy: IfNotPresent
  tag: ''

imagePullSecrets: []
nameOverride: ''
fullnameOverride: ''

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ''

podAnnotations: {}

podSecurityContext:
  {}
  # fsGroup: 2000

securityContext:
  {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  port: 80

name: 'tfchainnode01'

port: 30333

rpc_port: 9944

is_validator: true

chainspec: '/etc/chainspecs/dev/chainSpecRaw.json'

rpc_max_connections: "1048576"

#rpc_methods: "Unsafe"

rpc_methods: 'safe'

# Archive will keep all history, otherwise only the last 256 blocks will be kept
#archive: true

# Telemetry dashboard url
# telemetry_url: ""

keys: []
# - name: aura
#   secret: "kkjghjfkkj kjhgkkhhgg"
# - name: grandpa
#   secret: "kkjghjfkkj kjhgkkhhgg"
# - name: node
#   secret: 1a...

# boot_node: "/ip4/***********/tcp/30333/p2p/****************************************************"

global:
  ingress:
    certresolver: le

ingress:
  enabled: false
  annotations:
  hosts:
    - host: dev.substrate01.threefold.io
      paths:
        - /
  tls: []

resources:
  {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

volume:
  size: 10Gi
  existingpersistentVolumeClaim: ''
  persistentVolume:
    create: true
    hostPath: '/chain01'

nodeSelector: {}

tolerations: []

affinity: {}

threefoldVdc:
  backup: ''
